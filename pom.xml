<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.ly.travel.car</groupId>
    <artifactId>shared-mobility-im-chat-service-parent</artifactId>
    <version>*******-RELEASE</version>

    <name>LY shared-mobility-im-chat-service parent</name>
    <url>http://www.ly.com</url>
    <description>LY shared-mobility-im-chat-service Application Parent</description>
    <packaging>pom</packaging>

    <properties>
        <sof.version>********</sof.version>
        <jdk.version>1.8</jdk.version>
        <spring.version>4.3.25.RELEASE</spring.version>
        <car.supplycrm.version>1.0.0</car.supplycrm.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>

        <jacoco.skip>true</jacoco.skip>
        <jacoco.path>${project.build.directory}/jacoco-ut.exec</jacoco.path>

        <sonar.core.codeCoveragePlugin>jacoco</sonar.core.codeCoveragePlugin>
        <sonar.dynamicAnalysis>reuseReports</sonar.dynamicAnalysis>
        <sonar.jacoco.itReportPath>target/jacoco-it.exec</sonar.jacoco.itReportPath>
        <sonar.jacoco.reportPath>target/jacoco-ut.exec</sonar.jacoco.reportPath>

        <cglib.version>2.2</cglib.version>
        <asm.version>3.1</asm.version>
        <aspectj.version>1.8.14</aspectj.version>
        <velocity.version>1.7</velocity.version>
        <velocity.tools.version>2.0</velocity.tools.version>
        <dbcp.version>1.4</dbcp.version>
        <ibatis.version>2.3.4.726</ibatis.version>
        <ibatis-spring.version>1.1.0</ibatis-spring.version>
        <mysql.version>5.1.38</mysql.version>
        <mariadb.version>1.3.7</mariadb.version>
        <jaxb.version>2.3.0</jaxb.version>
        <slf4j.version>1.7.30</slf4j.version>
        <log4j.version>1-LY-EMPTY</log4j.version>
        <log4j2.version>2.17.2</log4j2.version>
        <junit.version>4.12</junit.version>
        <mockito.version>1.9.5</mockito.version>
        <easymock.version>3.1</easymock.version>
        <powermock.version>1.5</powermock.version>
        <easymock.powermock.version>1.4.10</easymock.powermock.version>
        <dal-new.version>3.6.6</dal-new.version>
        <lombok.version>1.18.20</lombok.version>
        <mapstruct.version>1.4.1.4.LY.Final</mapstruct.version>
        <redisson.version>3.13.2</redisson.version>
        <netty.version>4.1.50.Final</netty.version>

        <!-- tcbase cache -->
        <tcbase-cache.version>3.6.8</tcbase-cache.version>
        <!-- toolkit -->
        <flight.toolkit.version>1.0.3.4.RELEASE</flight.toolkit.version>
        <!-- excel 导入 导出-->
        <easyexcel.version>3.2.1</easyexcel.version>

        <commons.lang3.version>3.11</commons.lang3.version>
        <jackson.databind.version>2.12.5</jackson.databind.version>
        <jackson.core.version>2.12.5</jackson.core.version>

        <!-- 供应链接入层服务 -->
        <mobility.supply.version>1.0.1.2024082901.RELEASE</mobility.supply.version>
        <supply.trade.version>1.0.0.57-SNAPSHOT</supply.trade.version>

        <!-- 订单服务 -->
        <order.service.version>1.0.0.0.RELEASE</order.service.version>
        <order.core.version>1.0.0.5.RELEASE</order.core.version>
        <trade.core.model.version>1.0.0.0.RELEASE</trade.core.model.version>
        <distribution.order.version>1.0.2.20.RELEASE</distribution.order.version>

        <mybayis.plus.version>3.5.1</mybayis.plus.version>

        <hutool.all.version>5.8.8</hutool.all.version>

        <car.support.version>1.2.0</car.support.version>
    </properties>

    <modules>
        <module>app/web</module>
        <module>app/test</module>
        <module>app/model</module>
        <module>app/facade</module>
        <module>app/facade-impl</module>
        <module>app/integration</module>
        <module>app/biz</module>
        <module>app/dal</module>
        <module>assembly</module>
        <module>webdocs</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <!-- project dependency -->
            <dependency>
                <groupId>com.ly.travel.car</groupId>
                <artifactId>shared-mobility-im-chat-service-web</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ly.travel.car</groupId>
                <artifactId>shared-mobility-im-chat-service-biz</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ly.travel.car</groupId>
                <artifactId>shared-mobility-im-chat-service-dal</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ly.travel.car</groupId>
                <artifactId>shared-mobility-im-chat-service-facade</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ly.travel.car</groupId>
                <artifactId>shared-mobility-im-chat-service-facade-impl</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ly.travel.car</groupId>
                <artifactId>shared-mobility-im-chat-service-integration</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ly.travel.car</groupId>
                <artifactId>shared-mobility-im-chat-service-model</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-common</artifactId>
                <version>4.1.50.Final</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-handler</artifactId>
                <version>4.1.50.Final</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-transport</artifactId>
                <version>4.1.50.Final</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>4.1.50.Final</version>
            </dependency>
            <dependency>
                <groupId>com.ly.travel.car</groupId>
                <artifactId>shared-mobility-im-chat-service-test</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ly.travel.car</groupId>
                <artifactId>shared-mobility-im-chat-service-assembly</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- SOF bom import -->
            <dependency>
                <groupId>com.ly.sof</groupId>
                <artifactId>sof-bom</artifactId>
                <version>${sof.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- BEGIN: Spring -->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-beans</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-core</artifactId>
                <version>${spring.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-tx</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-webmvc</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-aop</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context-support</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-jdbc</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-orm</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-aspects</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <!-- END: Spring -->

            <!-- Spring dependecy -->
            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjweaver</artifactId>
                <version>${aspectj.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjrt</artifactId>
                <version>${aspectj.version}</version>
            </dependency>
            <dependency>
                <groupId>cglib</groupId>
                <artifactId>cglib</artifactId>
                <version>${cglib.version}</version>
            </dependency>
            <dependency>
                <groupId>cglib</groupId>
                <artifactId>cglib-nodep</artifactId>
                <version>${cglib.version}</version>
            </dependency>
            <dependency>
                <groupId>asm</groupId>
                <artifactId>asm</artifactId>
                <version>${asm.version}</version>
            </dependency>

            <!-- ibatis -->
            <dependency>
                <groupId>com.ibatis</groupId>
                <artifactId>ibatis</artifactId>
                <version>${ibatis.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-2-spring</artifactId>
                <version>${ibatis-spring.version}</version>
            </dependency>

            <!-- tools -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity</artifactId>
                <version>${velocity.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-tools</artifactId>
                <version>${velocity.tools.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>4.2.0.Final</version>
            </dependency>
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>servlet-api</artifactId>
                <version>2.5</version>
            </dependency>
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>jstl</artifactId>
                <version>1.2</version>
            </dependency>
            <dependency>
                <groupId>commons-logging</groupId>
                <artifactId>commons-logging</artifactId>
                <version>1.1.1</version>
            </dependency>

            <dependency>
                <groupId>commons-dbcp</groupId>
                <artifactId>commons-dbcp</artifactId>
                <version>${dbcp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ly.flight.toolkit</groupId>
                <artifactId>object-diff</artifactId>
                <version>${flight.toolkit.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ly.flight.toolkit</groupId>
                <artifactId>deploy-static-resource</artifactId>
                <version>${flight.toolkit.version}</version>
            </dependency>
            <dependency>
                <artifactId>sof-batis-gen-dependency</artifactId>
                <groupId>com.ly.flight.toolkit</groupId>
                <version>${flight.toolkit.version}</version>
            </dependency>

            <!--jaxb-->
            <dependency>
                <groupId>javax.xml.bind</groupId>
                <artifactId>jaxb-api</artifactId>
                <version>${jaxb.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-core</artifactId>
                <version>${jaxb.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-impl</artifactId>
                <version>${jaxb.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.activation</groupId>
                <artifactId>activation</artifactId>
                <version>1.1.1</version>
            </dependency>

            <!-- JDBC Driver -->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mariadb.jdbc</groupId>
                <artifactId>mariadb-java-client</artifactId>
                <version>${mariadb.version}</version>
            </dependency>

            <!-- log dependecies -->
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-log4j12</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>log4j</groupId>
                <artifactId>log4j</artifactId>
                <version>${log4j.version}</version>
            </dependency>

            <!-- log4j2 -->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${log4j2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-slf4j-impl</artifactId>
                <version>${log4j2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>jcl-over-slf4j</artifactId>
                <version>${slf4j.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-web</artifactId>
                <version>${log4j2.version}</version>
                <scope>runtime</scope>
            </dependency>

            <!-- Test dependecies -->
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-all</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.easymock</groupId>
                <artifactId>easymock</artifactId>
                <version>${easymock.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-junit4</artifactId>
                <version>${powermock.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-api-easymock</artifactId>
                <version>${easymock.powermock.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-test</artifactId>
                <version>${spring.version}</version>
            </dependency>

            <!-- tcdbms -->
            <dependency>
                <groupId>com.ly.dal</groupId>
                <artifactId>dal-new</artifactId>
                <version>${dal-new.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ly.tcbase</groupId>
                <artifactId>cache</artifactId>
                <version>${tcbase-cache.version}</version>
            </dependency>

            <!-- excel -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons.lang3.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.databind.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson.core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ly.travel.shared.mobility.supply</groupId>
                <artifactId>mobility-supply-integration-facade</artifactId>
                <version>${mobility.supply.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ly.travel.shared.mobility.supply</groupId>
                <artifactId>shared-mobility-supply-trade-core-facade</artifactId>
                <version>${supply.trade.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ly.travel.shared.mobility.supply</groupId>
                <artifactId>crm-client</artifactId>
                <version>${car.supplycrm.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.ly.dsf</groupId>
                        <artifactId>dsf-tccommon</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.ly.travel.car</groupId>
                <artifactId>shared-mobility-order-service-facade</artifactId>
                <version>${order.service.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ly.travel.car.distribution</groupId>
                <artifactId>shared-mobility-distribution-order-facade</artifactId>
                <version>${distribution.order.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ly.travel.shared.mobility.supply</groupId>
                <artifactId>shared-mobility-supply-order-core-facade</artifactId>
                <version>${order.core.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ly.travel.car</groupId>
                <artifactId>shared-mobility-trade-core-model</artifactId>
                <version>${trade.core.model.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus</artifactId>
                <version>${mybayis.plus.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.all.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ly.car</groupId>
                <artifactId>car-support</artifactId>
                <version>${car.support.version}</version>
            </dependency>


            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>${netty.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-scm-plugin</artifactId>
                <configuration>
                    <connectionType>connection</connectionType>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
            </plugin>
        </plugins>

        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>2.4</version>
                    <!-- <configuration> <archive> <manifestFile>src/main/resources/META-INF/MANIFEST.MF</manifestFile> </archive> </configuration> -->
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                    <configuration>
                        <source>1.8</source>
                        <target>1.8</target>
                        <encoding>UTF-8</encoding>
                        <fork>true</fork>
                        <compilerVersion>${jdk.version}</compilerVersion>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${mapstruct.version}</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-assembly-plugin</artifactId>
                    <version>2.5.4</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>2.6</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>build-helper-maven-plugin</artifactId>
                    <version>1.9.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>2.7</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-scm-plugin</artifactId>
                    <version>1.8.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>2.2</version>
                    <executions>
                        <execution>
                            <id>attach-sources</id>
                            <goals>
                                <goal>jar</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>0.6.0.201210061924</version>
                    <configuration>
                        <skip>${jacoco.skip}</skip>
                        <destFile>${jacoco.path}</destFile>
                        <dataFile>${jacoco.path}</dataFile>
                        <sessionId>jacoco_coverage</sessionId>
                    </configuration>
                    <executions>
                        <execution>
                            <id>pre-test</id>
                            <phase>process-classes</phase>
                            <goals>
                                <goal>prepare-agent</goal>
                            </goals>
                            <configuration>
                                <propertyName>coverageAgent</propertyName>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-eclipse-plugin</artifactId>
                    <version>2.9</version>
                    <configuration>
                        <downloadSources>true</downloadSources>
                        <downloadJavadocs>false</downloadJavadocs>
                        <additionalConfig>
                            <file>
                                <name>.settings/org.eclipse.core.resources.prefs</name>
                                <content>
                                    <![CDATA[eclipse.preferences.version=1${line.separator}encoding/<project>=UTF-8${line.separator}]]>
                                </content>
                            </file>
                        </additionalConfig>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>versions-maven-plugin</artifactId>
                    <version>2.0</version>
                    <dependencies>
                        <dependency>
                            <groupId>org.apache.maven.doxia</groupId>
                            <artifactId>doxia-core</artifactId>
                            <version>1.2</version>
                        </dependency>
                        <dependency>
                            <groupId>org.apache.maven.doxia</groupId>
                            <artifactId>doxia-site-renderer</artifactId>
                            <version>1.2</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-war-plugin</artifactId>
                    <version>2.4</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <profiles>
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <repositories>
                <repository>
                    <id>17usoft</id>
                    <name>LY Share Repository</name>
                    <url>http://nexus.17usoft.com/repository/mvn-all/</url>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
            </repositories>
            <pluginRepositories>
                <pluginRepository>
                    <id>public</id>
                    <url>http://nexus.17usoft.com/repository/mvn-all/</url>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </pluginRepository>
            </pluginRepositories>
        </profile>

        <profile>
            <id>external</id>
            <repositories>
                <repository>
                    <id>oschina</id>
                    <name>OSChina Repository</name>
                    <url>http://maven.oschina.net/content/groups/public/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                </repository>
            </repositories>
            <pluginRepositories>
                <pluginRepository>
                    <id>oschina</id>
                    <name>OSChina Repository</name>
                    <url>http://maven.oschina.net/content/groups/public/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </pluginRepository>
            </pluginRepositories>
        </profile>
    </profiles>
    <distributionManagement>
        <snapshotRepository>
            <id>snapshots</id>
            <name>nexus-snapshots</name>
            <url>http://nexus.17usoft.com/repository/mvn-flight-snapshot/</url>
            <uniqueVersion>false</uniqueVersion>
        </snapshotRepository>
        <repository>
            <id>releases</id>
            <name>nexus-releases</name>
            <url>http://nexus.17usoft.com/repository/mvn-flight-release/</url>
        </repository>
    </distributionManagement>
</project>
