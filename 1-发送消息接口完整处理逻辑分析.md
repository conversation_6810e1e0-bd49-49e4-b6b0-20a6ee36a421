# 发送消息接口完整处理逻辑分析

## 1. 接口概述

### 1.1 接口基本信息
- **接口路径**: `POST /chat/sendMsg`
- **主要功能**: 乘客向司机发送消息
- **入口类**: `ChatFacadeImpl.sendMsg()`
- **核心处理类**: `ChatServiceImpl.sendMsg()`
- **消息处理器**: 根据消息类型动态选择Handler

### 1.2 请求处理主流程架构图

```mermaid
graph TB
    subgraph "客户端层"
        A[移动端/小程序]
    end
    
    subgraph "接入层"
        B[ChatFacade]
        C[SendMsgInvoker]
    end
    
    subgraph "业务逻辑层"
        D[ChatService]
        E[消息处理器选择]
        F[OtherMsgHandler]
        G[ReadMsgHandler]
        H[TipMsgHandler]
        I[InviteDriverMsgHandler]
    end
    
    subgraph "外部服务层"
        J[SendService]
        K[OrderService]
        L[RiskApi]
        M[PushApi]
    end
    
    subgraph "数据存储层"
        N[SfcImInfoDao]
        O[SfcImMsgRecordDao]
        P[Redis缓存]
    end
    
    subgraph "消息队列"
        Q[RocketMQ分发队列]
        R[RocketMQ聊天队列]
    end
    
    A --> B
    B --> C
    C --> D
    D --> E
    E --> F
    E --> G
    E --> H
    E --> I
    F --> J
    F --> K
    F --> L
    F --> M
    J --> N
    J --> O
    D --> Q
    F --> R
    G --> P
```

## 2. 详细处理流程分析

### 2.1 ChatService.sendMsg() 主流程

```mermaid
flowchart TD
    A[开始: sendMsg请求] --> B[记录请求日志]
    B --> C[投递消息到分发MQ]
    C --> D[重复消息校验]
    D --> E{是否重复消息?}
    E -->|是| F[抛出重复异常]
    E -->|否| G[获取订单详情]
    G --> H{订单是否存在?}
    H -->|否| I[抛出订单不存在异常]
    H -->|是| J[根据消息类型获取处理器]
    J --> K{处理器是否存在?}
    K -->|否| L[抛出未知消息类型异常]
    K -->|是| M[调用处理器handle方法]
    M --> N[处理器执行具体业务逻辑]
    N --> O[返回处理结果]
    
    F --> P[返回失败响应]
    I --> P
    L --> P
    O --> Q[返回成功响应]
    
    style C fill:#e1f5fe
    style M fill:#f3e5f5
    style N fill:#fff3e0
```

**核心代码逻辑**：
```java
@Override
public SendMsgRspDTO sendMsg(SendMsgReqDTO req) {
    LoggerUtils.info(log, "发送消息，req：{}", FastJsonUtils.toJSONString(req));
    try {
        //1. 投递消息MQ
        dispatchMsg(req);

        //2. 重复消息校验
        duplicateInformationValid(req);

        //3. 订单信息
        OrderDetailDTO orderDetail = orderService.getOrderDetail(req.getOrderId());
        if (Objects.isNull(orderDetail)) {
            throw new ValidateException(BIZ_ERROR_FACTORY.orderNotFoundError(req.getOrderId()));
        }

        //4. 根据消息类型获取对应的处理器
        MsgHandler msgHandler = msgHandlerMap.get(MsgTypeEnum.getHandlerName(req.getMsgType()));
        if (Objects.isNull(msgHandler)) {
            throw new ValidateException(BIZ_ERROR_FACTORY.unknownMsgTypeError(req.getMsgType()));
        }

        //5. 调用对应的处理器处理消息
        return msgHandler.handle(req, orderDetail);

    } catch (ValidateException validateException) {
        return SendMsgRspDTO.fail(req.getTraceId(), validateException.getErrorCode(), 
            validateException.getError().getMessage(), req.getMsgContent().getMsgId());
    } catch (Exception e) {
        LoggerUtils.warn(log, "乘客消息发送司机异常 req：{}", FastJsonUtils.toJSONString(req), e);
        return SendMsgRspDTO.fail(req.getTraceId(), ERROR_CODE, "消息发送失败", 
            req.getMsgContent().getMsgId());
    }
}
```

### 2.2 MQ消息分发处理

#### 2.2.1 分发MQ处理流程

```mermaid
sequenceDiagram
    participant Service as ChatService
    participant MQ as RocketMQ
    participant Async as 异步线程池
    participant Config as 配置中心
    
    Service->>Async: 异步执行dispatchMsg
    Async->>Async: 构建ChatMessageDispatchDTO
    Async->>Config: 获取超时配置
    Async->>MQ: 发送分发消息
    Note over Async,MQ: Topic: dispatchTopic<br/>Tag: im_chat_message_dispatch
    MQ-->>Async: 确认发送成功
    Async->>Service: 异步执行完成
```

**分发MQ核心代码**：
```java
private void dispatchMsg(SendMsgReqDTO req) {
    // 已读消息不发送分发MQ
    if (Objects.equals(MsgTypeEnum.READ_ACK.getMsgType(), req.getMsgType())){
        return;
    }
    
    CompletableFuture.runAsync(() -> {
        try {
            // 构建分发消息DTO
            ChatMessageDispatchDTO dispatchMsg = new ChatMessageDispatchDTO();
            dispatchMsg.setOrderId(req.getOrderId());
            dispatchMsg.setMsgId(req.getMsgId());
            dispatchMsg.setSupplierCode(req.getSupplierCode());
            dispatchMsg.setStatus(MsgDispatchStatusEnum.RECEIVE.getCode());
            dispatchMsg.setLatestTime(req.getMsgSendTime() != null ? 
                DateUtil.date2String(req.getMsgSendTime()) : DateUtil.date2String(new Date()));
            
            ArrayList<ChatMessageDispatchDTO> maps = new ArrayList<>();
            maps.add(dispatchMsg);
            
            LoggerUtils.info(log, "发送消息分发MQ：{}", FastJsonUtils.toJSONString(maps));
            
            // 发送到MQ
            defaultProducer.send(dispatchTopic, ChatMsgTagEnum.im_chat_message_dispatch.getName(),
                maps, Long.valueOf(ImConfigCenter.getConfigValue(ConfigCenterKeyEnum.im_dispatch_send_msg_timeout)), 
                0, new HashMap<>(), SerializeEnum.FASTJSON);
                
        } catch (Exception e) {
            LoggerUtils.error(log, "发送消息分发MQ异常: {}", req.getOrderId(), e);
            throw new CompletionException(e);
        }
    }, asyncConfig.mqSendExecutor());
}
```

#### 2.2.2 MQ消息结构

**ChatMessageDispatchDTO结构**：
```java
public class ChatMessageDispatchDTO implements Serializable {
    private String orderId;           // 订单编号
    private String msgId;             // 消息ID
    private String supplierCode;      // 供应商代码
    private String passengerOrderGuid; // 供应商订单号
    private String latestTime;        // 最新时间
    private Integer status;           // 投递状态(RECEIVE=2)
}
```

**MQ配置信息**：
- **Topic**: `${chat.dispatch.mq.topic}` (配置文件中定义)
- **Tag**: `im_chat_message_dispatch`
- **序列化方式**: FASTJSON
- **超时时间**: 从配置中心获取 `im_dispatch_send_msg_timeout`

### 2.3 重复消息校验

```mermaid
flowchart TD
    A[重复消息校验] --> B[根据sessionKey+msgId+发送者查询]
    B --> C[查询数据库中已成功发送的消息]
    C --> D{是否存在重复消息?}
    D -->|是| E[抛出重复消息异常]
    D -->|否| F[校验通过，继续处理]
    
    style E fill:#ffebee
    style F fill:#e8f5e8
```

**重复校验核心代码**：
```java
private void duplicateInformationValid(SendMsgReqDTO req) throws ValidateException {
    SfcImMsgRecord sfcImMsgRecord = sfcImMsgRecordDao.queryRepeatMsg(
        req.getSessionKey(), 
        req.getMsgId(), 
        MsgSenderEnum.PASSENGER.getCode(),
        MsgSendStatusEnum.SUCCESS.getCode()
    );
    
    if (Objects.nonNull(sfcImMsgRecord)) {
        throw new ValidateException(BIZ_ERROR_FACTORY.duplicateInformation(req.getMsgId()));
    }
}
```

### 2.4 消息处理器选择机制

#### 2.4.1 处理器映射关系

```mermaid
graph LR
    A[消息类型] --> B{MsgTypeEnum.getHandlerName}
    B --> C[OtherMsgHandler]
    B --> D[ReadMsgHandler]
    B --> E[TipMsgHandler]
    B --> F[InviteDriverMsgHandler]
    B --> G[InviteDriverTextMsgHandler]
    B --> H[InviteDriverSysMsgHandler]
    B --> I[DistributeOtherMsgHandler]
    B --> J[DistributeReadMsgHandler]
    
    C --> K[文本/图片/语音/定位<br/>快捷消息/系统消息等]
    D --> L[已读消息确认]
    E --> M[提示公告/夜间提示]
    F --> N[邀请车主消息]
    G --> O[邀请车主文本]
    H --> P[邀请车主系统提示]
    I --> Q[分销自定义文本]
    J --> R[分销已读消息]
```

**处理器初始化**：
```java
@PostConstruct
public void initMsgHandler() {
    // 从Spring容器中获取所有MsgHandler实现类
    Map<String, MsgHandler> beansMap = applicationContext.getBeansOfType(MsgHandler.class);
    // 使用不区分大小写的Map存储
    msgHandlerMap = new CaseInsensitiveMap<>(beansMap);
}
```

**处理器选择逻辑**：
```java
// 根据消息类型获取处理器名称
String handlerName = MsgTypeEnum.getHandlerName(req.getMsgType());
// 从处理器映射中获取具体处理器实例
MsgHandler msgHandler = msgHandlerMap.get(handlerName);
```

## 3. 数据流转分析

### 3.1 同步请求数据流

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Facade as ChatFacade
    participant Service as ChatService
    participant Handler as MsgHandler
    participant DAO as 数据访问层
    
    Client->>Facade: SendMsgReqDTO
    Facade->>Service: 转发请求
    Service->>Service: MQ分发(异步)
    Service->>Service: 重复校验
    Service->>Service: 获取订单详情
    Service->>Handler: 选择并调用处理器
    Handler->>DAO: 数据库操作
    DAO-->>Handler: 返回结果
    Handler-->>Service: 处理结果
    Service-->>Facade: SendMsgRspDTO
    Facade-->>Client: 最终响应
```

### 3.2 异步MQ数据流

```mermaid
sequenceDiagram
    participant Service as ChatService
    participant MQPool as MQ线程池
    participant MQ as RocketMQ
    participant Consumer as 消息消费者
    
    Service->>MQPool: 异步执行dispatchMsg
    MQPool->>MQPool: 构建ChatMessageDispatchDTO
    MQPool->>MQ: 发送分发消息
    Note over MQPool,MQ: 状态: RECEIVE
    MQ->>Consumer: 消息投递
    Consumer->>Consumer: 处理分发逻辑
    Consumer->>MQ: 发送状态更新消息
    Note over Consumer,MQ: 状态: SEND/PUSH
```

### 3.3 完整数据链路图

```mermaid
graph TB
    subgraph "请求入口"
        A[SendMsgReqDTO]
    end
    
    subgraph "同步处理链路"
        B[ChatService.sendMsg]
        C[重复校验]
        D[订单验证]
        E[处理器选择]
        F[Handler.handle]
    end
    
    subgraph "异步MQ链路"
        G[dispatchMsg异步执行]
        H[ChatMessageDispatchDTO]
        I[RocketMQ分发队列]
    end
    
    subgraph "数据存储"
        J[SfcImInfo会话表]
        K[SfcImMsgRecord消息表]
        L[Redis缓存]
    end
    
    subgraph "外部服务"
        M[供应商服务]
        N[推送服务]
        O[风控服务]
    end
    
    A --> B
    B --> C
    B --> G
    C --> D
    D --> E
    E --> F
    F --> J
    F --> K
    F --> L
    F --> M
    F --> N
    F --> O
    G --> H
    H --> I
    
    style G fill:#e1f5fe
    style I fill:#e1f5fe
    style F fill:#fff3e0
```

## 4. 关键业务规则

### 4.1 消息发送限制规则

1. **支付状态限制**: 未支付订单限制发送非快捷消息
2. **行程状态限制**: 未建立行程时限制消息发送
3. **司机未读限制**: 司机未读消息达到阈值时限制发送
4. **风控拦截**: 敏感词检测和风控规则拦截

### 4.2 会话管理规则

1. **会话创建**: 首次发送消息时自动创建会话
2. **会话更新**: 每次发送消息更新最新消息信息
3. **未读计数**: 实时维护未读消息数量

### 4.3 异常处理机制

1. **业务异常**: ValidateException，返回具体错误码和消息
2. **系统异常**: Exception，返回通用错误信息
3. **MQ异常**: 异步处理，不影响主流程响应
