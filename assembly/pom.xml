<?xml version="1.0" encoding="utf-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ly.travel.car</groupId>
        <artifactId>shared-mobility-im-chat-service-parent</artifactId>
        <version>*******-RELEASE</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>shared-mobility-im-chat-service-assembly</artifactId>
    <packaging>pom</packaging>
    
    <name>LY shared-mobility-im-chat-service-Assembly</name>
    <description>LY shared-mobility-im-chat-service Assembly</description>

    <properties>
        <assembly.name>shared-mobility-im-chat-service</assembly.name>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.ly.travel.car</groupId>
            <artifactId>shared-mobility-im-chat-service-web</artifactId>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>dev</id>
            <activation>
                <property>
                    <name>sof-env</name>
                    <value>dev</value>
                </property>
            </activation>
            <build>
                <filters>
                    <filter>../conf/filter/dubbo.properties.dev</filter>
                    <filter>../conf/filter/dsf_application.properties.dev</filter>
                    <filter>../conf/filter/tcbase.properties.dev</filter>
					<filter>../conf/filter/log4j2.properties.dev</filter>
                </filters>
            </build>
        </profile>
        <profile>
            <id>test</id>
            <activation>
                <property>
                    <name>sof-env</name>
                    <value>test</value>
                </property>
            </activation>
            <build>
                <filters>
                    <filter>../conf/filter/dubbo.properties.test</filter>
                    <filter>../conf/filter/dsf_application.properties.test</filter>
                    <filter>../conf/filter/tcbase.properties.test</filter>
					<filter>../conf/filter/log4j2.properties.test</filter>
                </filters>
            </build>
        </profile>
        <profile>
            <id>uat</id>
            <activation>
                <property>
                    <name>sof-env</name>
                    <value>uat</value>
                </property>
            </activation>
            <build>
                <filters>
                    <filter>../conf/filter/dubbo.properties.uat</filter>
                    <filter>../conf/filter/dsf_application.properties.uat</filter>
                    <filter>../conf/filter/tcbase.properties.uat</filter>
                    <filter>../conf/filter/log4j2.properties.uat</filter>
                </filters>
            </build>
        </profile>
        <profile>
            <id>stage</id>
            <activation>
                <property>
                    <name>sof-env</name>
                    <value>stage</value>
                </property>
            </activation>
            <build>
                <filters>
                    <filter>../conf/filter/dubbo.properties.stage</filter>
                    <filter>../conf/filter/dsf_application.properties.stage</filter>
                    <filter>../conf/filter/tcbase.properties.stage</filter>
					<filter>../conf/filter/log4j2.properties.stage</filter>
                </filters>
            </build>
        </profile>
        <profile>
            <id>stage_test2</id>
            <activation>
                <property>
                    <name>sof-env</name>
                    <value>stage_test2</value>
                </property>
            </activation>
            <build>
                <filters>
                    <filter>../conf/filter/dubbo.properties.stage_test2</filter>
                    <filter>../conf/filter/dsf_application.properties.stage_test2</filter>
                    <filter>../conf/filter/tcbase.properties.stage_test2</filter>
                    <filter>../conf/filter/log4j2.properties.stage_test2</filter>
                </filters>
            </build>
        </profile>
        <profile>
            <id>prod</id>
            <activation>
                <property>
                    <name>sof-env</name>
                    <value>prod</value>
                </property>
            </activation>
            <build>
                <filters>
                    <filter>../conf/filter/dubbo.properties.prod</filter>
                    <filter>../conf/filter/dsf_application.properties.prod</filter>
                    <filter>../conf/filter/tcbase.properties.prod</filter>
					<filter>../conf/filter/log4j2.properties.prod</filter>
                </filters>
            </build>
        </profile>
    </profiles>
    
    <build>
        <resources>
            <resource>
                <directory>../conf</directory>
                <targetPath>${basedir}/../target/${assembly.name}.sof</targetPath>
                <filtering>false</filtering>
                <excludes>
                    <exclude>**/filter/**</exclude>
                </excludes>
            </resource>

            <resource>
                <directory>../conf/config</directory>
                <targetPath>${basedir}/../target/${assembly.name}.sof/config</targetPath>
                <filtering>true</filtering>
                <includes>
                    <include>**/*.conf</include>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                </includes>
            </resource>

            <resource>
                <directory>../webdocs</directory>
                <targetPath>${basedir}/../target/${assembly.name}.sof/webdocs</targetPath>
                <filtering>false</filtering>
                <excludes>
                	<exclude>**/pom.xml</exclude>
                </excludes>
            </resource>
        </resources>

        <filters>
            <filter>../conf/filter/dubbo.properties.default</filter>
			<filter>../conf/filter/dsf_application.properties.default</filter>
			<filter>../conf/filter/tcbase.properties.default</filter>
			<filter>../conf/filter/log4j2.properties.default</filter>
        </filters>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-conf</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>resources</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.ly.flight.intl</groupId>
                <artifactId>remoteconfig-maven-plugin</artifactId>
                <!--注意使用最新版本-->
                <version>1.15</version>
                <configuration>
                    <!--项目天眼标识，请如实配置，必需配置项-->
                    <skyCode>TODO</skyCode>
                    <!--是否打开调试模式，会打印替换配置的日志，非必需配置-->
                    <debug>true</debug>
                    <!--如果项目的配置文件路径被自定义改过，则需要添加该配置，相对项目根目录路径，非必需配置-->
                    <!--<configDir>/config/xxx</configDir>-->
                </configuration>
                <executions>
                    <execution>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>remoteConfig</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-assembly-plugin</artifactId>
                <executions>
                    <execution>
                        <id>distribution-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>../target</outputDirectory>
                            <attach>false</attach>
                            <appendAssemblyId>false</appendAssemblyId>
                            <finalName>${assembly.name}</finalName>
                            <descriptors>
                                <descriptor>assembly.xml</descriptor>
                            </descriptors>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
