<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xmlns:sof="http://schema.ly.com/schema/sof"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
         http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
         http://schema.ly.com/schema/sof http://schema.ly.com/schema/sof.xsd
         http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd"
       default-autowire="byName">

    <context:component-scan base-package="com.ly.travel.car.im"/>

    <!-- 蜂巢服务元数据配置 -->
    <dubbo:registry id="tcdsfRegistry" address="${dubbo.service.registry.address}" protocol="tcdsf"/>
    <dubbo:protocol name="tcdsfrest" port="${dubbo.service.port}" server="${dubbo.service.deploy.container}"
                    extension="com.ly.sof.core.support.DsfJacksonConfig"/>
    <bean id="tcdsfGroup" class="com.alibaba.dubbo.registry.tcdsf.DsfServiceGroup">
        <constructor-arg name="registry" ref="tcdsfRegistry"/>
        <constructor-arg name="gsName" value="${dubbo.service.gsname}"/>
        <constructor-arg name="version" value="${dubbo.service.version}"/>
        <constructor-arg name="cLevel" value="1"/>
        <constructor-arg name="pLevel" value="5"/>
        <constructor-arg name="services">
            <set>
                <ref bean="chatFacadeService"/>
                <ref bean="supplierChatFacadeService"/>
                <ref bean="wxSubFacadeService"/>
                <ref bean="distributeChatFacadeService"/>
            </set>
        </constructor-arg>
    </bean>

    <!-- 蜂巢服务发布 -->
    <dubbo:service id="chatFacadeService"
                   ref="chatFacade"
                   interface="com.ly.travel.car.im.facade.ChatFacade"
                   protocol="tcdsfrest">
    </dubbo:service>

    <dubbo:service id="supplierChatFacadeService"
                   ref="supplierChatFacade"
                   interface="com.ly.travel.car.im.facade.SupplierChatFacade"
                   protocol="tcdsfrest">
    </dubbo:service>

    <dubbo:service id="wxSubFacadeService"
                   ref="wxSubFacade"
                   interface="com.ly.travel.car.im.facade.WxSubFacade"
                   protocol="tcdsfrest">
    </dubbo:service>

    <dubbo:service id="distributeChatFacadeService"
                   ref="distributeChatFacade"
                   interface="com.ly.travel.car.im.facade.DistributeChatFacade"
                   protocol="tcdsfrest">
    </dubbo:service>
</beans>
