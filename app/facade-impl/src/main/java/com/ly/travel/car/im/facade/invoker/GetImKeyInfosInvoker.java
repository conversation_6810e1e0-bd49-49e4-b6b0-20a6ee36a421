package com.ly.travel.car.im.facade.invoker;

import com.ly.sof.gateway.core.Proxy;
import com.ly.sof.gateway.core.ProxyInvoker;
import com.ly.sof.gateway.exception.ProxyException;
import com.ly.sof.gateway.model.GatewayContext;
import com.ly.sof.utils.log.LogContextUtils;
import com.ly.travel.car.im.biz.service.ChatService;
import com.ly.travel.car.im.facade.Services;
import com.ly.travel.car.im.facade.request.GetImKeyInfosReqDTO;
import com.ly.travel.car.im.facade.response.GetImKeyInfosRspDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Proxy(Services.GET_IM_KEY_INFOS_FACADE)
public class GetImKeyInfosInvoker implements ProxyInvoker<GetImKeyInfosReqDTO, GetImKeyInfosRspDTO> {
    @Resource
    private ChatService chatService;

    @Override
    public GetImKeyInfosRspDTO invoke(GatewayContext<GetImKeyInfosReqDTO, GetImKeyInfosRspDTO> context) throws ProxyException {
        LogContextUtils.setFilter1(context.getRequest().getTraceId());
        return chatService.getImKeyInfos(context.getRequest());
    }
}