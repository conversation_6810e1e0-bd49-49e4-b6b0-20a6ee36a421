package com.ly.travel.car.im.facade.invoker;

import com.ly.sof.gateway.core.Proxy;
import com.ly.sof.gateway.core.ProxyInvoker;
import com.ly.sof.gateway.exception.ProxyException;
import com.ly.sof.gateway.model.GatewayContext;
import com.ly.sof.utils.log.LogContextUtils;
import com.ly.travel.car.im.biz.service.ChatService;
import com.ly.travel.car.im.facade.Services;
import com.ly.travel.car.im.facade.request.QueryMsgRecordReqDTO;
import com.ly.travel.car.im.facade.response.QueryMsgRecordRspDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Proxy(Services.QUERY_INNER_MSG_RECORD_FACADE)
public class QueryInnerMsgRecordInvoker implements ProxyInvoker<QueryMsgRecordReqDTO, QueryMsgRecordRspDTO> {
    @Resource
    private ChatService chatService;

    @Override
    public QueryMsgRecordRspDTO invoke(GatewayContext<QueryMsgRecordReqDTO, QueryMsgRecordRspDTO> context) throws ProxyException {
        LogContextUtils.setFilter2(context.getRequest().getTraceId());
        LogContextUtils.setCategory("queryInnerMsgRecord");
        return chatService.queryInnerMsgRecord(context.getRequest());
    }
}