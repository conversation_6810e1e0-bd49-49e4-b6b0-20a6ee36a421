package com.ly.travel.car.im.facade;

/**
 * 服务Key
 *
 * <AUTHOR>
 * @version Id: Services, v 0.1 2024-05-27 15:45 lichunbo Exp $
 */
public interface Services {

    /**
     * 乘客消息发送司机
     */
    String SEND_MSG_FACADE = "SEND_MSG_FACADE";

    /**
     * 查询用户消息列表
     */
    String QUERY_MSG_FACADE = "QUERY_MSG_FACADE";

    /**
     * 查询用户聊天记录
     */
    String QUERY_MSG_RECORD_FACADE = "QUERY_MSG_RECORD_FACADE";
    String QUERY_INNER_MSG_RECORD_FACADE = "QUERY_INNER_MSG_RECORD_FACADE";

    /**
     * 获取消息快捷语
     */
    String GET_QUICK_MSG_FACADE = "GET_QUICK_MSG_FACADE";

    /**
     * 获取司机在离线状态
     */
    String GET_DRIVER_ONLINE_STATUS_FACADE = "GET_DRIVER_ONLINE_STATUS_FACADE";

    /**
     * 上报用户状态
     */
    String UPLOAD_USER_STATUS_FACADE = "UPLOAD_USER_STATUS_FACADE";

    /**
     * 能否发送消息检查
     */
    String CHECK_ALLOW_SEND_MSG_FACADE = "CHECK_ALLOW_SEND_MSG_FACADE";

    /**
     * 删除消息列表
     */
    String DELETE_MSG_FACADE = "DELETE_MSG_FACADE";

    /**
     * 查询未读消息列表&未读消息数量
     */
    String QUERY_UNREAD_MSG_FACADE = "QUERY_UNREAD_MSG_FACADE";

    /**
     * 查询未读消息数量
     */
    String QUERY_UNREAD_MSG_COUNT_FACADE = "QUERY_UNREAD_MSG_COUNT_FACADE";

    /**
     * 查询是否有司机未读消息
     */
    String QUERY_IS_DRIVER_UNREAD_MSG_FACADE = "QUERY_IS_DRIVER_UNREAD_MSG_FACADE";

    /**
     * 获取司机消息
     */
    String GET_MSG_FACADE = "GET_MSG_FACADE";

    /**
     * 关闭会话
     */
    String CLOSE_CHAT_ACTION_FACADE = "CLOSE_CHAT_ACTION_FACADE";

    /**
     * 创建会话
     */
    String OPEN_CHAT_ACTION_FACADE = "OPEN_CHAT_ACTION_FACADE";

    /**
     * 获取订阅消息模版
     */
    String GET_MSG_TEMPLATE_FACADE = "GET_MSG_TEMPLATE_FACADE";

    /**
     * 订阅回调
     */
    String CALL_BACK_FACADE = "CALL_BACK_FACADE";

    /**
     * 获取系统版本
     */
    String GET_VERSION_FACADE = "GET_VERSION_FACADE";

    /**
     * 获取imKeyInfos
     */
    String GET_IM_KEY_INFOS_FACADE = "GET_IM_KEY_INFOS_FACADE";



    /**
     * 乘客消息发送司机
     */
    String DISTRIBUTE_SEND_MSG_FACADE = "DISTRIBUTE_SEND_MSG_FACADE";

    /**
     * 获取聊天开关信息
     */
    String GET_CHAT_SWITCH_INFOS_FACADE = "GET_CHAT_SWITCH_INFOS_FACADE";
}
