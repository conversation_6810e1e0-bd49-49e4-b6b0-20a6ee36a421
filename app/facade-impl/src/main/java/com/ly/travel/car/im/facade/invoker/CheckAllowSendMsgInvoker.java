package com.ly.travel.car.im.facade.invoker;

import com.ly.sof.gateway.core.Proxy;
import com.ly.sof.gateway.core.ProxyInvoker;
import com.ly.sof.gateway.exception.ProxyException;
import com.ly.sof.gateway.model.GatewayContext;
import com.ly.sof.utils.log.LogContextUtils;
import com.ly.travel.car.im.biz.service.ChatService;
import com.ly.travel.car.im.facade.Services;
import com.ly.travel.car.im.facade.request.CheckAllowSendMsgReqDTO;
import com.ly.travel.car.im.facade.response.CheckAllowSendMsgRspDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Proxy(Services.CHECK_ALLOW_SEND_MSG_FACADE)
public class CheckAllowSendMsgInvoker implements ProxyInvoker<CheckAllowSendMsgReqDTO, CheckAllowSendMsgRspDTO> {

    @Resource
    private ChatService chatService;

    @Override
    public CheckAllowSendMsgRspDTO invoke(GatewayContext<CheckAllowSendMsgReqDTO, CheckAllowSendMsgRspDTO> context) throws ProxyException {
        LogContextUtils.setFilter1(context.getRequest().getTraceId());
        return chatService.checkAllowSendMsg(context.getRequest());
    }
}