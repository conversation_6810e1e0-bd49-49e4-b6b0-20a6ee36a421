package com.ly.travel.car.im.facade.impl;

import com.ly.sof.gateway.AbstractFacade;
import com.ly.sof.utils.log.LogContextUtils;
import com.ly.travel.car.im.facade.ChatFacade;
import com.ly.travel.car.im.facade.DistributeChatFacade;
import com.ly.travel.car.im.facade.Services;
import com.ly.travel.car.im.facade.request.*;
import com.ly.travel.car.im.facade.response.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service("distributeChatFacade")
public class DistributeChatFacadeImpl extends AbstractFacade implements DistributeChatFacade {
    @Override
    public SendMsgRspDTO sendMsg(DistributeSendMsgReqDTO req) {
        LogContextUtils.initTracer(Services.DISTRIBUTE_SEND_MSG_FACADE, req.getTraceId(), StringUtils.EMPTY);
        LogContextUtils.setFilter2(req.getTraceId());
        return execute(req, Services.DISTRIBUTE_SEND_MSG_FACADE, SendMsgRspDTO.class);
    }

}
