package com.ly.travel.car.im.facade.invoker;

import com.ly.sof.gateway.core.Proxy;
import com.ly.sof.gateway.core.ProxyInvoker;
import com.ly.sof.gateway.exception.ProxyException;
import com.ly.sof.gateway.model.GatewayContext;
import com.ly.sof.utils.log.LogContextUtils;
import com.ly.travel.car.im.biz.service.ChatService;
import com.ly.travel.car.im.facade.Services;
import com.ly.travel.car.im.facade.request.QueryUnreadMsgCountReqDTO;
import com.ly.travel.car.im.facade.response.QueryUnreadMsgCountRspDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Proxy(Services.QUERY_UNREAD_MSG_COUNT_FACADE)
public class QueryUnreadMsgCountInvoker implements ProxyInvoker<QueryUnreadMsgCountReqDTO, QueryUnreadMsgCountRspDTO> {
    @Resource
    private ChatService chatService;

    @Override
    public QueryUnreadMsgCountRspDTO invoke(GatewayContext<QueryUnreadMsgCountReqDTO, QueryUnreadMsgCountRspDTO> context) throws ProxyException {
        LogContextUtils.setFilter2(context.getRequest().getTraceId());
        LogContextUtils.setCategory("queryUnreadMsgCount");
        return chatService.queryUnreadMsgCount(context.getRequest());
    }
}