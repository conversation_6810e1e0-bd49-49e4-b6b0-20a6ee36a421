package com.ly.travel.car.im.facade.impl;

import com.ly.sof.gateway.AbstractFacade;
import com.ly.sof.utils.log.LogContextUtils;
import com.ly.travel.car.im.facade.Services;
import com.ly.travel.car.im.facade.SupplierChatFacade;
import com.ly.travel.car.im.facade.request.*;
import com.ly.travel.car.im.facade.response.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service("supplierChatFacade")
public class SupplierChatFacadeImpl extends AbstractFacade implements SupplierChatFacade {
    @Override
    public GetMsgRspDTO getMsg(GetMsgReqDTO req) {
        LogContextUtils.initTracer(Services.GET_MSG_FACADE, req.getTraceId(), StringUtils.EMPTY);
        LogContextUtils.setFilter2(req.getTraceId());
        return execute(req, Services.GET_MSG_FACADE, GetMsgRspDTO.class);
    }

    @Override
    public CloseChatActionRspDTO closeChatAction(CloseChatActionReqDTO req) {
        LogContextUtils.initTracer(Services.CLOSE_CHAT_ACTION_FACADE, req.getTraceId(), StringUtils.EMPTY);
        return execute(req, Services.CLOSE_CHAT_ACTION_FACADE, CloseChatActionRspDTO.class);
    }

    @Override
    public OpenChatActionRspDTO openChatAction(OpenChatActionReqDTO req) {
        LogContextUtils.initTracer(Services.OPEN_CHAT_ACTION_FACADE, req.getTraceId(), StringUtils.EMPTY);
        LogContextUtils.setFilter2(req.getTraceId());
        return execute(req, Services.OPEN_CHAT_ACTION_FACADE, OpenChatActionRspDTO.class);
    }
}
