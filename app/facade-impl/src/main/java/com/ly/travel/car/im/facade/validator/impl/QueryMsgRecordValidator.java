package com.ly.travel.car.im.facade.validator.impl;

import com.ly.sof.gateway.core.Proxy;
import com.ly.sof.gateway.core.Validator;
import com.ly.sof.gateway.exception.ValidationException;
import com.ly.travel.car.im.facade.Services;
import com.ly.travel.car.im.facade.request.QueryMsgRecordReqDTO;
import com.ly.travel.car.im.facade.validator.BaseValidator;
import com.ly.travel.car.im.model.enums.OrderChannelEnum;
import com.ly.travel.car.im.model.utils.PlatformUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.UUID;

@Slf4j
@Service
@Proxy(Services.QUERY_MSG_RECORD_FACADE)
public class QueryMsgRecordValidator extends BaseValidator implements Validator<QueryMsgRecordReqDTO> {
    @Override
    public void validate(QueryMsgRecordReqDTO req) throws ValidationException {
        dtoNotNullValidate(req, "request");
        req.setTraceId(StringUtils.defaultIfBlank(req.getTraceId(), UUID.randomUUID().toString()));
        dtoNotStringEmptyValidate(req.getTraceId(), "traceId");
        dtoNotStringEmptyValidate(req.getSessionKey(), "sessionKey");

        if (PlatformUtils.isWX(req.getPlatform()) && (Objects.isNull( req.getPlatId())|| req.getPlatId() == 0)){
            req.setPlatId(OrderChannelEnum.XCX.getCode());
        }

        if (PlatformUtils.isApp(req.getPlatform()) && (Objects.isNull( req.getPlatId())|| req.getPlatId() == 0)){
            req.setPlatId(OrderChannelEnum.IOS_APP.getCode());
        }
    }
}