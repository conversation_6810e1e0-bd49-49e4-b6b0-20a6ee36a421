package com.ly.travel.car.im.facade.validator.impl;

import com.ly.sof.gateway.core.Proxy;
import com.ly.sof.gateway.core.Validator;
import com.ly.sof.gateway.exception.ValidationException;
import com.ly.travel.car.im.facade.Services;
import com.ly.travel.car.im.facade.request.GetDriverOnlineStatusReqDTO;
import com.ly.travel.car.im.facade.validator.BaseValidator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Slf4j
@Service
@Proxy(Services.GET_DRIVER_ONLINE_STATUS_FACADE)
public class GetDriverOnLineStatusValidator extends BaseValidator implements Validator<GetDriverOnlineStatusReqDTO> {
    @Override
    public void validate(GetDriverOnlineStatusReqDTO req) throws ValidationException {
        dtoNotNullValidate(req, "request");
        req.setTraceId(StringUtils.defaultIfBlank(req.getTraceId(), UUID.randomUUID().toString()));
        dtoNotStringEmptyValidate(req.getTraceId(), "traceId");
        dtoNotStringEmptyValidate(req.getSupplierOrderId(), "supplierOrderId");
        dtoNotStringEmptyValidate(req.getDriverId(), "driverId");
    }
}