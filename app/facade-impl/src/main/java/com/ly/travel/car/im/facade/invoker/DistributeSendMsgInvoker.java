package com.ly.travel.car.im.facade.invoker;

import com.ly.sof.gateway.core.Proxy;
import com.ly.sof.gateway.core.ProxyInvoker;
import com.ly.sof.gateway.exception.ProxyException;
import com.ly.sof.gateway.model.GatewayContext;
import com.ly.sof.utils.log.LogContextUtils;
import com.ly.travel.car.im.biz.service.ChatService;
import com.ly.travel.car.im.biz.service.DistributeChatService;
import com.ly.travel.car.im.facade.Services;
import com.ly.travel.car.im.facade.request.DistributeSendMsgReqDTO;
import com.ly.travel.car.im.facade.request.SendMsgReqDTO;
import com.ly.travel.car.im.facade.response.SendMsgRspDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Proxy(Services.DISTRIBUTE_SEND_MSG_FACADE)
public class DistributeSendMsgInvoker implements ProxyInvoker<DistributeSendMsgReqDTO, SendMsgRspDTO> {
    @Resource
    private DistributeChatService chatService;

    @Override
    public SendMsgRspDTO invoke(GatewayContext<DistributeSendMsgReqDTO, SendMsgRspDTO> context) throws ProxyException {
        LogContextUtils.setFilter2(context.getRequest().getTraceId());
        LogContextUtils.setCategory("sendMsg");
        return chatService.sendMsg(context.getRequest());
    }
}