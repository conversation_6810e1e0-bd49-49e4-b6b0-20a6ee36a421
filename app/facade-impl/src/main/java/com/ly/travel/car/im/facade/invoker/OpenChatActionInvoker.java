package com.ly.travel.car.im.facade.invoker;

import com.ly.sof.gateway.core.Proxy;
import com.ly.sof.gateway.core.ProxyInvoker;
import com.ly.sof.gateway.exception.ProxyException;
import com.ly.sof.gateway.model.GatewayContext;
import com.ly.sof.utils.log.LogContextUtils;
import com.ly.travel.car.im.biz.service.SupplierChatService;
import com.ly.travel.car.im.facade.Services;
import com.ly.travel.car.im.facade.request.OpenChatActionReqDTO;
import com.ly.travel.car.im.facade.response.OpenChatActionRspDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Proxy(Services.OPEN_CHAT_ACTION_FACADE)
public class OpenChatActionInvoker implements ProxyInvoker<OpenChatActionReqDTO, OpenChatActionRspDTO> {
    @Resource
    public SupplierChatService supplierChatService;

    @Override
    public OpenChatActionRspDTO invoke(GatewayContext<OpenChatActionReqDTO, OpenChatActionRspDTO> context) throws ProxyException {
        LogContextUtils.setFilter2(context.getRequest().getTraceId());
        LogContextUtils.setCategory("openChatAction");
        return supplierChatService.openChatAction(context.getRequest());
    }
}