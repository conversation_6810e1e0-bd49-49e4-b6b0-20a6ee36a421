package com.ly.travel.car.im.facade.invoker;

import com.ly.sof.gateway.core.Proxy;
import com.ly.sof.gateway.core.ProxyInvoker;
import com.ly.sof.gateway.exception.ProxyException;
import com.ly.sof.gateway.model.GatewayContext;
import com.ly.sof.utils.log.LogContextUtils;
import com.ly.travel.car.im.biz.service.WxSubService;
import com.ly.travel.car.im.facade.Services;
import com.ly.travel.car.im.facade.request.GetMsgTemplateReqDTO;
import com.ly.travel.car.im.facade.response.GetMsgTemplateRspDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Proxy(Services.GET_MSG_TEMPLATE_FACADE)
public class GetMsgTemplateInvoker implements ProxyInvoker<GetMsgTemplateReqDTO, GetMsgTemplateRspDTO> {
    @Resource
    private WxSubService wxSubService;

    @Override
    public GetMsgTemplateRspDTO invoke(GatewayContext<GetMsgTemplateReqDTO, GetMsgTemplateRspDTO> context) throws ProxyException {
        LogContextUtils.setFilter1(context.getRequest().getTraceId());
        return wxSubService.getMsgTemplate(context.getRequest());
    }
}