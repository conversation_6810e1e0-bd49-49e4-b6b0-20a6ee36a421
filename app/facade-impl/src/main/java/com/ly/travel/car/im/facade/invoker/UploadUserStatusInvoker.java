package com.ly.travel.car.im.facade.invoker;

import com.ly.sof.gateway.core.Proxy;
import com.ly.sof.gateway.core.ProxyInvoker;
import com.ly.sof.gateway.exception.ProxyException;
import com.ly.sof.gateway.model.GatewayContext;
import com.ly.sof.utils.log.LogContextUtils;
import com.ly.travel.car.im.biz.service.ChatService;
import com.ly.travel.car.im.facade.Services;
import com.ly.travel.car.im.facade.request.UploadUserStatusReqDTO;
import com.ly.travel.car.im.facade.response.UploadUserStatusRspDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Proxy(Services.UPLOAD_USER_STATUS_FACADE)
public class UploadUserStatusInvoker implements ProxyInvoker<UploadUserStatusReqDTO, UploadUserStatusRspDTO> {
    @Resource
    private ChatService chatService;

    @Override
    public UploadUserStatusRspDTO invoke(GatewayContext<UploadUserStatusReqDTO, UploadUserStatusRspDTO> context) throws ProxyException {
        LogContextUtils.setFilter1(context.getRequest().getTraceId());
        return chatService.uploadUserStatus(context.getRequest());
    }
}