package com.ly.travel.car.im.facade.invoker;

import com.ly.sof.gateway.core.Proxy;
import com.ly.sof.gateway.core.ProxyInvoker;
import com.ly.sof.gateway.exception.ProxyException;
import com.ly.sof.gateway.model.GatewayContext;
import com.ly.sof.utils.log.LogContextUtils;
import com.ly.travel.car.im.biz.service.SupplierChatService;
import com.ly.travel.car.im.facade.Services;
import com.ly.travel.car.im.facade.request.CloseChatActionReqDTO;
import com.ly.travel.car.im.facade.response.CloseChatActionRspDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Proxy(Services.CLOSE_CHAT_ACTION_FACADE)
public class CloseChatActionInvoker implements ProxyInvoker<CloseChatActionReqDTO, CloseChatActionRspDTO> {
    @Resource
    private SupplierChatService supplierChatService;

    @Override
    public CloseChatActionRspDTO invoke(GatewayContext<CloseChatActionReqDTO, CloseChatActionRspDTO> context) throws ProxyException {
        LogContextUtils.setFilter1(context.getRequest().getTraceId());
        return supplierChatService.closeChatAction(context.getRequest());
    }
}