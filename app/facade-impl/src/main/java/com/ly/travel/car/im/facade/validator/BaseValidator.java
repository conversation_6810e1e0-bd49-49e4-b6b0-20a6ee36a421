package com.ly.travel.car.im.facade.validator;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.ly.sof.api.error.LYError;
import com.ly.sof.gateway.exception.ValidationException;
import com.ly.travel.car.im.facade.error.GatewayErrorFactory;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 基础校验
 *
 * <AUTHOR>
 * @version Id: BaseValidator, v 0.1 2024/05/27 15:01 lichunbo Exp $
 */
public abstract class BaseValidator {
    /**
     * The Error factory
     */
    protected static final GatewayErrorFactory ERROR_FACTORY = GatewayErrorFactory.getInstance();

    /**
     * 参数不为真
     *
     * @param value 入参
     * @throws ValidationException 校验异常
     */
    protected void dtoNotTrueValidate(boolean value, String message) throws ValidationException {
        if (!value) {
            throw new ValidationException(ERROR_FACTORY.paramNull(message));
        }
    }

    /**
     * 参数不为空
     *
     * @param value 入参
     * @throws ValidationException 校验异常
     */
    protected  void dtoNotNullValidate(Object value, String message) throws ValidationException {
        if (value == null) {
            throw new ValidationException(ERROR_FACTORY.paramNull(message));
        }
    }

    /**
     * 字符串不为空或空字符串
     *
     * @param value 入参
     * @throws ValidationException 校验异常
     */
    protected  void dtoNotStringEmptyValidate(String value, String message) throws ValidationException {
        if (StringUtils.isBlank(value)) {
            throw new ValidationException(ERROR_FACTORY.paramNull(message));
        }
    }

    /**
     * 字符串不为空或空字符串
     *
     * @param value 入参
     * @throws ValidationException 校验异常
     */
    protected void dtoNotStringEmptyValidate(String value, String value2, String message) throws ValidationException {
        if (StringUtils.isBlank(value) && StringUtils.isBlank(value2)) {
            throw new ValidationException(ERROR_FACTORY.paramNull(message));
        }
    }

    /**
     * integer不为空或空字符串
     *
     * @param value 入参
     * @throws ValidationException 校验异常
     */
    protected void dtoNotIntegerEmptyValidate(Integer value, String message) throws ValidationException {
        if (value == null) {
            throw new ValidationException(ERROR_FACTORY.paramNull(message));
        }
    }

    /**
     * 集合不为空或空字符串
     *
     * @param value 入参
     * @throws ValidationException 校验异常
     */
    protected void dtoNotCollectionEmptyValidate(Collection<?> value, String message) throws ValidationException {
        if (CollectionUtils.isEmpty(value)) {
            throw new ValidationException(ERROR_FACTORY.paramNull(message));
        }
    }

    /**
     * 正则匹配
     *
     * @param pattern of type Pattern
     * @param matcher of type String
     * @param lyError of type LYError
     * @throws ValidationException 校验异常
     */
    protected void regularMatch(Pattern pattern, String matcher, LYError lyError) throws ValidationException {
        Matcher m = pattern.matcher(matcher);
        if (!m.matches()) {
            throw new ValidationException(lyError);
        }
    }

    /**
     * 枚举校验
     *
     * @param code      前端传的枚举 code
     * @param enumValue 枚举值
     * @param param     参数名
     * @throws ValidationException 异常
     */
    protected void enumValidate(Object code, Object enumValue, String param) throws ValidationException {
        if (enumValue == null) {
            throw new ValidationException(ERROR_FACTORY.illegalParam(param, code));
        }
    }
}
