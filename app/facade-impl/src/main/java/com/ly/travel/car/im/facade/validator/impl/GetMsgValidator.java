package com.ly.travel.car.im.facade.validator.impl;

import com.ly.sof.gateway.core.Proxy;
import com.ly.sof.gateway.core.Validator;
import com.ly.sof.gateway.exception.ValidationException;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.im.dal.dao.SfcImInfoDao;
import com.ly.travel.car.im.dal.entity.SfcImInfo;
import com.ly.travel.car.im.facade.Services;
import com.ly.travel.car.im.facade.dto.ContentDTO;
import com.ly.travel.car.im.facade.request.GetMsgReqDTO;
import com.ly.travel.car.im.facade.validator.BaseValidator;
import com.ly.travel.car.im.model.config.ImConfigCenter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
@Proxy(Services.GET_MSG_FACADE)
public class GetMsgValidator extends BaseValidator implements Validator<GetMsgReqDTO> {
    @Resource
    private SfcImInfoDao sfcImInfoDao;
    private static final String YueYue = "YueYue";

    @Override
    public void validate(GetMsgReqDTO req) throws ValidationException {
        dtoNotNullValidate(req, "request");
        req.setTraceId(StringUtils.defaultIfBlank(req.getTraceId(), UUID.randomUUID().toString()));
        dtoNotStringEmptyValidate(req.getTraceId(), "traceId");
        dtoNotStringEmptyValidate(req.getSupplierMsgType(), "supplierMsgType");
        dtoNotStringEmptyValidate(req.getTcMsgType(), "tcMsgType");
        dtoNotStringEmptyValidate(req.getSupplierCode(), "supplierCode");
        dtoNotStringEmptyValidate(req.getContent(), "content");
        ContentDTO contentDto = FastJsonUtils.fromJSONString(req.getContent(), ContentDTO.class);
        dtoNotStringEmptyValidate(contentDto.getMsgId(), "msgId");
        dtoNotStringEmptyValidate(contentDto.getFromUserGuid(), "fromUserGuid");

        //约约供应商不传供应商订单号 需特殊处理
        if (req.getSupplierCode().contains(YueYue)) {
            SfcImInfo sfcImInfo = sfcImInfoDao.findByTcConversationId(req.getTcConversationId());
            dtoNotNullValidate(sfcImInfo, "sfcImInfo");
            contentDto.setPassengerOrderGuid(sfcImInfo.getSupplierOrderId());
        }

        dtoNotStringEmptyValidate(contentDto.getPassengerOrderGuid(), "passengerOrderGuid");
        req.setContent(FastJsonUtils.toJSONString(contentDto));

        //检查入参消息类型
        List<String> msgTypeConfigs = ImConfigCenter.getSupportMsgTypeConfig();
        if (CollectionUtils.isNotEmpty(msgTypeConfigs) && !msgTypeConfigs.contains(req.getTcMsgType())) {
            throw new ValidationException(ERROR_FACTORY.validationMsgType(req.getTcMsgType()));
        }
    }
}