package com.ly.travel.car.im.facade.validator.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.ly.sof.gateway.core.Proxy;
import com.ly.sof.gateway.core.Validator;
import com.ly.sof.gateway.exception.ValidationException;
import com.ly.travel.car.im.biz.utils.ImUtils;
import com.ly.travel.car.im.facade.Services;
import com.ly.travel.car.im.facade.dto.BaseResponse;
import com.ly.travel.car.im.facade.enums.MsgTypeEnum;
import com.ly.travel.car.im.facade.request.DistributeSendMsgReqDTO;
import com.ly.travel.car.im.facade.request.SendMsgReqDTO;
import com.ly.travel.car.im.facade.validator.BaseValidator;
import com.ly.travel.car.im.model.config.ImConfigCenter;
import com.ly.travel.car.im.model.dto.DecryptMemberConfigDTO;
import com.ly.travel.car.im.model.enums.OrderChannelEnum;
import com.ly.travel.car.im.model.utils.PlatformUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;
import java.util.UUID;

import static com.ly.travel.car.im.biz.utils.DecryptMemberInfoUtil.getMemberInfo;

@Slf4j
@Service
@Proxy(Services.DISTRIBUTE_SEND_MSG_FACADE)
public class DistributeSendMsgValidator extends BaseValidator implements Validator<DistributeSendMsgReqDTO> {
    @Override
    public void validate(DistributeSendMsgReqDTO req) throws ValidationException {
        dtoNotNullValidate(req, "request");
        req.setTraceId(StringUtils.defaultIfBlank(req.getTraceId(), UUID.randomUUID().toString()));
        dtoNotStringEmptyValidate(req.getTraceId(), "traceId");
        dtoNotStringEmptyValidate(req.getSupplierCode(), "supplierCode");
        dtoNotStringEmptyValidate(req.getMsgId(), "msgId");
        dtoNotStringEmptyValidate(req.getSupplierOrderId(), "supplierOrderId");
        dtoNotStringEmptyValidate(req.getOrderId(), "orderId");
        dtoNotStringEmptyValidate(req.getDriverNickName(), "driverNickName");
        dtoNotNullValidate(req.getMsgType(), "msgType");
        dtoNotNullValidate(req.getMsgContent(), "msgContent");
        //组装sessionKey标识
        req.setSessionKey(ImUtils.buildSessionKey(req.getSupplierCode(), req.getOrderId(), req.getDriverId()));
        if (MsgTypeEnum.DISTRIBUTE_CUSTOM_TEXT.getMsgType().equals(req.getMsgType())){
            req.setMsgId(JSON.parseArray(req.getMsgId()).get(0).toString());
        }
        req.setMsgSendTime(new Date());
    }


}
