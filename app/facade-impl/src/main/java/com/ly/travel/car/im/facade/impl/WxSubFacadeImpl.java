package com.ly.travel.car.im.facade.impl;

import com.ly.sof.gateway.AbstractFacade;
import com.ly.sof.utils.log.LogContextUtils;
import com.ly.travel.car.im.facade.Services;
import com.ly.travel.car.im.facade.WxSubFacade;
import com.ly.travel.car.im.facade.dto.BaseDTO;
import com.ly.travel.car.im.facade.request.*;
import com.ly.travel.car.im.facade.response.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service("wxSubFacade")
public class WxSubFacadeImpl extends AbstractFacade implements WxSubFacade {

    @Override
    public GetMsgTemplateRspDTO getMsgTemplate(GetMsgTemplateReqDTO req) {
        LogContextUtils.initTracer(Services.GET_MSG_TEMPLATE_FACADE, req.getTraceId(), StringUtils.EMPTY);
        return execute(req, Services.GET_MSG_TEMPLATE_FACADE, GetMsgTemplateRspDTO.class);
    }

    @Override
    public CallBackRspDTO callBack(BaseDTO req) {
        LogContextUtils.initTracer(Services.CALL_BACK_FACADE, req.getTraceId(), StringUtils.EMPTY);
        return execute(req, Services.CALL_BACK_FACADE, CallBackRspDTO.class);
    }
}
