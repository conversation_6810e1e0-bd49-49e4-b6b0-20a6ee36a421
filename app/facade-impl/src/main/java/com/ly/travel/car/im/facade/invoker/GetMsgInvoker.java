package com.ly.travel.car.im.facade.invoker;

import com.ly.sof.gateway.core.Proxy;
import com.ly.sof.gateway.core.ProxyInvoker;
import com.ly.sof.gateway.exception.ProxyException;
import com.ly.sof.gateway.model.GatewayContext;
import com.ly.sof.utils.log.LogContextUtils;
import com.ly.travel.car.im.biz.service.SupplierChatService;
import com.ly.travel.car.im.facade.Services;
import com.ly.travel.car.im.facade.request.GetMsgReqDTO;
import com.ly.travel.car.im.facade.response.GetMsgRspDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Proxy(Services.GET_MSG_FACADE)
public class GetMsgInvoker implements ProxyInvoker<GetMsgReqDTO, GetMsgRspDTO> {
    @Resource
    private SupplierChatService supplierChatService;

    @Override
    public GetMsgRspDTO invoke(GatewayContext<GetMsgReqDTO, GetMsgRspDTO> context) throws ProxyException {
        LogContextUtils.setFilter2(context.getRequest().getTraceId());
        LogContextUtils.setCategory("getMsg");
        return supplierChatService.getMsg(context.getRequest());
    }
}