package com.ly.travel.car.im.facade.validator.impl;

import com.ly.sof.gateway.core.Proxy;
import com.ly.sof.gateway.core.Validator;
import com.ly.sof.gateway.exception.ValidationException;
import com.ly.travel.car.im.biz.error.ValidateException;
import com.ly.travel.car.im.facade.Services;
import com.ly.travel.car.im.facade.enums.MsgTypeEnum;
import com.ly.travel.car.im.facade.request.QueryMsgReqDTO;
import com.ly.travel.car.im.facade.validator.BaseValidator;
import com.ly.travel.car.im.model.enums.OrderChannelEnum;
import com.ly.travel.car.im.model.utils.PlatformUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.UUID;

@Slf4j
@Service
@Proxy(Services.QUERY_MSG_FACADE)
public class QueryMsgValidator extends BaseValidator implements Validator<QueryMsgReqDTO> {
    @Override
    public void validate(QueryMsgReqDTO req) throws ValidationException {
        dtoNotNullValidate(req, "request");
        req.setTraceId(StringUtils.defaultIfBlank(req.getTraceId(), UUID.randomUUID().toString()));
        if (req.getPlatform().equals("WX")&& StringUtils.isEmpty(req.getUnionId())){
            dtoNotNullValidate(req.getUnionId(), "unionId");
        }
        if (req.getPlatform().equals("APP")&& StringUtils.isEmpty(req.getMemberId())){
            dtoNotNullValidate(req.getMemberId(), "memberId");
        }
        dtoNotStringEmptyValidate(req.getTraceId(), "traceId");

        if (PlatformUtils.isWX(req.getPlatform()) && (Objects.isNull( req.getPlatId())|| req.getPlatId() == 0)){
            req.setPlatId(OrderChannelEnum.XCX.getCode());
        }

        if (PlatformUtils.isApp(req.getPlatform()) && (Objects.isNull( req.getPlatId())|| req.getPlatId() == 0)){
            req.setPlatId(OrderChannelEnum.IOS_APP.getCode());
        }

        if (req.getPlatId() == OrderChannelEnum.DISTRIBUTION.getCode()) {
            req.setPlatform("APP");
        }
    }
}