package com.ly.travel.car.im.facade.impl;

import com.ly.sof.utils.log.LogContextUtils;
import com.ly.travel.car.im.facade.ChatFacade;
import com.ly.sof.gateway.AbstractFacade;
import com.ly.travel.car.im.facade.Services;
import com.ly.travel.car.im.facade.request.*;
import com.ly.travel.car.im.facade.response.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service("chatFacade")
public class ChatFacadeImpl extends AbstractFacade implements ChatFacade {

    @Override
    public SendMsgRspDTO sendMsg(SendMsgReqDTO req) {
        LogContextUtils.initTracer(Services.SEND_MSG_FACADE, req.getTraceId(), StringUtils.EMPTY);
        LogContextUtils.setFilter2(req.getTraceId());
        return execute(req, Services.SEND_MSG_FACADE, SendMsgRspDTO.class);
    }

    @Override
    public QueryMsgRspDTO queryMsgList(QueryMsgReqDTO req) {
        LogContextUtils.initTracer(Services.QUERY_MSG_FACADE, req.getTraceId(), StringUtils.EMPTY);
        LogContextUtils.setFilter2(req.getTraceId());
        return execute(req, Services.QUERY_MSG_FACADE, QueryMsgRspDTO.class);
    }

    @Override
    public QueryMsgRecordRspDTO queryMsgRecord(QueryMsgRecordReqDTO req) {
        LogContextUtils.initTracer(Services.QUERY_MSG_RECORD_FACADE, req.getTraceId(), StringUtils.EMPTY);
        LogContextUtils.setFilter2(req.getTraceId());
        return execute(req, Services.QUERY_MSG_RECORD_FACADE, QueryMsgRecordRspDTO.class);
    }

    @Override
    public GetQuickMsgRspDTO getQuickMsgList(GetQuickMsgReqDTO req) {
        LogContextUtils.initTracer(Services.GET_QUICK_MSG_FACADE, req.getTraceId(), StringUtils.EMPTY);
        LogContextUtils.setFilter2(req.getTraceId());
        return execute(req, Services.GET_QUICK_MSG_FACADE, GetQuickMsgRspDTO.class);
    }

    @Override
    public GetDriverOnlineStatusRspDTO getDriverOnlineStatus(GetDriverOnlineStatusReqDTO req) {
        LogContextUtils.initTracer(Services.GET_DRIVER_ONLINE_STATUS_FACADE, req.getTraceId(), StringUtils.EMPTY);
        return execute(req, Services.GET_DRIVER_ONLINE_STATUS_FACADE, GetDriverOnlineStatusRspDTO.class);
    }

    @Override
    public UploadUserStatusRspDTO uploadUserStatus(UploadUserStatusReqDTO req) {
        LogContextUtils.initTracer(Services.UPLOAD_USER_STATUS_FACADE, req.getTraceId(), StringUtils.EMPTY);
        return execute(req, Services.UPLOAD_USER_STATUS_FACADE, UploadUserStatusRspDTO.class);
    }

    @Override
    public CheckAllowSendMsgRspDTO checkAllowSendMsg(CheckAllowSendMsgReqDTO req) {
        LogContextUtils.initTracer(Services.CHECK_ALLOW_SEND_MSG_FACADE, req.getTraceId(), StringUtils.EMPTY);
        return execute(req, Services.CHECK_ALLOW_SEND_MSG_FACADE, CheckAllowSendMsgRspDTO.class);
    }

    @Override
    public DeleteMsgRspDTO deleteMsg(DeleteMsgReqDTO req) {
        LogContextUtils.initTracer(Services.DELETE_MSG_FACADE, req.getTraceId(), StringUtils.EMPTY);
        return execute(req, Services.DELETE_MSG_FACADE, DeleteMsgRspDTO.class);
    }

    @Override
    public QueryUnreadMsgRspDTO queryUnreadMsg(QueryUnreadMsgReqDTO req) {
        LogContextUtils.initTracer(Services.QUERY_UNREAD_MSG_FACADE, req.getTraceId(), StringUtils.EMPTY);
        return execute(req, Services.QUERY_UNREAD_MSG_FACADE, QueryUnreadMsgRspDTO.class);
    }

    @Override
    public QueryUnreadMsgCountRspDTO queryUnreadMsgCount(QueryUnreadMsgCountReqDTO req) {
        LogContextUtils.initTracer(Services.QUERY_UNREAD_MSG_COUNT_FACADE, req.getTraceId(), StringUtils.EMPTY);
        LogContextUtils.setFilter2(req.getTraceId());
        return execute(req, Services.QUERY_UNREAD_MSG_COUNT_FACADE, QueryUnreadMsgCountRspDTO.class);
    }

    @Override
    public QueryIsDriverUnreadMsgRspDTO queryIsDriverUnreadMsg(QueryIsDriverUnreadMsgReqDTO req) {
        LogContextUtils.initTracer(Services.QUERY_IS_DRIVER_UNREAD_MSG_FACADE, req.getTraceId(), StringUtils.EMPTY);
        return execute(req, Services.QUERY_UNREAD_MSG_FACADE, QueryIsDriverUnreadMsgRspDTO.class);
    }

    @Override
    public GetVersionRspDTO getVersion(GetVersionReqDTO req) {
        LogContextUtils.initTracer(Services.GET_VERSION_FACADE, req.getTraceId(), StringUtils.EMPTY);
        LogContextUtils.setFilter2(req.getTraceId());
        return execute(req, Services.GET_VERSION_FACADE, GetVersionRspDTO.class);
    }

    @Override
    public GetImKeyInfosRspDTO getImKeyInfos(GetImKeyInfosReqDTO req) {
        LogContextUtils.initTracer(Services.GET_IM_KEY_INFOS_FACADE, req.getTraceId(), StringUtils.EMPTY);
        return execute(req, Services.GET_IM_KEY_INFOS_FACADE, GetImKeyInfosRspDTO.class);
    }


    @Override
    public QueryMsgRecordRspDTO queryInnerMsgRecord(QueryMsgRecordReqDTO req) {
        LogContextUtils.initTracer(Services.QUERY_INNER_MSG_RECORD_FACADE, req.getTraceId(), StringUtils.EMPTY);
        LogContextUtils.setFilter2(req.getTraceId());
        return execute(req, Services.QUERY_INNER_MSG_RECORD_FACADE, QueryMsgRecordRspDTO.class);
    }

    @Override
    public GetChatSwitchInfosRspDTO getChatSwitchInfos(GetChatSwitchInfosReqDTO req) {
        LogContextUtils.initTracer(Services.GET_CHAT_SWITCH_INFOS_FACADE, req.getTraceId(), StringUtils.EMPTY);
        return execute(req, Services.GET_CHAT_SWITCH_INFOS_FACADE, GetChatSwitchInfosRspDTO.class);
    }
}
