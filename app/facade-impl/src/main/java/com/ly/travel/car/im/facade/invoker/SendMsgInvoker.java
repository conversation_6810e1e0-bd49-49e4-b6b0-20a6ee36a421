package com.ly.travel.car.im.facade.invoker;

import com.ly.sof.gateway.core.Proxy;
import com.ly.sof.gateway.core.ProxyInvoker;
import com.ly.sof.gateway.exception.ProxyException;
import com.ly.sof.gateway.model.GatewayContext;
import com.ly.sof.utils.log.LogContextUtils;
import com.ly.travel.car.im.biz.service.ChatService;
import com.ly.travel.car.im.facade.Services;
import com.ly.travel.car.im.facade.request.SendMsgReqDTO;
import com.ly.travel.car.im.facade.response.SendMsgRspDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Proxy(Services.SEND_MSG_FACADE)
public class SendMsgInvoker implements ProxyInvoker<SendMsgReqDTO, SendMsgRspDTO> {
    @Resource
    private ChatService chatService;

    @Override
    public SendMsgRspDTO invoke(GatewayContext<SendMsgReqDTO, SendMsgRspDTO> context) throws ProxyException {
        LogContextUtils.setFilter2(context.getRequest().getTraceId());
        LogContextUtils.setCategory("sendMsg");
        return chatService.sendMsg(context.getRequest());
    }
}