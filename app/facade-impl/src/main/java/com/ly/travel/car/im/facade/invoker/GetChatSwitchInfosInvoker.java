package com.ly.travel.car.im.facade.invoker;

import com.ly.sof.gateway.core.Proxy;
import com.ly.sof.gateway.core.ProxyInvoker;
import com.ly.sof.gateway.exception.ProxyException;
import com.ly.sof.gateway.model.GatewayContext;
import com.ly.sof.utils.log.LogContextUtils;
import com.ly.travel.car.im.biz.service.ChatService;
import com.ly.travel.car.im.facade.Services;
import com.ly.travel.car.im.facade.request.GetChatSwitchInfosReqDTO;
import com.ly.travel.car.im.facade.response.GetChatSwitchInfosRspDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Proxy(Services.GET_CHAT_SWITCH_INFOS_FACADE)
public class GetChatSwitchInfosInvoker implements ProxyInvoker<GetChatSwitchInfosReqDTO, GetChatSwitchInfosRspDTO> {
    @Resource
    private ChatService chatService;

    @Override
    public GetChatSwitchInfosRspDTO invoke(GatewayContext<GetChatSwitchInfosReqDTO, GetChatSwitchInfosRspDTO> context) throws ProxyException {
        LogContextUtils.setFilter1(context.getRequest().getTraceId());
        return chatService.getChatSwitchInfos(context.getRequest());
    }
} 