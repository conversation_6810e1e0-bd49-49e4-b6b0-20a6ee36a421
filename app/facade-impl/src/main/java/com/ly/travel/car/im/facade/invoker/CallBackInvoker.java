package com.ly.travel.car.im.facade.invoker;

import com.ly.sof.gateway.core.Proxy;
import com.ly.sof.gateway.core.ProxyInvoker;
import com.ly.sof.gateway.exception.ProxyException;
import com.ly.sof.gateway.model.GatewayContext;
import com.ly.sof.utils.log.LogContextUtils;
import com.ly.travel.car.im.biz.service.WxSubService;
import com.ly.travel.car.im.facade.Services;
import com.ly.travel.car.im.facade.dto.BaseDTO;
import com.ly.travel.car.im.facade.response.CallBackRspDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Proxy(Services.CALL_BACK_FACADE)
public class CallBackInvoker implements ProxyInvoker<BaseDTO, CallBackRspDTO> {
    @Resource
    private WxSubService wxSubService;

    @Override
    public CallBackRspDTO invoke(GatewayContext<BaseDTO, CallBackRspDTO> context) throws ProxyException {
        LogContextUtils.setFilter1(context.getRequest().getTraceId());
        return wxSubService.callBack(context.getRequest());
    }
}