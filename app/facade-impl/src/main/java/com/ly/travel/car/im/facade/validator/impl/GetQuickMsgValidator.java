package com.ly.travel.car.im.facade.validator.impl;

import com.ly.sof.gateway.core.Proxy;
import com.ly.sof.gateway.core.Validator;
import com.ly.sof.gateway.exception.ValidationException;
import com.ly.travel.car.im.facade.Services;
import com.ly.travel.car.im.facade.request.GetQuickMsgReqDTO;
import com.ly.travel.car.im.facade.validator.BaseValidator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Slf4j
@Service
@Proxy(Services.GET_QUICK_MSG_FACADE)
public class GetQuickMsgValidator extends BaseValidator implements Validator<GetQuickMsgReqDTO> {
    @Override
    public void validate(GetQuickMsgReqDTO req) throws ValidationException {
        dtoNotNullValidate(req, "request");
        req.setTraceId(StringUtils.defaultIfBlank(req.getTraceId(), UUID.randomUUID().toString()));
        dtoNotStringEmptyValidate(req.getTraceId(), "traceId");
        dtoNotNullValidate(req.getSceneType(), "sceneType");
        dtoNotStringEmptyValidate(req.getOrderId(), "orderId");
        dtoNotStringEmptyValidate(req.getProductType(), "productType");
    }
}