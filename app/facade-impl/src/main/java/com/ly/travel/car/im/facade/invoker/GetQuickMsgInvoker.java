package com.ly.travel.car.im.facade.invoker;

import com.ly.sof.gateway.core.Proxy;
import com.ly.sof.gateway.core.ProxyInvoker;
import com.ly.sof.gateway.exception.ProxyException;
import com.ly.sof.gateway.model.GatewayContext;
import com.ly.sof.utils.log.LogContextUtils;
import com.ly.travel.car.im.biz.service.ChatService;
import com.ly.travel.car.im.facade.Services;
import com.ly.travel.car.im.facade.request.GetQuickMsgReqDTO;
import com.ly.travel.car.im.facade.response.GetQuickMsgRspDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Proxy(Services.GET_QUICK_MSG_FACADE)
public class GetQuickMsgInvoker implements ProxyInvoker<GetQuickMsgReqDTO, GetQuickMsgRspDTO> {
    @Resource
    private ChatService chatService;

    @Override
    public GetQuickMsgRspDTO invoke(GatewayContext<GetQuickMsgReqDTO, GetQuickMsgRspDTO> context) throws ProxyException {
        LogContextUtils.setCategory("getQuickMsgList");
        return chatService.getQuickMsgList(context.getRequest());
    }
}