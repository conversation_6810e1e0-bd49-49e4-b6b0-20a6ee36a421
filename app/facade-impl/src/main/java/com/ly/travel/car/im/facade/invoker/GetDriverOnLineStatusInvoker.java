package com.ly.travel.car.im.facade.invoker;

import com.ly.sof.gateway.core.Proxy;
import com.ly.sof.gateway.core.ProxyInvoker;
import com.ly.sof.gateway.exception.ProxyException;
import com.ly.sof.gateway.model.GatewayContext;
import com.ly.sof.utils.log.LogContextUtils;
import com.ly.travel.car.im.biz.service.ChatService;
import com.ly.travel.car.im.facade.Services;
import com.ly.travel.car.im.facade.request.GetDriverOnlineStatusReqDTO;
import com.ly.travel.car.im.facade.response.GetDriverOnlineStatusRspDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Proxy(Services.GET_DRIVER_ONLINE_STATUS_FACADE)
public class GetDriverOnLineStatusInvoker implements ProxyInvoker<GetDriverOnlineStatusReqDTO, GetDriverOnlineStatusRspDTO> {
    @Resource
    private ChatService chatService;

    @Override
    public GetDriverOnlineStatusRspDTO invoke(GatewayContext<GetDriverOnlineStatusReqDTO, GetDriverOnlineStatusRspDTO> context) throws ProxyException {
        LogContextUtils.setFilter1(context.getRequest().getTraceId());
        return chatService.getDriverOnlineStatus(context.getRequest());
    }
}