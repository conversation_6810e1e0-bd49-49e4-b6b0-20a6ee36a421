package com.ly.travel.car.im.facade.invoker;

import com.ly.sof.gateway.core.Proxy;
import com.ly.sof.gateway.core.ProxyInvoker;
import com.ly.sof.gateway.exception.ProxyException;
import com.ly.sof.gateway.model.GatewayContext;
import com.ly.sof.utils.log.LogContextUtils;
import com.ly.travel.car.im.biz.service.ChatService;
import com.ly.travel.car.im.facade.Services;
import com.ly.travel.car.im.facade.request.QueryUnreadMsgReqDTO;
import com.ly.travel.car.im.facade.response.QueryUnreadMsgRspDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Proxy(Services.QUERY_UNREAD_MSG_FACADE)
public class QueryUnreadMsgInvoker implements ProxyInvoker<QueryUnreadMsgReqDTO, QueryUnreadMsgRspDTO> {
    @Resource
    private ChatService chatService;

    @Override
    public QueryUnreadMsgRspDTO invoke(GatewayContext<QueryUnreadMsgReqDTO, QueryUnreadMsgRspDTO> context) throws ProxyException {
        LogContextUtils.setFilter1(context.getRequest().getTraceId());
        return chatService.queryUnreadMsg(context.getRequest());
    }
}