package com.ly.travel.car.im.facade.invoker;

import com.ly.sof.gateway.core.Proxy;
import com.ly.sof.gateway.core.ProxyInvoker;
import com.ly.sof.gateway.exception.ProxyException;
import com.ly.sof.gateway.model.GatewayContext;
import com.ly.sof.utils.log.LogContextUtils;
import com.ly.travel.car.im.biz.service.ChatService;
import com.ly.travel.car.im.facade.Services;
import com.ly.travel.car.im.facade.request.DeleteMsgReqDTO;
import com.ly.travel.car.im.facade.response.DeleteMsgRspDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Proxy(Services.DELETE_MSG_FACADE)
public class DeleteMsgInvoker implements ProxyInvoker<DeleteMsgReqDTO, DeleteMsgRspDTO> {
    @Resource
    private ChatService chatService;

    @Override
    public DeleteMsgRspDTO invoke(GatewayContext<DeleteMsgReqDTO, DeleteMsgRspDTO> context) throws ProxyException {
        LogContextUtils.setFilter1(context.getRequest().getTraceId());
        return chatService.deleteMsg(context.getRequest());
    }
}