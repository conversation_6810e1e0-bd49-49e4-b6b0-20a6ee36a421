package com.ly.travel.car.im.facade.invoker;


import com.ly.sof.gateway.core.Proxy;
import com.ly.sof.gateway.core.ProxyInvoker;
import com.ly.sof.gateway.exception.ProxyException;
import com.ly.sof.gateway.model.GatewayContext;
import com.ly.sof.utils.log.LogContextUtils;
import com.ly.travel.car.im.biz.service.ChatService;
import com.ly.travel.car.im.facade.Services;
import com.ly.travel.car.im.facade.request.QueryIsDriverUnreadMsgReqDTO;
import com.ly.travel.car.im.facade.response.QueryIsDriverUnreadMsgRspDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Proxy(Services.QUERY_IS_DRIVER_UNREAD_MSG_FACADE)
public class QueryIsDriverUnreadMsgInvoker implements ProxyInvoker<QueryIsDriverUnreadMsgReqDTO, QueryIsDriverUnreadMsgRspDTO> {
    @Resource
    private ChatService chatService;

    @Override
    public QueryIsDriverUnreadMsgRspDTO invoke(GatewayContext<QueryIsDriverUnreadMsgReqDTO, QueryIsDriverUnreadMsgRspDTO> context) throws ProxyException {
        LogContextUtils.setFilter1(context.getRequest().getTraceId());
        return chatService.queryIsDriverUnreadMsg(context.getRequest());
    }
}