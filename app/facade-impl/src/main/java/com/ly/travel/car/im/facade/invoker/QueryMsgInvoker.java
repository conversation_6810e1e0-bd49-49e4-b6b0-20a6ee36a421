package com.ly.travel.car.im.facade.invoker;

import com.ly.sof.gateway.core.Proxy;
import com.ly.sof.gateway.core.ProxyInvoker;
import com.ly.sof.gateway.exception.ProxyException;
import com.ly.sof.gateway.model.GatewayContext;
import com.ly.sof.utils.log.LogContextUtils;
import com.ly.travel.car.im.biz.service.ChatService;
import com.ly.travel.car.im.facade.Services;
import com.ly.travel.car.im.facade.request.QueryMsgReqDTO;
import com.ly.travel.car.im.facade.response.QueryMsgRspDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Proxy(Services.QUERY_MSG_FACADE)
public class QueryMsgInvoker implements ProxyInvoker<QueryMsgReqDTO, QueryMsgRspDTO> {
    @Resource
    private ChatService chatService;

    @Override
    public QueryMsgRspDTO invoke(GatewayContext<QueryMsgReqDTO, QueryMsgRspDTO> context) throws ProxyException {
        LogContextUtils.setFilter2(context.getRequest().getTraceId());
        LogContextUtils.setCategory("queryMsgList");
        return chatService.queryMsgList(context.getRequest());
    }
}