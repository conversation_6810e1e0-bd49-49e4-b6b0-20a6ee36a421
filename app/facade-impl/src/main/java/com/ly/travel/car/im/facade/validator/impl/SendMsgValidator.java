package com.ly.travel.car.im.facade.validator.impl;

import com.alibaba.fastjson.JSON;
import com.ly.sof.gateway.core.Proxy;
import com.ly.sof.gateway.core.Validator;
import com.ly.sof.gateway.exception.ValidationException;
import com.ly.travel.car.im.biz.utils.ImUtils;
import com.ly.travel.car.im.facade.Services;
import com.ly.travel.car.im.facade.dto.BaseResponse;
import com.ly.travel.car.im.facade.dto.CheckMemberTokenDto;
import com.ly.travel.car.im.facade.request.SendMsgReqDTO;
import com.ly.travel.car.im.facade.validator.BaseValidator;
import com.ly.travel.car.im.integration.utils.HttpUtils;
import com.ly.travel.car.im.model.config.ImConfigCenter;
import com.ly.travel.car.im.model.dto.DecryptMemberConfigDTO;
import com.ly.travel.car.im.model.enums.OrderChannelEnum;
import com.ly.travel.car.im.model.utils.PlatformUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Objects;
import java.util.UUID;

import static com.ly.travel.car.im.biz.utils.DecryptMemberInfoUtil.getMemberInfo;

@Slf4j
@Service
@Proxy(Services.SEND_MSG_FACADE)
public class SendMsgValidator extends BaseValidator implements Validator<SendMsgReqDTO> {
    @Override
    public void validate(SendMsgReqDTO req) throws ValidationException {
        dtoNotNullValidate(req, "request");
        req.setTraceId(StringUtils.defaultIfBlank(req.getTraceId(), UUID.randomUUID().toString()));
        dtoNotStringEmptyValidate(req.getTraceId(), "traceId");
        dtoNotStringEmptyValidate(req.getSupplierCode(), "supplierCode");
        dtoNotStringEmptyValidate(req.getSupplierOrderId(), "supplierOrderId");
        dtoNotStringEmptyValidate(req.getDriverId(), "driverId");
        String imKey = req.getImKey();
        dtoNotStringEmptyValidate(imKey, "imKey");
        dtoNotStringEmptyValidate(req.getOrderId(), "orderId");
        dtoNotStringEmptyValidate(req.getDriverNickName(), "driverNickName");
        dtoNotNullValidate(req.getMsgType(), "msgType");
        dtoNotNullValidate(req.getMsgContent(), "msgContent");
        dtoNotStringEmptyValidate(req.getUnionId(), req.getMemberId(), "unionId & memberId");

        if (StringUtils.equalsIgnoreCase("null", imKey)) {
            throw new ValidationException(ERROR_FACTORY.paramNull("imKey"));
        }

        //memberId 解密

        String memberId = req.getMemberId();
        String platform = req.getPlatform();
        DecryptMemberConfigDTO decryptMemberConfig = ImConfigCenter.getDecryptMemberConfig();
        if (Objects.nonNull(req.getPlatId())
                && CollectionUtils.isNotEmpty(Objects.requireNonNull(decryptMemberConfig).getPlateIds())
                && decryptMemberConfig.getPlateIds().contains(req.getPlatId())) {
            BaseResponse checkMemberTokenDto = getMemberInfo(memberId, decryptMemberConfig);
            memberId = checkMemberTokenDto.getData().getMemberId();
            if (StringUtils.equals(imKey, memberId)) {
                imKey = memberId;
                req.setImKey(imKey);
            }
            req.setUnionId(null);
            req.setMemberId(memberId);
        } else if (Objects.nonNull(req.getPlatId())
                && OrderChannelEnum.RIDE.getCode() == req.getPlatId()) {
            memberId = StringUtils.isEmpty(memberId) ? null : PlatformUtils.memberIdDecrypt(memberId);
            req.setMemberId(memberId);
        } else if (StringUtils.isNotBlank(memberId) && PlatformUtils.isApp(platform)) {
            if (!memberId.matches("\\d+")) {
                memberId = PlatformUtils.handlerMemberId(memberId, platform);
            }
            if (StringUtils.equals(imKey, memberId)) {
                imKey = memberId;
                req.setImKey(imKey);
            }
            req.setUnionId(null);
            req.setMemberId(memberId);
        }



        if (Objects.isNull(req.getPlatId()) || req.getPlatId() == 0) {
            if (StringUtils.isNotEmpty(req.getPlatform())) {
                if (PlatformUtils.isWX(req.getPlatform())) {
                    req.setPlatId(OrderChannelEnum.XCX.getCode());
                }

                if (PlatformUtils.isApp(req.getPlatform())) {
                    req.setPlatId(OrderChannelEnum.IOS_APP.getCode());
                }
            } else {
                if (StringUtils.isNotEmpty(req.getUnionId())) {
                    req.setPlatId(OrderChannelEnum.XCX.getCode());
                } else  {
                    req.setPlatId(OrderChannelEnum.IOS_APP.getCode());
                }
            }
        }

        //组装sessionKey标识
        req.setSessionKey(ImUtils.buildSessionKey(req.getSupplierCode(), imKey, req.getDriverId()));
        req.setMsgSendTime(new Date());
    }

}
