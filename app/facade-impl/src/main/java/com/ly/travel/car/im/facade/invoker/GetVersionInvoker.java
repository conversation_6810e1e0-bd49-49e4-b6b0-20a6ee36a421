package com.ly.travel.car.im.facade.invoker;

import com.ly.sof.gateway.core.Proxy;
import com.ly.sof.gateway.core.ProxyInvoker;
import com.ly.sof.gateway.exception.ProxyException;
import com.ly.sof.gateway.model.GatewayContext;
import com.ly.sof.utils.log.LogContextUtils;
import com.ly.travel.car.im.biz.service.ChatService;
import com.ly.travel.car.im.facade.Services;
import com.ly.travel.car.im.facade.request.GetVersionReqDTO;
import com.ly.travel.car.im.facade.response.GetVersionRspDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Proxy(Services.GET_VERSION_FACADE)
public class GetVersionInvoker implements ProxyInvoker<GetVersionReqDTO, GetVersionRspDTO> {
    @Resource
    private ChatService chatService;

    @Override
    public GetVersionRspDTO invoke(GatewayContext<GetVersionReqDTO, GetVersionRspDTO> context) throws ProxyException {
        LogContextUtils.setFilter2(context.getRequest().getTraceId());
        LogContextUtils.setCategory("getVersion");
        return chatService.getVersion(context.getRequest());
    }
}