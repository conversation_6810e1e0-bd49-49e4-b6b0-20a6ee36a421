<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:p="http://www.springframework.org/schema/p"
	xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
	xmlns:sof="http://schema.ly.com/schema/sof"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
         http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
         http://schema.ly.com/schema/sof http://schema.ly.com/schema/sof.xsd
         http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd"
	default-autowire="byName">

	<!-- im包扫描 -->
	<context:component-scan base-package="com.ly.travel.car.im,com.ly.travel.shared.mobility.supply.crm.client"/>
</beans>
