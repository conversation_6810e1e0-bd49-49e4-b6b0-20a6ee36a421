<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:sof="http://schema.ly.com/schema/sof"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
         http://schema.ly.com/schema/sof http://schema.ly.com/schema/sof.xsd"
       default-autowire="byName">

    <sof:consumer id="chatListenerConsumer" group="${chat.mq.consumer.group}"
                  nameSrvAddress="${mq.nameSrvAddress}" consumeThreadMax="16" consumeThreadMin="4"
                  pullThresholdForQueue="1024">
        <sof:listener ref="chatListener"/>
        <sof:channels>
            <sof:channel topic="${chat.mq.topic}">
                <sof:event eventCode="*"/>
            </sof:channel>
        </sof:channels>
    </sof:consumer>
    
    <sof:consumer id="chatDispatchListenerConsumer" group="${chat.dispatch.mq.consumer.group}"
                  nameSrvAddress="${mq.nameSrvAddress}" consumeThreadMax="16" consumeThreadMin="4"
                  pullThresholdForQueue="1024">
        <sof:listener ref="msgDispatchListener"/>
        <sof:channels>
            <sof:channel topic="${chat.dispatch.mq.topic}">
                <sof:event eventCode="im_chat_message_dispatch"/>
            </sof:channel>
        </sof:channels>
    </sof:consumer>
</beans>