<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:sof="http://schema.ly.com/schema/sof"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
         http://schema.ly.com/schema/sof http://schema.ly.com/schema/sof.xsd"
       default-autowire="byName">

    <bean id="producer" class="com.ly.sof.api.mq.producer.DefaultProducer">
        <property name="uniformEventPublisher" ref="uniformEventPublisher"/>
    </bean>

    <sof:publisher id="uniformEventPublisher" group="${chat.mq.producer.group}"
                   nameSrvAddress="${mq.nameSrvAddress}"/>

</beans>
