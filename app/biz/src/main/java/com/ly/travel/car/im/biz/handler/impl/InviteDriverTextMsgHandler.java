package com.ly.travel.car.im.biz.handler.impl;

import com.ly.sof.facade.base.BaseResponseDTO;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.im.biz.error.ValidateException;
import com.ly.travel.car.im.biz.handler.BaseTools;
import com.ly.travel.car.im.biz.handler.MsgHandler;
import com.ly.travel.car.im.biz.service.SendService;
import com.ly.travel.car.im.dal.dao.SfcImMsgRecordDao;
import com.ly.travel.car.im.dal.entity.SfcImMsgRecord;
import com.ly.travel.car.im.facade.request.SendMsgReqDTO;
import com.ly.travel.car.im.facade.response.SendMsgRspDTO;
import com.ly.travel.car.im.model.dto.OrderDetailDTO;
import com.ly.travel.car.im.model.enums.MsgSendStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * 邀请司机文本消息处理器
 */
@Service
@Slf4j
public class InviteDriverTextMsgHandler extends BaseTools implements MsgHandler {
    @Resource
    private SendService sendService;
    @Resource
    private SfcImMsgRecordDao sfcImMsgRecordDao;

    @Override
    public SendMsgRspDTO handle(SendMsgReqDTO req, OrderDetailDTO orderDetail) throws ValidateException {
        //组装消息存储入参
        SfcImMsgRecord msgRecord = buildMsg(req, orderDetail);
        msgRecord.setOrderStatus(orderDetail.getStatus());
        req.setMemberId(orderDetail.getMemberId());
        req.setUnionId(orderDetail.getUnionId());

        //消息发送
        BaseResponseDTO baseResponseDTO = sendService.sendMsg(req, orderDetail);
        if (Objects.nonNull(baseResponseDTO)) {
            msgRecord.setMsgSendStatus(MsgSendStatusEnum.SUCCESS.getCode());
        }

        msgRecord.setUpdateTime(new Date());
        sfcImMsgRecordDao.save(msgRecord);

        //保存或更新会话
        insertOrUpdateSfcImInfo(req, getSfcImInfo(req.getSessionKey(),req.getPlatId()), msgRecord,req.getPlatId());

        LoggerUtils.info(log, "[InviteDriverTextMsgHandler][{}] 消息发送成功，邀请司机文本消息：{}", req.filter1(), FastJsonUtils.toJSONString(msgRecord));
        return SendMsgRspDTO.success(req.getTraceId(), req.getMsgContent().getMsgId());
    }
}
