package com.ly.travel.car.im.biz.service;

import com.ly.travel.car.im.facade.request.*;
import com.ly.travel.car.im.facade.response.*;

public interface ChatService {

    /**
     * 乘客消息发送司机
     */
    SendMsgRspDTO sendMsg(SendMsgReqDTO req);

    /**
     * 查询用户消息列表
     */
    QueryMsgRspDTO queryMsgList(QueryMsgReqDTO req);

    /**
     * 查询用户聊天记录
     */
    QueryMsgRecordRspDTO queryMsgRecord(QueryMsgRecordReqDTO req);

    /**
     * 获取消息快捷语
     */
    GetQuickMsgRspDTO getQuickMsgList(GetQuickMsgReqDTO req);

    /**
     * 获取司机在离线状态
     */
    GetDriverOnlineStatusRspDTO getDriverOnlineStatus(GetDriverOnlineStatusReqDTO req);

    /**
     * 上报用户状态
     */
    UploadUserStatusRspDTO uploadUserStatus(UploadUserStatusReqDTO req);

    /**
     * 能否发送消息检查
     */
    CheckAllowSendMsgRspDTO checkAllowSendMsg(CheckAllowSendMsgReqDTO req);

    /**
     * 删除消息列表
     */
    DeleteMsgRspDTO deleteMsg(DeleteMsgReqDTO req);

    /**
     * 查询未读消息列表&未读消息数量
     */
    QueryUnreadMsgRspDTO queryUnreadMsg(QueryUnreadMsgReqDTO req);

    /**
     * 查询未读消息数量
     */
    QueryUnreadMsgCountRspDTO queryUnreadMsgCount(QueryUnreadMsgCountReqDTO req);

    /**
     * 查询是否有司机未读消息
     */
    QueryIsDriverUnreadMsgRspDTO queryIsDriverUnreadMsg(QueryIsDriverUnreadMsgReqDTO req);

    /**
     * 获取版本
     */
    GetVersionRspDTO getVersion(GetVersionReqDTO req);

    /**
     * 获取imKeyInfos
     */
    GetImKeyInfosRspDTO getImKeyInfos(GetImKeyInfosReqDTO req);

    QueryMsgRecordRspDTO queryInnerMsgRecord(QueryMsgRecordReqDTO request);


    /**
     * 获取聊天开关信息
     */
    GetChatSwitchInfosRspDTO getChatSwitchInfos(GetChatSwitchInfosReqDTO req);
}
