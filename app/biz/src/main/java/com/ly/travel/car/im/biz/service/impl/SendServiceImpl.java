package com.ly.travel.car.im.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.car.bean.Simple;
import com.ly.sof.api.error.LYError;
import com.ly.sof.facade.base.BaseResponseDTO;
import com.ly.sof.utils.log.LogContextUtils;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.im.biz.error.BizErrorFactory;
import com.ly.travel.car.im.biz.error.ValidateException;
import com.ly.travel.car.im.biz.service.SendService;
import com.ly.travel.car.im.biz.utils.MD5;
import com.ly.travel.car.im.dal.dao.SfcImInfoDao;
import com.ly.travel.car.im.dal.dao.SfcImMsgRecordDao;
import com.ly.travel.car.im.dal.entity.SfcImInfo;
import com.ly.travel.car.im.dal.entity.SfcImMsgRecord;
import com.ly.travel.car.im.facade.dto.MsgContentDTO;
import com.ly.travel.car.im.facade.enums.MsgSenderEnum;
import com.ly.travel.car.im.facade.enums.MsgTypeEnum;
import com.ly.travel.car.im.facade.request.SendMsgReqDTO;
import com.ly.travel.car.im.integration.client.api.IdGeneratorApi;
import com.ly.travel.car.im.integration.client.api.PushApi;
import com.ly.travel.car.im.integration.client.chat.ImChatClient;
import com.ly.travel.car.im.integration.client.redis.impl.RedisClientProxy;
import com.ly.travel.car.im.integration.request.SafeNightWarnMsgReqDTO;
import com.ly.travel.car.im.integration.request.SafeWarnMsgReqDTO;
import com.ly.travel.car.im.integration.throwable.exception.IntegrationException;
import com.ly.travel.car.im.integration.utils.HttpUtils;
import com.ly.travel.car.im.integration.utils.RedisKeyBuilder;
import com.ly.travel.car.im.model.config.ImConfigCenter;
import com.ly.travel.car.im.model.constant.MsgTipsConstant;
import com.ly.travel.car.im.model.dto.OrderDetailDTO;
import com.ly.travel.car.im.model.enums.MsgSendStatusEnum;
import com.ly.travel.car.im.model.enums.MsgStatusEnum;
import com.ly.travel.car.im.model.enums.SystemMsgErrorMappingEnum;
import com.ly.travel.car.tradecore.model.enums.OrderState;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.driverOnlineStatus.DriverOnlineStatusRequest;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.driverOnlineStatus.DriverOnlineStatusResponse;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.imClose.ImCloseRequest;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.imClose.ImCloseResponse;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.imCreate.ImCreateRequest;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.imCreate.ImCreateResponse;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.sendMsg.SendMsgRequest;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.sendMsg.SendMsgResponse;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.uploadUserStatus.UploadUserStatusRequest;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.uploadUserStatus.UploadUserStatusResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;
import java.util.UUID;

import static com.ly.travel.car.im.biz.handler.BaseTools.MSG_VERSION;


@Service
@Slf4j
public class SendServiceImpl implements SendService {

    private static final BizErrorFactory BIZ_ERROR_FACTORY = BizErrorFactory.getInstance();
    @Resource
    private ImChatClient imChatClient;
    @Resource
    private SfcImInfoDao sfcImInfoDao;
    private static final String YueYue = "YueYue";
    @Resource
    private IdGeneratorApi idGeneratorApi;

    @Resource
    private RedisClientProxy redisClientProxy;

    @Resource
    private SfcImMsgRecordDao sfcImMsgRecordDao;

    @Resource
    private PushApi pushApi;

    @Override
    public BaseResponseDTO sendMsg(SendMsgReqDTO req, OrderDetailDTO orderDetail) throws ValidateException {
        LogContextUtils.setCategory("sendMsg");

        String orderId = orderDetail.getOrderId();

        LogContextUtils.setFilter1(orderId);

        SendMsgRequest sendMsgRequest = new SendMsgRequest();
        Integer msgType = req.getMsgType();
        sendMsgRequest.setMsgType(msgType);
        sendMsgRequest.setMsgId(req.getMsgId());

        String supplierCode = req.getSupplierCode();
        LoggerUtils.info(log, "req = {}，orderDetail = {}", FastJsonUtils.toJSONString(req), FastJsonUtils.toJSONString(orderDetail));

        //供应商特殊处理
        handleSupplier(req, orderDetail, sendMsgRequest);

        //已读特殊处理
        if (Objects.equals(MsgTypeEnum.READ_ACK.getMsgType(), msgType)) {
            sendMsgRequest.setContent(req.getReadContent());
        } else {
            sendMsgRequest.setContent(req.content());
        }

        sendMsgRequest.setDriverId(req.getDriverId());
        sendMsgRequest.setTraceId(req.getTraceId());
        sendMsgRequest.setPhoneNo(orderDetail.getPassengerPhone());
        sendMsgRequest.setSupplierSerialNo(req.getSupplierOrderId());
        sendMsgRequest.setTcSerialNo(orderDetail.getBusinessOrderNo());
        sendMsgRequest.setSupplierCode(supplierCode);
        sendMsgRequest.setTcSubSerialNo(orderDetail.getBusinessOrderNo());
        sendMsgRequest.setPassengerId(Long.parseLong(MD5.encrypt(orderDetail.getMemberId()).substring(0, 10), 16) + Strings.EMPTY);
        sendMsgRequest.setMemberId(orderDetail.getMemberId());
        sendMsgRequest.setSceneType(1);
        try {
            LoggerUtils.info(log, "乘客消息发送司机 req：{}", FastJsonUtils.toJSONString(sendMsgRequest));
            SendMsgResponse sendMsgResponse = imChatClient.sendMsg(sendMsgRequest);
            LoggerUtils.info(log, "乘客消息发送司机 rsp：{}", FastJsonUtils.toJSONString(sendMsgResponse));
            if (Objects.nonNull(sendMsgResponse) && sendMsgResponse.isSuccess()) {
                return sendMsgResponse;
            }
        } catch (IntegrationException e) {
            // 在日志中记录错误码和错误消息
            LoggerUtils.warn(log, "乘客消息发送司机异常 req：{}, errorCode: {}, errorMessage: {}",
                    FastJsonUtils.toJSONString(sendMsgRequest),
                    e.getErrorCode(),
                    e.getError().getMessage(),
                    e);
            if (!Objects.equals(MsgTypeEnum.READ_ACK.getMsgType(), msgType)) {
                SfcImMsgRecord sfcImMsgRecord = saveRecord(req, orderDetail, e.getError().getMessage());
                pushSystemMsg(buildSysMsgPush(req, req.getSessionKey(), e.getError().getMessage(),sfcImMsgRecord));

            }
        }
        return null;
    }

    private SfcImMsgRecord saveRecord(SendMsgReqDTO req, OrderDetailDTO orderDetail, String content) {
        SfcImMsgRecord sfcImMsgRecord = new SfcImMsgRecord();
        BeanUtils.copyProperties(req, sfcImMsgRecord);
        sfcImMsgRecord.setSessionKey(req.getSessionKey());
        sfcImMsgRecord.setMsgId(UUID.randomUUID().toString().replace("-", Strings.EMPTY));
        sfcImMsgRecord.setMsgSender(MsgSenderEnum.SYS.getCode());
        sfcImMsgRecord.setMemberId(StringUtils.isEmpty(orderDetail.getMemberId()) ? 0 : Long.parseLong(orderDetail.getMemberId()));
        sfcImMsgRecord.setMsgType(MsgTypeEnum.SYSTEM.getMsgType());
        sfcImMsgRecord.setSceneType(req.getSceneType());
        sfcImMsgRecord.setMsgStatus(MsgStatusEnum.READ.getCode());
        sfcImMsgRecord.setMsgContent(SystemMsgErrorMappingEnum.mapErrorMessage(content));
        sfcImMsgRecord.setMsgSendStatus(MsgSendStatusEnum.SUCCESS.getCode());
        sfcImMsgRecord.setMsgVersion(MSG_VERSION);
        sfcImMsgRecord.setOrderStatus(orderDetail.getStatus());
        sfcImMsgRecord.setUpdateTime(new Date());
        sfcImMsgRecord.setOrderStatus(orderDetail.getStatus());
        sfcImMsgRecordDao.save(sfcImMsgRecord);
        return sfcImMsgRecord;
    }

    private SafeNightWarnMsgReqDTO buildSysMsgPush(SendMsgReqDTO sendMsgReqDTO, String sessionKey, String content,SfcImMsgRecord sfcImMsgRecord) {
        MsgContentDTO msgContentDTO = new MsgContentDTO();
        msgContentDTO.setMsgId(sendMsgReqDTO.getMsgId());
        msgContentDTO.setMsgType(MsgTypeEnum.SYSTEM.getMsgType());
        
        // 使用枚举类映射错误消息
        msgContentDTO.setContent(sfcImMsgRecord.getMsgContent());
        SafeNightWarnMsgReqDTO req = new SafeNightWarnMsgReqDTO();
        req.setMsgId(sfcImMsgRecord.getMsgId());
        req.setMsgContent(msgContentDTO);
        req.setPlateNumber(sendMsgReqDTO.getPlateNumber());
        req.setSupplierCode(sendMsgReqDTO.getSupplierCode());
        req.setDriverId(sendMsgReqDTO.getDriverId());
        req.setOrderId(sendMsgReqDTO.getOrderId());
        req.setSupplierOrderId(sendMsgReqDTO.getSupplierOrderId());
        req.setUnionId(sendMsgReqDTO.getUnionId());
        req.setOpenId(Strings.EMPTY);
        req.setMemberId(StringUtils.isEmpty(sendMsgReqDTO.getMemberId()) ? "0" : sendMsgReqDTO.getMemberId());
        req.setImKey(req.filter1());
        req.setMsgType(MsgTypeEnum.SYSTEM.getMsgType());
        req.setSessionKey(sessionKey);
        return req;
    }

    private void handleSupplier(SendMsgReqDTO req, OrderDetailDTO orderDetail, SendMsgRequest sendMsgRequest) {
        String supplierConversationId = Strings.EMPTY;
        String tcConversationId = Strings.EMPTY;
        String orderId = orderDetail.getOrderId();
        String sessionKey = req.getSessionKey();

        //约约特殊处理
        if (req.getSupplierCode().contains(YueYue)) {
            SfcImInfo sfcImInfo = sfcImInfoDao.findSfcImInfo(sessionKey, orderId);
            if (Objects.nonNull(sfcImInfo)) {
                //完单
                if (Objects.equals(orderDetail.getStatus(), OrderState.ORDER_CLOSED)) {
                    tcConversationId = idGeneratorApi.getTcId() + Strings.EMPTY;
                    sfcImInfo.setTcSessionId(tcConversationId);
                    supplierConversationId = createSession(req, orderDetail, tcConversationId);
                    sfcImInfo.setSupplierSessionId(supplierConversationId);
                } else {
                    String tcSessionId = sfcImInfo.getTcSessionId();
                    tcConversationId = StringUtils.isBlank(tcSessionId) ? idGeneratorApi.getTcId() + Strings.EMPTY : tcSessionId;
                    sfcImInfo.setTcSessionId(tcConversationId);
                    String supplierSessionId = sfcImInfo.getSupplierSessionId();
                    supplierConversationId = StringUtils.isBlank(supplierSessionId) ? createSession(req, orderDetail, tcConversationId) : supplierSessionId;
                    sfcImInfo.setSupplierSessionId(supplierConversationId);
                }
                sfcImInfoDao.update(sfcImInfo, new LambdaQueryWrapper<SfcImInfo>().eq(SfcImInfo::getSessionKey, sessionKey).eq(SfcImInfo::getOrderId, orderId));
            } else {
                long startTime = System.currentTimeMillis();
                if (ImConfigCenter.isOpenChat()){
                    sfcImInfo = new SfcImInfo();
                    String tcId = idGeneratorApi.getTcId() + Strings.EMPTY;
                    sfcImInfo.setTcSessionId(tcId);
                    sfcImInfo.setSupplierCode(orderDetail.getSupplierCode());
                    tcConversationId = idGeneratorApi.getTcId() + Strings.EMPTY;
                    sfcImInfo.setTcSessionId(tcConversationId);
                    supplierConversationId = createSession(req, orderDetail, tcConversationId);
                    sfcImInfo.setSupplierSessionId(supplierConversationId);
                    sfcImInfo.setSupplierOrderId(req.getSupplierOrderId());
                    sfcImInfo.setSessionKey(sessionKey);
                    sfcImInfo.setChatStatus(0);
                    sfcImInfo.setOrderId(orderDetail.getOrderId());
                    sfcImInfo.setAllOrderIds(orderDetail.getOrderId());
                    if (StringUtils.isNotEmpty(orderDetail.getDriverName())){
                        sfcImInfo.setDriverNickName(orderDetail.getDriverName());
                    }
                    sfcImInfo.setPlateNumber(orderDetail.getPlateNumber());
                    sfcImInfo.setUnionId(orderDetail.getUnionId());
                    sfcImInfo.setDriverId(req.getDriverId());
                    sfcImInfo.setPlateId(orderDetail.getPlatId());
                    sfcImInfo.setMemberId(Long.parseLong(orderDetail.getMemberId()));
                    sfcImInfoDao.save(sfcImInfo);
                    redisClientProxy.setnx(RedisKeyBuilder.IM_INFO_KEY + sessionKey, FastJsonUtils.toJSONString(sfcImInfo), 7200);
                }
                LoggerUtils.warn(log, "会话为空，未获取到约约会话，消息首次创建会话，rsp：{}，耗时：{}毫秒",
                        FastJsonUtils.toJSONString(sfcImInfo), System.currentTimeMillis() - startTime);
            }
            sendMsgRequest.setMsgId(req.getMsgId().substring(0, 32));
            sendMsgRequest.setTcConversationId(tcConversationId);
            sendMsgRequest.setSupplierConversationId(supplierConversationId);
        }
    }

    @Override
    public UploadUserStatusResponse uploadUserStatus(UploadUserStatusRequest req) {
        try {
            LoggerUtils.info(log, "[uploadUserStatus] 上报用户状态 req：{}", FastJsonUtils.toJSONString(req));
            UploadUserStatusResponse uploadUserStatusResponse = imChatClient.uploadUserStatus(req);
            LoggerUtils.info(log, "[uploadUserStatus] 上报用户状态 rsp：{}", FastJsonUtils.toJSONString(uploadUserStatusResponse));
            if (Objects.nonNull(uploadUserStatusResponse) && uploadUserStatusResponse.isSuccess()) {
                return uploadUserStatusResponse;
            }
        } catch (IntegrationException e) {
            LoggerUtils.error(log, "[uploadUserStatus] 上报用户状态异常 req：{}", FastJsonUtils.toJSONString(req), e);
        }
        return null;
    }

    @Override
    public DriverOnlineStatusResponse getDriverOnlineStatus(DriverOnlineStatusRequest req) {
        try {
            LoggerUtils.info(log, "[getDriverOnlineStatus] 获取司机在离线状态 req：{}", FastJsonUtils.toJSONString(req));
            DriverOnlineStatusResponse driverOnlineStatus = imChatClient.getDriverOnlineStatus(req);
            LoggerUtils.info(log, "[getDriverOnlineStatus] 获取司机在离线状态 rsp：{}", FastJsonUtils.toJSONString(driverOnlineStatus));
            if (Objects.nonNull(driverOnlineStatus) && driverOnlineStatus.isSuccess()) {
                return driverOnlineStatus;
            }
        } catch (IntegrationException e) {
            LoggerUtils.error(log, "[getDriverOnlineStatus] 获取司机在线状态 req：{}", FastJsonUtils.toJSONString(req), e);
        }
        return null;
    }

    @Override
    public ImCreateResponse openChatAction(ImCreateRequest req) {
        try {
            LoggerUtils.info(log, "[openChatAction] 约约创建会话 req：{}", FastJsonUtils.toJSONString(req));
            ImCreateResponse imCreateResponse = imChatClient.imCreate(req);
            LoggerUtils.info(log, "[openChatAction] 约约创建会话 rsp：{}", FastJsonUtils.toJSONString(imCreateResponse));
            if (Objects.nonNull(imCreateResponse) && imCreateResponse.isSuccess()) {
                return imCreateResponse;
            }
        } catch (IntegrationException e) {
            LoggerUtils.error(log, "[openChatAction] 约约创建会话异常 req：{}", FastJsonUtils.toJSONString(req), e);
        }
        return null;
    }

    @Override
    public ImCloseResponse closeChatAction(ImCloseRequest req) {
        try {
            LoggerUtils.info(log, "[closeChatAction] 约约关闭会话 req：{}", FastJsonUtils.toJSONString(req));
            ImCloseResponse imCloseResponse = imChatClient.imClose(req);
            LoggerUtils.info(log, "[closeChatAction] 约约关闭会话 rsp：{}", FastJsonUtils.toJSONString(req));
            if (Objects.nonNull(imCloseResponse) && imCloseResponse.isSuccess()) {
                return imCloseResponse;
            }
        } catch (IntegrationException e) {
            LoggerUtils.error(log, "[closeChatAction] 约约关闭会话异常 req：{}", FastJsonUtils.toJSONString(req), e);
        }
        return null;
    }

    private String createSession(SendMsgReqDTO req, OrderDetailDTO orderDetail, String tcConversationId) {
        ImCreateRequest imCreateRequest = new ImCreateRequest();
        imCreateRequest.setDriverId(req.getDriverId());
        imCreateRequest.setSupplierSerialNo(req.getSupplierOrderId());
        imCreateRequest.setPassengerId(Long.parseLong(MD5.encrypt(orderDetail.getMemberId()).substring(0, 10), 16) + Strings.EMPTY);
        imCreateRequest.setTcSubSerialNo(orderDetail.getBusinessOrderNo());
        String orderId = req.getOrderId();
        imCreateRequest.setTcSerialNo(orderId);
        imCreateRequest.setMemberId(req.getMemberId());
        imCreateRequest.setSupplierCode(req.getSupplierCode());
        imCreateRequest.setTraceId(req.getTraceId());
        imCreateRequest.setTcConversationId(tcConversationId);
        try {
            LoggerUtils.info(log, "[createSession][{}] 约约创建会话 req：{}", orderId, FastJsonUtils.toJSONString(imCreateRequest));
            ImCreateResponse imCreateResponse = openChatAction(imCreateRequest);
            LoggerUtils.info(log, "[createSession][{}] 约约创建会话 rsp：{}", orderId, FastJsonUtils.toJSONString(imCreateResponse));
            if (Objects.nonNull(imCreateResponse) && imCreateResponse.isSuccess()) {
                return imCreateResponse.getSupplierConversationId();
            }
        } catch (Exception e) {
            LoggerUtils.error(log, "[createSession][{}] 约约创建会话异常 req：{}", orderId, FastJsonUtils.toJSONString(req), e);
        }
        return Strings.EMPTY;
    }

    private void pushSystemMsg(SafeNightWarnMsgReqDTO msgReqDTO) {
        // 推送消息到当前会话
        try {
            Simple<?> currentSimple = pushApi.safeNightMsgPush(msgReqDTO);
            if (Objects.nonNull(currentSimple) && Objects.equals(currentSimple.getStatus(), Simple.OK.getStatus())) {
                LoggerUtils.info(log, "[pushToMultipleSessions] 当前会话推送成功，sessionKey：{}，msgId：{}",
                        msgReqDTO.getSessionKey(), msgReqDTO.getMsgId());
            } else {
                LoggerUtils.warn(log, "[pushToMultipleSessions] 当前会话推送失败，sessionKey：{}，msgId：{}，响应：{}",
                        msgReqDTO.getSessionKey(), msgReqDTO.getMsgId(), FastJsonUtils.toJSONString(currentSimple));
            }
        } catch (Exception e) {
            LoggerUtils.error(log, "[pushToMultipleSessions] 当前会话推送异常，sessionKey：{}，msgId：{}",
                    msgReqDTO.getSessionKey(), msgReqDTO.getMsgId(), e);
        }
    }
}
