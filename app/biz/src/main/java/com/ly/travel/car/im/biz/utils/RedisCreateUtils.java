package com.ly.travel.car.im.biz.utils;

import com.alibaba.fastjson2.JSON;
import com.ly.tcbase.config.ConfigCenterClient;
import lombok.Data;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.ClusterServersConfig;
import org.redisson.config.Config;

import java.util.LinkedList;
import java.util.List;

public class RedisCreateUtils {


    public static RedissonClient redissonClient(String redisName) throws Exception {

        String s = ConfigCenterClient.get("TCBase.Cache.v2");

        List<CacheConfig> cacheConfigList = JSON.parseArray(s, CacheConfig.class);

        // proxy mode
        CacheConfig typeSConfig = cacheConfigList.stream()
                .filter(config -> config.getName().equals(redisName))
                .filter(config -> config.getType().equals("S"))
                .findFirst().orElse(null);
        if (typeSConfig != null) {
            return doCreateRedissonClient(typeSConfig);
        }

        // ip mode type c
        CacheConfig typeCConfig = cacheConfigList.stream()
                .filter(config -> config.getName().equals(redisName))
                .filter(config -> config.getType().equals("C"))
                .findFirst().orElse(null);

        if (typeCConfig != null) {
            return doCreateRedissonClient(typeCConfig);
        }
        // error
        return null;
    }

    private static RedissonClient doCreateRedissonClient(CacheConfig cacheConfig) throws Exception {
        Config config = new Config();
        ClusterServersConfig clusterServersConfig = config.useClusterServers();
        cacheConfig.instances.stream()
                .map(CacheConfig.Node::getIp).map(ip -> "redis://" + ip)
                .forEach(clusterServersConfig::addNodeAddress);
        clusterServersConfig.setMasterConnectionMinimumIdleSize(2);
        clusterServersConfig.setMasterConnectionPoolSize(12);

        clusterServersConfig.setSlaveConnectionMinimumIdleSize(2);
        clusterServersConfig.setSlaveConnectionPoolSize(12);

        clusterServersConfig.setPassword(cacheConfig.instances.get(0).password);

        return Redisson.create(config);
    }

    @Data
    public static class CacheConfig {
        private List<Node> instances = new LinkedList<>();
        private String name;
        private String type;

        @Data
        public static class Node {
            private String ip;
            private String password;
            private String sentinel;
        }
    }
}
