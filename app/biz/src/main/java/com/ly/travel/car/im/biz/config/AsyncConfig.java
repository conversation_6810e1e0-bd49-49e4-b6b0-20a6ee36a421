package com.ly.travel.car.im.biz.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Configuration
@EnableAsync
public class AsyncConfig {

    @Bean(name = "mqSendExecutor")
    public ExecutorService mqSendExecutor() {
        return new ThreadPoolExecutor(
            5,
            10,
            60L,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(1000),
            r -> new Thread(r, "mq-send-thread"),
            new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }

    @Bean(name = "ReadMsgSendExecutor")
    public ExecutorService readMsgSendExecutor() {
        return new ThreadPoolExecutor(
                5,
                10,
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1000),
                r -> new Thread(r, "mq-send-thread"),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }
} 