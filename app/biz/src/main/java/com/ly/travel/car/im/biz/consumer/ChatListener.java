package com.ly.travel.car.im.biz.consumer;

import com.ly.sof.api.exception.MQException;
import com.ly.sof.api.mq.common.UniformEvent;
import com.ly.sof.api.mq.common.UniformEventContext;
import com.ly.sof.api.mq.consumer.UniformEventMessageListener;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.travel.car.im.model.enums.ChatMsgTagEnum;
import com.ly.travel.car.im.model.utils.MQUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

@Service("chatListener")
@Slf4j
public class ChatListener implements UniformEventMessageListener {
    @Resource
    private ChatMsgServiceFactory chatMsgServiceFactory;

    @Override
    public boolean onUniformEvent(UniformEvent uniformEvent, UniformEventContext uniformEventContext) throws MQException {
        String msgBody = MQUtils.getMsgBody(uniformEvent);
        if (StringUtils.isBlank(msgBody)) {
            LoggerUtils.warn(log, "[onUniformEvent] 消息体为空");
            return Boolean.TRUE;
        }

        LoggerUtils.info(log, "[onUniformEvent] 请求参数：{}", msgBody);
        SmsListener smsListener = chatMsgServiceFactory.getService(ChatMsgTagEnum.fromName(uniformEvent.getEventCode()));
        if (Objects.isNull(smsListener)) {
            LoggerUtils.warn(log, "[onUniformEvent] tag：{} 未找到对应的处理器", uniformEvent.getEventCode());
            return Boolean.TRUE;
        }

        boolean handle = smsListener.handle(msgBody);
        LoggerUtils.info(log, "[onUniformEvent] 处理结果：{}", handle);

        return handle;
    }

    @Override
    public ListenerTypeEnum getListenerType() {
        return ListenerTypeEnum.CONCURRENTLY;
    }
}
