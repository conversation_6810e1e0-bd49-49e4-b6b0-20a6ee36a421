package com.ly.travel.car.im.biz.utils;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.travel.car.im.facade.dto.BaseResponse;
import com.ly.travel.car.im.facade.dto.CheckMemberTokenDto;
import com.ly.travel.car.im.facade.dto.MemberInfoDTO;
import com.ly.travel.car.im.facade.dto.ThirdPartyWechatBaseResponse;
import com.ly.travel.car.im.integration.utils.HttpUtils;
import com.ly.travel.car.im.model.config.ImConfigCenter;
import com.ly.travel.car.im.model.dto.DecryptMemberConfigDTO;
import com.ly.travel.car.im.model.dto.ThirdPartyConfigDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Objects;

@Slf4j
public class DecryptMemberInfoUtil {

    private static final Integer SESSION_KEY_LENGTH = 3;

    public static BaseResponse getMemberInfo(String memberId, DecryptMemberConfigDTO configDTO) {
        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("appKey", configDTO.getAppKey());
        headerMap.put("appSecret", configDTO.getAppSecret());
        String s = HttpUtils.post(configDTO.getCheckMemberTokenUrl(), headerMap, JSON.toJSONString(new CheckMemberTokenDto(memberId)), null, null);
        if(Objects.isNull(s) || s.isEmpty()) {
            return null;
        }
        LoggerUtils.info(log, "[getMemberInfo] 获取用户信息成功，获取MemberInfo：{}", s);
        return JSON.parseObject(s, BaseResponse.class);
    }

    public static ThirdPartyWechatBaseResponse getMemberIdListByUnionId(String unionId, ThirdPartyConfigDTO configDTO) {
        HashMap<String, String> headerMap = Maps.newHashMap();
        headerMap.put("appKey", configDTO.getAppKey());
        headerMap.put("appSecret", configDTO.getAppSecret());
        String s = HttpUtils.get(configDTO.getDomainUrl()+"/wechat/getListByUnionId?unionId="+unionId, headerMap, null);
        if(Objects.isNull(s) || s.isEmpty()) {
            return null;
        }
        LoggerUtils.info(log, "[getMemberIdListByUnionId] 获取用户信息成功，获取MemberInfo：{}", s);
        return JSON.parseObject(s, ThirdPartyWechatBaseResponse.class);
    }


    public static ThirdPartyWechatBaseResponse getMemberIdListByMemberId(String memberId, ThirdPartyConfigDTO configDTO) {
        HashMap<String, String> headerMap = Maps.newHashMap();
        headerMap.put("appKey", configDTO.getAppKey());
        headerMap.put("appSecret", configDTO.getAppSecret());
        String s = HttpUtils.get(configDTO.getDomainUrl()+"/wechat/getListByMemberId?memberId="+memberId, headerMap, null);
        if(Objects.isNull(s) || s.isEmpty()) {
            return null;
        }
        LoggerUtils.info(log, "[getMemberIdListByMemberId] 获取用户信息成功，获取MemberInfo：{}", s);
        return JSON.parseObject(s, ThirdPartyWechatBaseResponse.class);
    }


}
