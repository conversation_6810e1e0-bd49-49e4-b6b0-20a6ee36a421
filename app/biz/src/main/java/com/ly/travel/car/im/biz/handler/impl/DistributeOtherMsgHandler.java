
package com.ly.travel.car.im.biz.handler.impl;

import com.ly.sof.facade.base.BaseResponseDTO;
import com.ly.sof.utils.log.LogContextUtils;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.im.biz.error.BizErrorFactory;
import com.ly.travel.car.im.biz.error.ValidateException;
import com.ly.travel.car.im.biz.handler.BaseTools;
import com.ly.travel.car.im.biz.handler.MsgHandler;
import com.ly.travel.car.im.biz.service.SendService;
import com.ly.travel.car.im.dal.dao.SfcImMsgRecordDao;
import com.ly.travel.car.im.dal.entity.SfcImInfo;
import com.ly.travel.car.im.dal.entity.SfcImMsgRecord;
import com.ly.travel.car.im.facade.dto.MsgContentDTO;
import com.ly.travel.car.im.facade.enums.MsgTypeEnum;
import com.ly.travel.car.im.facade.request.SendMsgReqDTO;
import com.ly.travel.car.im.facade.response.SendMsgRspDTO;
import com.ly.travel.car.im.integration.client.api.PushApi;
import com.ly.travel.car.im.integration.client.api.RiskApi;
import com.ly.travel.car.im.integration.request.RiskQueryReqDTO;
import com.ly.travel.car.im.integration.response.RiskQueryRspDTO;
import com.ly.travel.car.im.model.dto.OrderDetailDTO;
import com.ly.travel.car.im.model.enums.MsgSendStatusEnum;
import com.ly.travel.car.im.model.utils.DesensitizationUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 其它消息处理器
 */
@Service
@Slf4j
public class DistributeOtherMsgHandler extends BaseTools implements MsgHandler {
    @Resource
    private SendService sendService;
    @Resource
    private RiskApi riskApi;
    private static final String          ERROR_CODE        = HttpStatus.INTERNAL_SERVER_ERROR.value() + Strings.EMPTY;
    @Resource
    private SfcImMsgRecordDao sfcImMsgRecordDao;
    private static final BizErrorFactory BIZ_ERROR_FACTORY = BizErrorFactory.getInstance();

    @Override
    public SendMsgRspDTO handle(SendMsgReqDTO req, OrderDetailDTO orderDetail) throws ValidateException {

        LogContextUtils.setCategory("DistributeOtherMsgHandler");
        LogContextUtils.setFilter1(req.filter1());
        String sessionKey = req.getSessionKey();
        Integer plateId = req.getPlatId();

        //会话信息
        SfcImInfo sfcImInfo = getSfcImInfo(sessionKey, plateId);
        //风控
        boolean risk = Boolean.FALSE;
        RiskQueryRspDTO riskQueryRsp = null;
        String content = req.getMsgContent().getContent();
        if (StringUtils.isNotBlank(content)) {
            RiskQueryReqDTO riskQueryReq = new RiskQueryReqDTO();
            riskQueryReq.setMemberId(req.getMemberId());
            riskQueryReq.setUnionId(req.getUnionId());
            riskQueryReq.setDriverCardNo(req.getPlateNumber());
            riskQueryReq.setOrderId(req.getOrderId());
            riskQueryReq.setMainScene(8);
            riskQueryReq.setChildScene(1);
            riskQueryReq.setText(content);
            riskQueryRsp = riskApi.riskQuery(riskQueryReq);
            risk = riskApi.riskJudge(riskQueryRsp);
        }

        //敏感词脱敏
        desensitization(risk, riskQueryRsp, req);

        //发送消息
        return sendAndSaveMsg(req, sfcImInfo, orderDetail);
    }


    private void desensitization(boolean risk, RiskQueryRspDTO riskQueryRsp, SendMsgReqDTO req) throws ValidateException {
        String userKey = req.filter1();
        LoggerUtils.info(log, "[desensitization][{}] 敏感词脱敏请求参数：{}，风控响应：{}", userKey, FastJsonUtils.toJSONString(req), FastJsonUtils.toJSONString(riskQueryRsp));

        //消息内容
        MsgContentDTO msgContent = req.getMsgContent();
        String content = msgContent.getContent();

        try {
            if (risk) {
                Map<String, String> obj = riskQueryRsp.getData().getObj();
                if (Objects.isNull(obj)) {
                    return;
                }

                for (String sensitiveWord : obj.values()) {
                    String value = DesensitizationUtils.desensitization(content, sensitiveWord);
                    if (StringUtils.isNotBlank(value)) {
                        content = value;
                    }
                }

                msgContent.setContent(content);
                LoggerUtils.info(log, "[desensitization][{}] 敏感词脱敏后：{}", userKey, FastJsonUtils.toJSONString(req));
            }
        } catch (Exception e) {
            LoggerUtils.error(log, "[desensitization] 敏感词脱敏异常：{}", userKey, e);
        }

        if (StringUtils.isNotBlank(content) && content.contains("*")) {
            throw new ValidateException(BIZ_ERROR_FACTORY.riskDesensitization(content));
        }
    }

    private SendMsgRspDTO sendAndSaveMsg(SendMsgReqDTO req, SfcImInfo sfcImInfo, OrderDetailDTO orderDetail) throws ValidateException {
        //组装消息存储入参
        SfcImMsgRecord msgRecord = distributeBuildMsg(req, orderDetail);
            SendMsgReqDTO sendMsgReqDTO = new SendMsgReqDTO();
            BeanUtils.copyProperties(req, sendMsgReqDTO);
            sendMsgReqDTO.setMemberId(orderDetail.getMemberId());
            sendMsgReqDTO.setUnionId(orderDetail.getUnionId());
        sendMsgReqDTO.getMsgContent().setMsgType(MsgTypeEnum.CUSTOM_TEXT.getMsgType());
            //发送消息
            BaseResponseDTO baseResponseDTO = sendService.sendMsg(sendMsgReqDTO, orderDetail);
            if (Objects.nonNull(baseResponseDTO)) {
                msgRecord.setMsgSendStatus(MsgSendStatusEnum.SUCCESS.getCode());
                msgRecord.setUpdateTime(new Date());
                msgRecord.setOrderStatus(orderDetail.getStatus());
                sfcImMsgRecordDao.save(msgRecord);
                //保存或更新会话
                insertOrUpdateSfcImInfo(req, sfcImInfo, msgRecord, req.getPlatId());
                LoggerUtils.info(log, "[sendAndSaveOnTripMsg] 保存im消息，乘客发送消息给司机，sfcImMsgRecord：{}", FastJsonUtils.toJSONString(msgRecord));
                return SendMsgRspDTO.success(req.getTraceId(), req.getMsgId());
            } else {
                msgRecord.setUpdateTime(new Date());
                msgRecord.setOrderStatus(orderDetail.getStatus());
                sfcImMsgRecordDao.save(msgRecord);
                //保存或更新会话
                insertOrUpdateSfcImInfo(req, sfcImInfo, msgRecord, req.getPlatId());
                LoggerUtils.info(log, "[sendAndSaveOnTripMsg] 保存im消息，乘客发送消息给司机，sfcImMsgRecord：{}", FastJsonUtils.toJSONString(msgRecord));
                return SendMsgRspDTO.fail(req.getTraceId(), ERROR_CODE, "消息发送失败", req.getMsgContent().getMsgId());
            }
    }
}
