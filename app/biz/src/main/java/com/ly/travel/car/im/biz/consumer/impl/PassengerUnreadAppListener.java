package com.ly.travel.car.im.biz.consumer.impl;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.utils.CarAssert;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.im.biz.annotations.ChatMsgTag;
import com.ly.travel.car.im.biz.consumer.SmsListener;
import com.ly.travel.car.im.dal.dao.SfcImMsgRecordDao;
import com.ly.travel.car.im.dal.entity.SfcImMsgRecord;
import com.ly.travel.car.im.integration.client.api.AppPushApi;
import com.ly.travel.car.im.integration.client.redis.impl.RedisClientProxy;
import com.ly.travel.car.im.integration.request.AppPushReqDTO;
import com.ly.travel.car.im.integration.utils.RedisKeyBuilder;
import com.ly.travel.car.im.model.dto.CarPassengerUnreadSmsDTO;
import com.ly.travel.car.im.model.enums.ChatMsgTagEnum;
import com.ly.travel.car.im.model.enums.MsgStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

@Service
@ChatMsgTag(ChatMsgTagEnum.im_passenger_unread_app)
@Slf4j
public class PassengerUnreadAppListener implements SmsListener {
    @Resource
    private RedisClientProxy redisClientProxy;
    @Resource
    private SfcImMsgRecordDao sfcImMsgRecordDao;

    @Value("${config.push.appPushJumpUrl}")
    private String appPushJumpUrl;
    @Resource
    private AppPushApi appPushApi;

    @Override
    public boolean handle(String msgBody) {
        LoggerUtils.info(log, "[passengerUnreadAppListener][req] 乘客未读消息监听 req：{}", msgBody);
        CarPassengerUnreadSmsDTO dto = FastJsonUtils.fromJSONString(msgBody, CarPassengerUnreadSmsDTO.class);
        String orderId = dto.getOrderId();
        boolean holdLock = Boolean.FALSE;
        try {
            holdLock = redisClientProxy.setnx(RedisKeyBuilder.orderLock(orderId), System.currentTimeMillis() + Strings.EMPTY, 60);
            CarAssert.isTrue(holdLock, "乘客未读消息推送，获取IM订单锁失败：" + orderId, dto);

            SfcImMsgRecord msgRecord = sfcImMsgRecordDao.findByMsgId(dto.getMsgId());
            if (Objects.isNull(msgRecord)) {
                LoggerUtils.warn(log, "[passengerUnreadSmsListener][{}] 消息记录不存在", orderId);
                return Boolean.TRUE;
            }

            if (Objects.equals(msgRecord.getMsgStatus(), MsgStatusEnum.READ.getCode())) {
                LoggerUtils.warn(log, "[passengerUnreadSmsListener][{}] 消息已读，忽略", orderId);
                return Boolean.TRUE;
            }

            AppPushReqDTO appPushReqDTO = new AppPushReqDTO();
            appPushReqDTO.setPushUrl(appPushJumpUrl);
            appPushReqDTO.setMemberId(dto.getMemberId().toString());
            appPushReqDTO.setNodeId("170086");
            JSONObject rsp = appPushApi.appPush(appPushReqDTO);

            if (Objects.nonNull(rsp) && StringUtils.equals(rsp.getString("RtnCode"), "0")) {
                LoggerUtils.info(log, "[passengerUnreadAppListener][{}] app推送发送成功: {}", orderId, rsp.get("RtnMsg"));
            } else {
                LoggerUtils.warn(log, "[passengerUnreadAppListener][{}] app推送发送失败", orderId);
            }
            return Boolean.TRUE;
        } catch (Exception e) {
            LoggerUtils.warn(log, "[passengerUnreadSmsListener][{}] app推送发送异常", orderId, e);
            return Boolean.FALSE;
        } finally {
            if (holdLock) {
                redisClientProxy.remove(RedisKeyBuilder.orderLock(orderId));
            }
        }
    }
}
