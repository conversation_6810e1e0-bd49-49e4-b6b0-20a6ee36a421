package com.ly.travel.car.im.biz.handler.impl;

import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.im.biz.error.ValidateException;
import com.ly.travel.car.im.biz.handler.BaseTools;
import com.ly.travel.car.im.biz.handler.MsgHandler;
import com.ly.travel.car.im.dal.dao.SfcImMsgRecordDao;
import com.ly.travel.car.im.dal.entity.SfcImMsgRecord;
import com.ly.travel.car.im.facade.dto.MsgContentDTO;
import com.ly.travel.car.im.facade.enums.MsgSenderEnum;
import com.ly.travel.car.im.facade.enums.MsgTypeEnum;
import com.ly.travel.car.im.facade.request.SendMsgReqDTO;
import com.ly.travel.car.im.facade.response.SendMsgRspDTO;
import com.ly.travel.car.im.model.dto.OrderDetailDTO;
import com.ly.travel.car.im.model.enums.MsgStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 邀请车主消息处理器
 */
@Service
@Slf4j
public class InviteDriverMsgHandler extends BaseTools implements MsgHandler {
    @Resource
    private SfcImMsgRecordDao sfcImMsgRecordDao;

    @Override
    public SendMsgRspDTO handle(SendMsgReqDTO req, OrderDetailDTO orderDetail) throws ValidateException {
        MsgContentDTO msgContent = req.getMsgContent();
        msgContent.setMsgType(MsgTypeEnum.INVITE_DRIVER.getMsgType());
        msgContent.setContent("待车主确认");
        msgContent.setMsgId(null);

        //组装系统消息存储入参
        SfcImMsgRecord msgRecord = buildSysMsg(req, MsgTypeEnum.INVITE_DRIVER.getMsgType(), FastJsonUtils.toJSONString(msgContent), Boolean.FALSE, orderDetail);
        msgRecord.setMsgSender(MsgSenderEnum.PASSENGER.getCode());
        msgRecord.setMsgStatus(MsgStatusEnum.READ.getCode());
        msgRecord.setUpdateTime(new Date());
        msgRecord.setOrderStatus(orderDetail.getStatus());
        sfcImMsgRecordDao.save(msgRecord);

        LoggerUtils.info(log, "[InviteDriverMsgHandler][{}] 发送消息成功，邀请司机消息入库，msgRecord：{}", req.filter1(), FastJsonUtils.toJSONString(msgRecord));
        return SendMsgRspDTO.success(req.getTraceId(), msgContent.getMsgId());
    }
}
