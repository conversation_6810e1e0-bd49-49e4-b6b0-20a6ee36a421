package com.ly.travel.car.im.biz.utils;

import org.apache.commons.lang3.StringUtils;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class MD5 {
    public MD5() {
    }

    public static String encrypt(String signature) {
        return encrypt(signature, "UTF-8", false);
    }

    public static String encrypt(String signature, String encode, boolean isUpperCase) {
        if (signature == null) {
            return null;
        } else {
            MessageDigest md5;
            try {
                md5 = MessageDigest.getInstance("MD5");
            } catch (NoSuchAlgorithmException var8) {
                throw new RuntimeException("MD5失败", var8);
            }

            byte[] plainText;
            try {
                if (StringUtils.isBlank(encode)) {
                    plainText = signature.getBytes();
                } else {
                    plainText = signature.getBytes(encode);
                }
            } catch (UnsupportedEncodingException var7) {
                throw new RuntimeException("MD5失败", var7);
            }

            md5.update(plainText);
            String ret = hexDump(md5.digest());
            if (isUpperCase) {
                ret = ret.toUpperCase();
            } else {
                ret = ret.toLowerCase();
            }

            return ret;
        }
    }

    public static final String hexDump(byte[] bArray) {
        StringBuffer sb = new StringBuffer(bArray.length);

        for (int i = 0; i < bArray.length; ++i) {
            String sTemp = Integer.toHexString(255 & bArray[i]);
            if (sTemp.length() < 2) {
                sb.append(0);
            }

            sb.append(sTemp.toUpperCase());
        }

        return sb.toString();
    }

    public static void main(String[] args) {
        String origin = "176191.0OCH2017061716000012345678544";
        System.out.println(encrypt(origin));
    }
}
