package com.ly.travel.car.im.biz.handler.impl;

import com.ly.car.bean.Simple;
import com.ly.sof.utils.log.LogContextUtils;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.im.biz.error.ValidateException;
import com.ly.travel.car.im.biz.handler.BaseTools;
import com.ly.travel.car.im.biz.handler.MsgHandler;
import com.ly.travel.car.im.dal.dao.SfcImMsgRecordDao;
import com.ly.travel.car.im.dal.entity.SfcImMsgRecord;
import com.ly.travel.car.im.facade.dto.MsgContentDTO;
import com.ly.travel.car.im.facade.enums.MsgTypeEnum;
import com.ly.travel.car.im.facade.request.SendMsgReqDTO;
import com.ly.travel.car.im.facade.response.SendMsgRspDTO;
import com.ly.travel.car.im.integration.client.api.PushApi;
import com.ly.travel.car.im.integration.request.SafeNightWarnMsgReqDTO;
import com.ly.travel.car.im.integration.request.TipsMsgReqDTO;
import com.ly.travel.car.im.model.constant.MsgTipsConstant;
import com.ly.travel.car.im.model.dto.OrderDetailDTO;
import com.ly.travel.car.im.model.utils.DateToolsUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 提示公告&夜间出行提示消息处理器
 */
@Service
@Slf4j
public class TipMsgHandler extends BaseTools implements MsgHandler {
    @Resource
    private SfcImMsgRecordDao sfcImMsgRecordDao;
    @Resource
    private PushApi pushApi;

    @Override
    public SendMsgRspDTO handle(SendMsgReqDTO req, OrderDetailDTO orderDetail) throws ValidateException {
        //提示公告
        tipsNotice(req, orderDetail, req.getSessionKey());

        //夜间出行提醒
        nightWarn(req, orderDetail, req.getSessionKey());

        return SendMsgRspDTO.success(req.getTraceId(), req.getMsgContent().getMsgId());
    }

    private void tipsNotice(SendMsgReqDTO req, OrderDetailDTO orderDetail, String sessionKey) {

        LogContextUtils.setCategory("tipsNotice");
        LogContextUtils.setFilter1(orderDetail.getOrderId());
        //查询提示公告是否推送
        SfcImMsgRecord msgRecord = sfcImMsgRecordDao.findByMsgRecord(sessionKey, req.getOrderId(), MsgTypeEnum.TIP.getMsgType());
        if (Objects.nonNull(msgRecord) && Objects.equals(msgRecord.getMsgType(), req.getMsgType())) {
            LoggerUtils.warn(log, "通知消息已发送，req：{}",  FastJsonUtils.toJSONString(req));
            return;
        }

        //提示公告（温馨提示）推送
        String content = req.getMsgContent().getContent();
        TipsMsgReqDTO tipsMsgReqDTO = buildTipsMsgPush(req, sessionKey, content);
        Simple<?> simple = pushApi.tipsMsgPush(tipsMsgReqDTO);
        if (Objects.nonNull(simple) && Objects.equals(simple.getStatus(), Simple.OK.getStatus())) {
            //组装系统消息存储入参
            msgRecord = buildSysMsg(req, req.getMsgType(), content, Boolean.FALSE, orderDetail);
            msgRecord.setUpdateTime(new Date());
            msgRecord.setOrderStatus(orderDetail.getStatus());
            sfcImMsgRecordDao.save(msgRecord);
            LoggerUtils.info(log, "发送消息成功，提示公告消息入库，sysMsg：{}", FastJsonUtils.toJSONString(msgRecord));
        }
    }

    private void nightWarn(SendMsgReqDTO req, OrderDetailDTO orderDetail, String sessionKey) {
        try {
            LogContextUtils.setCategory("nightWarn");
            LogContextUtils.setFilter1(orderDetail.getOrderId());
            //最早用车时间
            Date earliestTime = orderDetail.getEarliestTime();
            if (DateToolsUtils.dateIsNight(earliestTime)) {
                LoggerUtils.info(log, "该订单最早出发时间：{}" , earliestTime);
                //查询是否推送过夜间出行提醒
                SfcImMsgRecord msgRecord = sfcImMsgRecordDao.findByMsgRecord(sessionKey, orderDetail.getOrderId(), MsgTypeEnum.NIGHT_WARN.getMsgType());
                if (Objects.nonNull(msgRecord)) {
                    LoggerUtils.info(log, "当前订单已推送过夜间出行提示：{}",  sessionKey);
                    return;
                }

                req.setMsgId(UUID.randomUUID().toString().replace("-", Strings.EMPTY));
                SimpleDateFormat sdf = new SimpleDateFormat("MM月dd日 HH:mm");
                String content = String.format(MsgTipsConstant.NIGHT_WARN, sdf.format(earliestTime));

                //调用接口，推送消息
                Simple<?> simple = pushApi.safeNightMsgPush(buildNightWarnMsgPush(req, sessionKey, content));
                if (Objects.nonNull(simple) && Objects.equals(simple.getStatus(), Simple.OK.getStatus())) {
                    //组装系统信息存储入参 只落一次库
                    SfcImMsgRecord sysMsg = buildSysMsg(req, MsgTypeEnum.NIGHT_WARN.getMsgType(), content, Boolean.TRUE, orderDetail);
                    sfcImMsgRecordDao.save(sysMsg);
                    LoggerUtils.info(log, "发送消息成功，夜间出行提醒消息入库，sysMsg：{}", FastJsonUtils.toJSONString(sysMsg));
                }
            }
        } catch (Exception e) {
            LoggerUtils.warn(log, "夜间提醒消息推送失败：{}", req.getOrderId(), e);
        }
    }

    private TipsMsgReqDTO buildTipsMsgPush(SendMsgReqDTO sendMsgReqDTO, String sessionKey, String content) {
        MsgContentDTO msgContentDTO = new MsgContentDTO();
        msgContentDTO.setMsgId(sendMsgReqDTO.getMsgId());
        msgContentDTO.setMsgType(MsgTypeEnum.TIP.getMsgType());
        msgContentDTO.setContent(content);
        TipsMsgReqDTO req = new TipsMsgReqDTO();
        req.setMsgId(sendMsgReqDTO.getMsgId());
        req.setMsgContent(msgContentDTO);
        req.setPlateNumber(sendMsgReqDTO.getPlateNumber());
        req.setSupplierCode(sendMsgReqDTO.getSupplierCode());
        req.setDriverId(sendMsgReqDTO.getDriverId());
        req.setOrderId(sendMsgReqDTO.getOrderId());
        req.setSupplierOrderId(sendMsgReqDTO.getSupplierOrderId());
        req.setUnionId(sendMsgReqDTO.getUnionId());
        req.setOpenId(Strings.EMPTY);
        req.setMemberId(StringUtils.isEmpty(sendMsgReqDTO.getMemberId()) ? "0" : sendMsgReqDTO.getMemberId());
        req.setImKey(req.filter1());
        req.setMsgType(MsgTypeEnum.TIP.getMsgType());
        req.setSessionKey(sessionKey);
        return req;
    }

    private SafeNightWarnMsgReqDTO buildNightWarnMsgPush(SendMsgReqDTO sendMsgReqDTO, String sessionKey, String content) {
        MsgContentDTO msgContentDTO = new MsgContentDTO();
        msgContentDTO.setMsgId(sendMsgReqDTO.getMsgId());
        msgContentDTO.setMsgType(MsgTypeEnum.NIGHT_WARN.getMsgType());
        msgContentDTO.setContent(content);
        SafeNightWarnMsgReqDTO req = new SafeNightWarnMsgReqDTO();
        req.setMsgId(sendMsgReqDTO.getMsgId());
        req.setMsgContent(msgContentDTO);
        req.setPlateNumber(sendMsgReqDTO.getPlateNumber());
        req.setSupplierCode(sendMsgReqDTO.getSupplierCode());
        req.setDriverId(sendMsgReqDTO.getDriverId());
        req.setOrderId(sendMsgReqDTO.getOrderId());
        req.setSupplierOrderId(sendMsgReqDTO.getSupplierOrderId());
        req.setUnionId(sendMsgReqDTO.getUnionId());
        req.setOpenId(Strings.EMPTY);
        req.setMemberId(StringUtils.isEmpty(sendMsgReqDTO.getMemberId()) ? "0" : sendMsgReqDTO.getMemberId());
        req.setImKey(req.filter1());
        req.setMsgType(MsgTypeEnum.NIGHT_WARN.getMsgType());
        req.setSessionKey(sessionKey);
        return req;
    }
}
