package com.ly.travel.car.im.biz.config;

import com.ly.travel.car.im.biz.utils.RedisCreateUtils;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

@Configuration
public class RedissonConfig {

    @Bean
    public RedissonClient getRedissonClient() throws Exception {

        return RedisCreateUtils.redissonClient("car.im.chat.service.group");
    }

}