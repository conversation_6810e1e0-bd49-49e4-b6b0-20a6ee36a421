package com.ly.travel.car.im.biz.consumer.impl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.tcbase.config.ConfigCenterClient;
import com.ly.travel.car.im.biz.annotations.ChatMsgTag;
import com.ly.travel.car.im.biz.consumer.SmsListener;
import com.ly.travel.car.im.biz.error.ValidateException;
import com.ly.travel.car.im.biz.service.OrderService;
import com.ly.travel.car.im.integration.client.es.ImDispatchClient;
import com.ly.travel.car.im.integration.client.es.common.ESSearchResponse;
import com.ly.travel.car.im.integration.client.es.imdispatch.ImDispatchToElasticSearchVO;
import com.ly.travel.car.im.integration.client.redis.impl.RedisClientProxy;
import com.ly.travel.car.im.model.config.ImConfigCenter;
import com.ly.travel.car.im.model.dto.ChatMessageDispatchDTO;
import com.ly.travel.car.im.model.dto.OrderDetailDTO;
import com.ly.travel.car.im.model.enums.ChatMsgTagEnum;
import com.ly.travel.car.im.model.enums.ConfigCenterKeyEnum;
import com.ly.travel.shared.mobility.supply.order.core.facade.order.response.OrderRelationResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 消息分发监听器
 */
@Service
@ChatMsgTag(ChatMsgTagEnum.im_chat_message_dispatch)
@Slf4j
public class ChatMessageDispatchListener implements SmsListener {

    @Resource
    private ImDispatchClient imDispatchClient;

    @Resource
    private OrderService orderService;

    @Resource
    private RedissonClient redissonClient;

    private static final long dispatch_msg_concurrent_lock_wait_time = 3;

    @Override
    public boolean handle(String msgBody) {
        LoggerUtils.info(log, "[chatMessageDispatchListener][req] 消息分发监听 req：{}", msgBody);

        try {
            // 解析为数组
            List<ChatMessageDispatchDTO> dtoList = new ArrayList<>();
            try {
                JSONArray jsonArray = JSON.parseArray(msgBody);
                if (jsonArray != null && !jsonArray.isEmpty()) {
                    for (int i = 0; i < jsonArray.size(); i++) {
                        ChatMessageDispatchDTO item = JSON.toJavaObject(jsonArray.getJSONObject(i), ChatMessageDispatchDTO.class);
                        if (item != null) {
                            dtoList.add(item);
                        }
                    }
                }
            } catch (Exception e) {
                LoggerUtils.warn(log, "[chatMessageDispatchListener] 解析消息数组异常", e);
            }

            if (dtoList.isEmpty()) {
                LoggerUtils.warn(log, "[chatMessageDispatchListener] 消息格式不正确，无法解析");
                return Boolean.TRUE;
            }

            // 处理消息列表
            for (ChatMessageDispatchDTO item : dtoList) {
                if (Objects.nonNull(item)) {
                    processMessage(item);
                }
            }

            return Boolean.TRUE;
        } catch (Exception e) {
            LoggerUtils.error(log, "[chatMessageDispatchListener] 处理异常", e);
            return Boolean.FALSE;
        }
    }

    /**
     * 处理单条消息
     *
     * @param dto 消息数据
     */
    private void processMessage(ChatMessageDispatchDTO dto) {
        String cacheKey = dto.getMsgId()+"_"+dto.getStatus();
        RLock lock = redissonClient.getLock(cacheKey);
        boolean locked = false;
        try {

            locked = lock.tryLock(Long.valueOf(ImConfigCenter.getConfigValue(ConfigCenterKeyEnum.im_dispatch_concurrent_lock_wait_time)), Long.valueOf(ImConfigCenter.getConfigValue(ConfigCenterKeyEnum.im_dispatch_concurrent_lock_expire_time)), TimeUnit.SECONDS);

            if (!locked) {
                log.info("消息 {} 已被其他消费者处理，跳过", dto.getMsgId());
                return;
            }

            ESSearchResponse<ImDispatchToElasticSearchVO> esResponse = imDispatchClient.queryByMsgId(dto.getMsgId());
            if (esResponse != null && esResponse.getResult() != null &&
                    CollectionUtils.isNotEmpty(esResponse.getResult().getList())) {
                Optional<ImDispatchToElasticSearchVO> imDispatchToElasticSearchVO = esResponse.getResult().getList().stream().filter(item -> item.getStatus().equals(dto.getStatus())).findFirst();
                if (imDispatchToElasticSearchVO.isPresent()) {
                    log.info("消息 {} 已被其他消费者处理，跳过", dto.getMsgId());
                    return;
                }
            }

            String orderId = dto.getOrderId();
            String supplierCode = dto.getSupplierCode();
            if (StringUtils.isNotEmpty(dto.getPassengerOrderGuid()) && StringUtils.isEmpty(dto.getOrderId())) {
                OrderRelationResponse orderRelation = orderService.orderRelationInfo(Strings.EMPTY, dto.getPassengerOrderGuid());
                if (Objects.isNull(orderRelation)) {
                    log.warn("消息 {} orderRelation为空", dto.getMsgId());
                    return;
                }
                //订单详情
                OrderDetailDTO orderDetail = orderService.getOrderDetail(orderRelation.getOrderSerialNo());
                if (Objects.isNull(orderDetail)) {
                    log.warn("消息 {} orderDetail为空", dto.getMsgId());
                    return;
                }
                if (StringUtils.isEmpty(dto.getSupplierCode())) {
                    supplierCode = orderRelation.getSupplierCode();
                }
                orderId = orderDetail.getOrderId();
            }

            log.info( "[chatMessageDispatchListener] 处理消息分发, orderId: {}, msgId: {}, supplierCode: {}, latestTime: {}",
                    orderId, dto.getMsgId(), dto.getSupplierCode(), dto.getLatestTime());

            if (StringUtils.isEmpty(orderId)){
                log.warn("消息 {} orderId为空", dto.getMsgId());
                return;
            }

            ImDispatchToElasticSearchVO imDispatchToElasticSearchVO = new ImDispatchToElasticSearchVO();
            imDispatchToElasticSearchVO.setOrderId(orderId);
            imDispatchToElasticSearchVO.setMsgId(dto.getMsgId());
            imDispatchToElasticSearchVO.setSupplierCode(supplierCode);
            imDispatchToElasticSearchVO.setPassengerOrderGuid(dto.getPassengerOrderGuid());
            imDispatchToElasticSearchVO.setLatestTime(dto.getLatestTime());
            imDispatchToElasticSearchVO.setDispatchTime(new Date());
            imDispatchToElasticSearchVO.setStatus(dto.getStatus());
            imDispatchClient.bulk(imDispatchToElasticSearchVO);
        } catch (Exception e) {
            LoggerUtils.error(log, "[chatMessageDispatchListener][{}] 处理异常", dto.getMsgId(), e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
} 