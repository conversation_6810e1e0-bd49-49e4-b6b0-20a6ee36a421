package com.ly.travel.car.im.biz.handler;

import com.ly.travel.car.im.biz.error.ValidateException;
import com.ly.travel.car.im.facade.request.SendMsgReqDTO;
import com.ly.travel.car.im.facade.response.SendMsgRspDTO;
import com.ly.travel.car.im.model.dto.OrderDetailDTO;

public interface MsgHandler {

    /**
     * 消息类型处理器
     *
     * @param req         入参
     * @param orderDetail 订单详情
     * @return
     */
    SendMsgRspDTO handle(SendMsgReqDTO req, OrderDetailDTO orderDetail) throws ValidateException;
}
