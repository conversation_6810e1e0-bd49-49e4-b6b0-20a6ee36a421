package com.ly.travel.car.im.biz.handler.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.car.bean.Simple;
import com.ly.sof.facade.base.BaseResponseDTO;
import com.ly.sof.utils.log.LogContextUtils;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.im.biz.error.BizErrorFactory;
import com.ly.travel.car.im.biz.error.ValidateException;
import com.ly.travel.car.im.biz.handler.BaseTools;
import com.ly.travel.car.im.biz.handler.MsgHandler;
import com.ly.travel.car.im.biz.service.SendService;
import com.ly.travel.car.im.dal.dao.SfcImMsgRecordDao;
import com.ly.travel.car.im.dal.entity.SfcImInfo;
import com.ly.travel.car.im.dal.entity.SfcImMsgRecord;
import com.ly.travel.car.im.facade.dto.MsgContentDTO;
import com.ly.travel.car.im.facade.enums.MsgSenderEnum;
import com.ly.travel.car.im.facade.enums.MsgTypeEnum;
import com.ly.travel.car.im.facade.request.SendMsgReqDTO;
import com.ly.travel.car.im.facade.response.SendMsgRspDTO;
import com.ly.travel.car.im.integration.client.api.PushApi;
import com.ly.travel.car.im.integration.client.api.RiskApi;
import com.ly.travel.car.im.integration.client.supply.SupplierClient;
import com.ly.travel.car.im.integration.request.*;
import com.ly.travel.car.im.integration.response.RiskQueryDTO;
import com.ly.travel.car.im.integration.response.RiskQueryRspDTO;
import com.ly.travel.car.im.integration.throwable.exception.IntegrationException;
import com.ly.travel.car.im.model.config.ImConfigCenter;
import com.ly.travel.car.im.model.constant.MsgTipsConstant;
import com.ly.travel.car.im.model.dto.MsgConfigDTO;
import com.ly.travel.car.im.model.dto.OrderDetailDTO;
import com.ly.travel.car.im.model.enums.MsgSendStatusEnum;
import com.ly.travel.car.im.model.enums.MsgStatusEnum;
import com.ly.travel.car.im.model.enums.SystemMsgErrorMappingEnum;
import com.ly.travel.car.im.model.utils.DesensitizationUtils;
import com.ly.travel.car.tradecore.model.enums.OrderState;
import com.ly.travel.shared.mobility.supply.trade.core.facade.trade.request.SupplierImCapacityRequest;
import com.ly.travel.shared.mobility.supply.trade.core.facade.trade.response.SupplierImCapacityResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

import static com.ly.travel.car.im.model.constant.MsgTipsConstant.PAY_LIMIT;

/**
 * 其它消息处理器
 */
@Service
@Slf4j
public class OtherMsgHandler extends BaseTools implements MsgHandler {
    @Resource
    private SendService sendService;
    @Resource
    private RiskApi riskApi;
    @Resource
    private PushApi pushApi;
    @Resource
    private SfcImMsgRecordDao sfcImMsgRecordDao;

    @Resource
    private SupplierClient supplierClient;

    private static final BizErrorFactory BIZ_ERROR_FACTORY = BizErrorFactory.getInstance();
    private static final Set<Integer> ON_TRIP_STATES = new HashSet<>(Arrays.asList(
            OrderState.RECEIVING_ORDER.getCode(),
            OrderState.AWAITING_TRAVEL.getCode(),
            OrderState.IN_TRIP.getCode(),
            OrderState.TRIP_FINISHED.getCode(),
            OrderState.ORDER_CLOSED.getCode()
    ));

    private static final Set<Integer> NO_TRIP_STATES = new HashSet<>(Arrays.asList(
            OrderState.DRAFT.getCode(),
            OrderState.DISPATCHING.getCode(),
            OrderState.CANCELED.getCode()
    ));

    private static final Integer NOT_PAY = 0;
    private static final Integer PAY = 1;
    private static final Integer SFC = 80;

    @Override
    public SendMsgRspDTO handle(SendMsgReqDTO req, OrderDetailDTO orderDetail) throws ValidateException {

        LogContextUtils.setCategory("OtherMsgHandler");
        LogContextUtils.setFilter1(req.filter1());

        String userKey = req.filter1();
        String traceId = req.getTraceId();
        String sessionKey = req.getSessionKey();
        String orderId = req.getOrderId();
        Integer plateId = req.getPlatId();
        String msgId = req.getMsgContent().getMsgId();

        //会话信息
        SfcImInfo sfcImInfo = getSfcImInfo(sessionKey,plateId);

        //已建立行程
        boolean onTrip = Boolean.FALSE;

        //以下状态不限制发送消息条数校验
        int status = orderDetail.getStatus();
        int payStatus = orderDetail.getPayStatus();
        if(!(orderDetail.getPayCategory() == 1 && orderDetail.getPayStatus() == 0)){
            payStatus = PAY;
        }


        if (ON_TRIP_STATES.contains(status) &&
                (!NOT_PAY.equals(payStatus) && OrderState.RECEIVING_ORDER.getCode() == status)) {
            onTrip = Boolean.TRUE;
        }

        if (ImConfigCenter.isLimitMsg() && orderDetail.getOrderType() == SFC
                && (NOT_PAY.equals(payStatus))
                && !req.getMsgType().equals(MsgTypeEnum.QUICK_MSG.getMsgType())) {
            LoggerUtils.info(log, "消息状态不合法，msgType：{}，orderDetail：{}", req.getMsgType(), FastJsonUtils.toJSONString(orderDetail));
            //组装msg存储入参
            SfcImMsgRecord msg = buildMsg(req, orderDetail);
            msg.setUpdateTime(new Date());
            msg.setOrderStatus(orderDetail.getStatus());
            sfcImMsgRecordDao.save(msg);
            pushSystemMsg(buildSysMsgPush(req, req.getSessionKey(), PAY_LIMIT));
            saveRecord(req, orderDetail, PAY_LIMIT);
            throw new ValidateException(BIZ_ERROR_FACTORY.msgSendFailError(req.getMsgId()));
        }
        LoggerUtils.info(log, "发送消息是否在行程中，onTrip：{}，orderDetail：{}", onTrip, FastJsonUtils.toJSONString(orderDetail));

        if (!MsgTypeEnum.QUICK_MSG.getMsgType().equals(req.getMsgType())) {
            //未形成会话
            Long driverMsgCount = sfcImMsgRecordDao.selectCount(sessionKey, MsgSenderEnum.DRIVER.getCode(), orderId, null);

            if (Objects.equals(driverMsgCount, 0) && !onTrip) {
                //组装msg存储入参
                SfcImMsgRecord msg = buildMsg(req, orderDetail);
                msg.setUpdateTime(new Date());
                msg.setOrderStatus(orderDetail.getStatus());
                sfcImMsgRecordDao.save(msg);

                //存储或更新会话
                insertOrUpdateSfcImInfo(req, sfcImInfo, msg,req.getPlatId());

                //组装msg存储入参
                SfcImMsgRecord msgRecord = buildSysMsg(req, MsgTypeEnum.SYSTEM.getMsgType(), MsgTipsConstant.SEND_FAIL, Boolean.TRUE, orderDetail);
                msgRecord.setUpdateTime(new Date());
                msgRecord.setOrderStatus(orderDetail.getStatus());
                sfcImMsgRecordDao.save(msgRecord);

                throw new ValidateException(BIZ_ERROR_FACTORY.msgLimitError(msgId));
            }
        }

        //风控
        boolean risk = Boolean.FALSE;
        RiskQueryRspDTO riskQueryRsp = null;
        String content = req.getMsgContent().getContent();
        if (StringUtils.isNotBlank(content)) {
            RiskQueryReqDTO riskQueryReq = new RiskQueryReqDTO();
            riskQueryReq.setMemberId(req.getMemberId());
            riskQueryReq.setUnionId(req.getUnionId());
            riskQueryReq.setDriverCardNo(req.getPlateNumber());
            riskQueryReq.setOrderId(req.getOrderId());
            riskQueryReq.setMainScene(8);
            riskQueryReq.setChildScene(1);
            riskQueryReq.setText(content);
            riskQueryRsp = riskApi.riskQuery(riskQueryReq);
            risk = riskApi.riskJudge(riskQueryRsp);
        }

        //用户安全信息提醒
        boolean sendWarn = safeWarn(req, riskQueryRsp);

        //敏感词脱敏
        desensitization(risk, riskQueryRsp, req);

        if (onTrip) {
            //组装系统消息存储入参
            SfcImMsgRecord msgRecord = buildSysMsg(req, MsgTypeEnum.SYSTEM.getMsgType(), MsgTipsConstant.RISK, Boolean.TRUE, orderDetail);

            //发送消息
            sendAndSaveOnTripMsg(req, sfcImInfo, risk, msgRecord, sendWarn, orderDetail);

            if (!risk) {
                LoggerUtils.info(log, "发送消息成功");
                return SendMsgRspDTO.success(traceId, msgId);
            } else {
                if (sendWarn) {
                    LoggerUtils.warn(log, "发送报警通知|用户|已建立行程|content：{}", content);
                    return SendMsgRspDTO.success(traceId, msgId);
                }
                pushSystemMsg(buildSysMsgPush(req, req.getSessionKey(), msgRecord.getMsgContent()));
                throw new ValidateException(BIZ_ERROR_FACTORY.msgSensitiveWordsError(msgId));
            }
        }

        //司机未读n（默认5）条消息后，限制发送
        int limit = 5;

        limit = getLimit(orderDetail, limit, orderId,req);

        Page<SfcImMsgRecord> page = new Page<>();
        page.setSize(limit);
        Page<SfcImMsgRecord> sfcImMsgRecordPage = sfcImMsgRecordDao.selectRecordPage(page, sessionKey, orderId);
        List<SfcImMsgRecord> records = sfcImMsgRecordPage.getRecords();
        LoggerUtils.info(log, "查看前5条司乘消息，records：{}",FastJsonUtils.toJSONString(records));

        List<SfcImMsgRecord> sendMsgList = new ArrayList<>();
        int i = 0;
        //乘客发送且为未读
        while (i < records.size() && Objects.equals(MsgSenderEnum.PASSENGER.getCode(), records.get(i).getMsgSender())
                && Objects.equals(MsgStatusEnum.UNREAD.getCode(), records.get(i).getMsgStatus())
                && Objects.equals(MsgSendStatusEnum.SUCCESS.getCode(), records.get(i).getMsgSendStatus())) {
            sendMsgList.add(records.get(i));
            i++;
        }

        if (CollectionUtils.isEmpty(sendMsgList)) {
            //组装msg存储入参
            SfcImMsgRecord msgRecord = buildSysMsg(req,
                    MsgTypeEnum.SYSTEM.getMsgType(), risk ? MsgTipsConstant.RISK : String.format(MsgTipsConstant.BEFORE_LIMIT, limit - 1), Boolean.TRUE, orderDetail);



            //发送msg&存储msg
            sendAndSaveMsg(req, sfcImInfo, risk, msgRecord, sendWarn, orderDetail);

            if (!risk) {
                LoggerUtils.info(log, "发送消息成功");
                return SendMsgRspDTO.success(traceId, msgId);
            } else {
                if (sendWarn) {
                    LoggerUtils.warn(log, "发送报警通知|用户|消息列表空|content：{}", content);
                    return SendMsgRspDTO.success(traceId, msgId);
                }
                throw new ValidateException(BIZ_ERROR_FACTORY.msgSensitiveWordsError(msgId));
            }
        }

        //司机未读乘客消息条数 < 配置条数
        if (sendMsgList.size() < limit) {

            String tip = sendMsgList.size() < (limit - 1) ? String.format(MsgTipsConstant.BEFORE_LIMIT, limit - 1 - sendMsgList.size())
                    : String.format(MsgTipsConstant.AFTER_LIMIT, limit);

            //组装msg存储入参
            SfcImMsgRecord msgRecord = buildSysMsg(req, MsgTypeEnum.SYSTEM.getMsgType(), risk ? MsgTipsConstant.RISK : tip, Boolean.TRUE, orderDetail);


            //发送msg&存储msg
            sendAndSaveMsg(req, sfcImInfo, risk, msgRecord, sendWarn, orderDetail);

            if (!risk) {
                LoggerUtils.info(log, "发送消息成功", userKey);
                return SendMsgRspDTO.success(traceId, msgId);
            }

            if (sendWarn) {
                LoggerUtils.warn(log, "发送报警通知|用户|小于发送限制|content：{}", content);
                return SendMsgRspDTO.success(traceId, msgId);
            }

            throw new ValidateException(BIZ_ERROR_FACTORY.msgSensitiveWordsError(msgId));
        }


        //存储msg
        savePassageRecord(req, orderDetail);

        //组装保存系统消息
        SfcImMsgRecord msgRecord = buildSysMsg(req, MsgTypeEnum.SYSTEM.getMsgType(), risk ? MsgTipsConstant.RISK : String.format(MsgTipsConstant.AFTER_LIMIT, limit),
                Boolean.TRUE, orderDetail);
        msgRecord.setUpdateTime(new Date());
        msgRecord.setOrderStatus(status);
        sfcImMsgRecordDao.save(msgRecord);

        //组装消息
        SfcImMsgRecord record = buildMsg(req, orderDetail);

        if (sendWarn) {
            //组装&保存系统信息
            SfcImMsgRecord sfcImMsgRecord = buildSysMsg(req, MsgTypeEnum.SAFE_WARN_TEXT.getMsgType(),
                    MsgTipsConstant.SAFE_RISK_WARN, Boolean.TRUE, orderDetail);
            sfcImMsgRecordDao.save(sfcImMsgRecord);
        }else {
            pushSystemMsg(buildSysMsgPush(req, req.getSessionKey(), msgRecord.getMsgContent()));
        }

        //保存或更新会话
        insertOrUpdateSfcImInfo(req, sfcImInfo, record,req.getPlatId());

        if (!risk) {
            throw new ValidateException(BIZ_ERROR_FACTORY.msgLimitError(msgId));
        }

        if (sendWarn) {
            LoggerUtils.warn(log, "发送报警通知|用户|非发送限制|content：{}", content);
            return SendMsgRspDTO.success(traceId, msgId);
        }

        throw new ValidateException(BIZ_ERROR_FACTORY.msgSensitiveWordsError(msgId));
    }


    private int getLimit(OrderDetailDTO orderDetail, int limit, String orderId,SendMsgReqDTO req) {
        try {
            SupplierImCapacityRequest supplierImCapacityRequest = new SupplierImCapacityRequest();
            ArrayList<String> supplierCodes = new ArrayList<>();
            supplierCodes.add(req.getSupplierCode());
            supplierImCapacityRequest.setSupplierCodes(supplierCodes);
            SupplierImCapacityResponse supplierImCapacityResponse = supplierClient.queryImCapacity(supplierImCapacityRequest);
            if (Objects.nonNull(supplierImCapacityResponse) && Objects.nonNull(supplierImCapacityResponse.getSuppliers())
                    && StringUtils.isNotEmpty(supplierImCapacityResponse.getSuppliers().get(0).getImMessageCountLimit())) {
                limit = Integer.parseInt(supplierImCapacityResponse.getSuppliers().get(0).getImMessageCountLimit());
                return limit;
            }
        } catch (IntegrationException e) {
            LoggerUtils.warn(log, "获取供应链发送条数限制异常：{}", e);
        }
        MsgConfigDTO msgConfig = ImConfigCenter.getMsgConfig(Strings.EMPTY, orderId);
        if (Objects.nonNull(msgConfig) && Objects.nonNull(msgConfig.getLimit())) {
            limit = msgConfig.getLimit();
        }
        return limit;
    }

    private boolean safeWarn(SendMsgReqDTO req, RiskQueryRspDTO riskQueryRsp) {
        String orderId = req.getOrderId();
        if (Objects.isNull(riskQueryRsp)) {
            LoggerUtils.warn(log, "[safeWarn] 安全提醒请求风控异常 orderId：{}", orderId);
            return Boolean.FALSE;
        }

        RiskQueryDTO data = riskQueryRsp.getData();
        if (Objects.isNull(data)) {
            LoggerUtils.warn(log, "[safeWarn] 安全提醒无风控数据 orderId：{}", orderId);
            return Boolean.FALSE;
        }

        Map<String, String> obj = data.getObj();
        if (Objects.isNull(obj)) {
            LoggerUtils.warn(log, "[safeWarn] 安全提醒无风控规则 orderId：{}", orderId);
            return Boolean.FALSE;
        }

        if (!obj.containsKey("aq006") && !obj.containsKey("aq007")) {
            LoggerUtils.warn(log, "[safeWarn] 安全无风险 orderId：{}", orderId);
            return Boolean.FALSE;
        }

        SafeWarnMsgReqDTO safeWarnMsgReq = buildSafeWarnMsgPushReq(req);
        try {
            //调用接口，推送消息
            Simple<?> simple = pushApi.safeWarnMsgPush(safeWarnMsgReq);
            if (Objects.nonNull(simple) && Objects.equals(simple.getStatus(), Simple.OK.getStatus())) {
                return Boolean.TRUE;
            }
        } catch (Exception e) {
            LoggerUtils.error(log, "[safeWarn] 安全消息推送失败：{}", orderId, e);
        }
        return Boolean.FALSE;
    }

    private SafeWarnMsgReqDTO buildSafeWarnMsgPushReq(SendMsgReqDTO req) {
        MsgContentDTO msgContentDTO = new MsgContentDTO();
        msgContentDTO.setMsgId(req.getMsgId());
        msgContentDTO.setMsgType(MsgTypeEnum.SAFE_WARN_TEXT.getMsgType());
        msgContentDTO.setContent(MsgTipsConstant.SAFE_RISK_WARN);
        SafeWarnMsgReqDTO safeWarnMsgReqDTO = new SafeWarnMsgReqDTO();
        safeWarnMsgReqDTO.setMsgId(req.getMsgId());
        safeWarnMsgReqDTO.setMsgContent(msgContentDTO);
        safeWarnMsgReqDTO.setPlateNumber(req.getPlateNumber());
        safeWarnMsgReqDTO.setSupplierCode(req.getSupplierCode());
        safeWarnMsgReqDTO.setDriverId(req.getDriverId());
        safeWarnMsgReqDTO.setOrderId(req.getOrderId());
        safeWarnMsgReqDTO.setSupplierOrderId(req.getSupplierOrderId());
        safeWarnMsgReqDTO.setUnionId(req.getUnionId());
        safeWarnMsgReqDTO.setOpenId(Strings.EMPTY);
        safeWarnMsgReqDTO.setMemberId(StringUtils.isEmpty(req.getMemberId()) ? "0" : req.getMemberId());
        safeWarnMsgReqDTO.setImKey(safeWarnMsgReqDTO.filter1());
        safeWarnMsgReqDTO.setMsgType(MsgTypeEnum.SAFE_WARN_TEXT.getMsgType());
        safeWarnMsgReqDTO.setSessionKey(req.getSessionKey());
        return safeWarnMsgReqDTO;
    }

    private void desensitization(boolean risk, RiskQueryRspDTO riskQueryRsp, SendMsgReqDTO req) throws ValidateException {
        String userKey = req.filter1();
        LoggerUtils.info(log, "[desensitization][{}] 敏感词脱敏请求参数：{}，风控响应：{}", userKey, FastJsonUtils.toJSONString(req), FastJsonUtils.toJSONString(riskQueryRsp));

        //消息内容
        MsgContentDTO msgContent = req.getMsgContent();
        String content = msgContent.getContent();

        try {
            if (risk) {
                Map<String, String> obj = riskQueryRsp.getData().getObj();
                if (Objects.isNull(obj)) {
                    return;
                }

                for (String sensitiveWord : obj.values()) {
                    String value = DesensitizationUtils.desensitization(content, sensitiveWord);
                    if (StringUtils.isNotBlank(value)) {
                        content = value;
                    }
                }

                msgContent.setContent(content);
                LoggerUtils.info(log, "[desensitization][{}] 敏感词脱敏后：{}", userKey, FastJsonUtils.toJSONString(req));
            }
        } catch (Exception e) {
            LoggerUtils.error(log, "[desensitization] 敏感词脱敏异常：{}", userKey, e);
        }

        if (StringUtils.isNotBlank(content) && content.contains("*")) {
            throw new ValidateException(BIZ_ERROR_FACTORY.riskDesensitization(content));
        }
    }

    private void sendAndSaveOnTripMsg(SendMsgReqDTO req, SfcImInfo sfcImInfo, boolean risk, SfcImMsgRecord sysMsg, boolean sendWarn, OrderDetailDTO orderDetail) throws ValidateException {
        //组装消息存储入参
        SfcImMsgRecord msgRecord = buildMsg(req, orderDetail);
        if (risk && !sendWarn) {
            msgRecord.setUpdateTime(new Date());
            sysMsg.setUpdateTime(new Date());
            sysMsg.setOrderStatus(orderDetail.getStatus());
            msgRecord.setOrderStatus(orderDetail.getStatus());
            sfcImMsgRecordDao.save(msgRecord);
            sfcImMsgRecordDao.save(sysMsg);
            LoggerUtils.info(log, "[sendAndSaveOnTripMsg] 保存im消息，乘客发送消息给司机，sfcImMsgRecord：", FastJsonUtils.toJSONString(msgRecord));
        } else {
            msgRecord.setMsgSendStatus(MsgSendStatusEnum.FAIL.getCode());
            msgRecord.setUpdateTime(new Date());
            msgRecord.setOrderStatus(orderDetail.getStatus());
            sfcImMsgRecordDao.save(msgRecord);
            SendMsgReqDTO sendMsgReqDTO = new SendMsgReqDTO();
            BeanUtils.copyProperties(req, sendMsgReqDTO);
            sendMsgReqDTO.setMemberId(orderDetail.getMemberId());
            sendMsgReqDTO.setUnionId(orderDetail.getUnionId());
            //发送消息
            BaseResponseDTO baseResponseDTO = sendService.sendMsg(sendMsgReqDTO, orderDetail);
            if (Objects.nonNull(baseResponseDTO)) {
                msgRecord.setMsgSendStatus(MsgSendStatusEnum.SUCCESS.getCode());
                msgRecord.setUpdateTime(new Date());
                msgRecord.setOrderStatus(orderDetail.getStatus());
                sfcImMsgRecordDao.updateById(msgRecord);
                LoggerUtils.info(log, "[sendAndSaveOnTripMsg] 保存im消息，乘客发送消息给司机，sfcImMsgRecord：{}", FastJsonUtils.toJSONString(msgRecord));
            } else {
                msgRecord.setUpdateTime(new Date());
                msgRecord.setOrderStatus(orderDetail.getStatus());
                sfcImMsgRecordDao.updateById(msgRecord);
                LoggerUtils.info(log, "[sendAndSaveOnTripMsg] 保存im消息，乘客发送消息给司机，sfcImMsgRecord：{}", FastJsonUtils.toJSONString(msgRecord));
                throw new ValidateException(BIZ_ERROR_FACTORY.msgSendFailError(req.getMsgId()));
            }
        }

        if (sendWarn) {
            //组装&保存系统信息
            SfcImMsgRecord record = buildSysMsg(req, MsgTypeEnum.SAFE_WARN_TEXT.getMsgType(),
                    MsgTipsConstant.SAFE_RISK_WARN, Boolean.TRUE, orderDetail);
            sfcImMsgRecordDao.save(record);
        }

        //保存或更新会话
        insertOrUpdateSfcImInfo(req, sfcImInfo, msgRecord,req.getPlatId());
    }

    private void sendAndSaveMsg(SendMsgReqDTO req, SfcImInfo sfcImInfo, boolean risk, SfcImMsgRecord msgRecord, boolean sendWarn, OrderDetailDTO orderDetail) throws ValidateException {
        //组装消息存储入参
        SfcImMsgRecord msg = buildMsg(req, orderDetail);
        int status = orderDetail.getStatus();
        if (risk) {
            msg.setUpdateTime(new Date());
            msg.setOrderStatus(status);
            sfcImMsgRecordDao.save(msg);
        } else {
            msg.setMsgSendStatus(MsgSendStatusEnum.FAIL.getCode());
            msg.setUpdateTime(new Date());
            msg.setOrderStatus(orderDetail.getStatus());
            sfcImMsgRecordDao.save(msg);
            SendMsgReqDTO sendMsgReqDTO = new SendMsgReqDTO();
            BeanUtils.copyProperties(req, sendMsgReqDTO);
            sendMsgReqDTO.setMemberId(orderDetail.getMemberId());
            sendMsgReqDTO.setUnionId(orderDetail.getUnionId());
            //发送消息
            BaseResponseDTO baseResponseDTO = sendService.sendMsg(sendMsgReqDTO, orderDetail);
            if (Objects.nonNull(baseResponseDTO)) {
                msg.setMsgSendStatus(MsgSendStatusEnum.SUCCESS.getCode());
                msg.setUpdateTime(new Date());
                msg.setOrderStatus(status);
                sfcImMsgRecordDao.updateById(msg);
                //推送系统消息
                pushSystemMsg(buildSysMsgPush(req, req.getSessionKey(), msgRecord.getMsgContent()));
            }else {
                msg.setUpdateTime(new Date());
                msg.setOrderStatus(status);
                sfcImMsgRecordDao.updateById(msg);
                throw new ValidateException(BIZ_ERROR_FACTORY.msgSendFailError(req.getMsgId()));
            }
        }

        msgRecord.setUpdateTime(new Date());
        msgRecord.setOrderStatus(status);
        sfcImMsgRecordDao.save(msgRecord);

        if (sendWarn) {
            //组装或保存系统信息
            SfcImMsgRecord record = buildSysMsg(req, MsgTypeEnum.SAFE_WARN_TEXT.getMsgType(),
                    MsgTipsConstant.SAFE_RISK_WARN, Boolean.TRUE, orderDetail);
            sfcImMsgRecordDao.save(record);

        }

        //存储或更新会话
        insertOrUpdateSfcImInfo(req, sfcImInfo, msg,req.getPlatId());
    }


    private SafeNightWarnMsgReqDTO buildSysMsgPush(SendMsgReqDTO sendMsgReqDTO, String sessionKey, String content) {
        MsgContentDTO msgContentDTO = new MsgContentDTO();
        msgContentDTO.setMsgId(sendMsgReqDTO.getMsgId());
        msgContentDTO.setMsgType(MsgTypeEnum.SYSTEM.getMsgType());
        msgContentDTO.setContent(content);
        SafeNightWarnMsgReqDTO req = new SafeNightWarnMsgReqDTO();
        req.setMsgId(sendMsgReqDTO.getMsgId());
        req.setMsgContent(msgContentDTO);
        req.setPlateNumber(sendMsgReqDTO.getPlateNumber());
        req.setSupplierCode(sendMsgReqDTO.getSupplierCode());
        req.setDriverId(sendMsgReqDTO.getDriverId());
        req.setOrderId(sendMsgReqDTO.getOrderId());
        req.setSupplierOrderId(sendMsgReqDTO.getSupplierOrderId());
        req.setUnionId(sendMsgReqDTO.getUnionId());
        req.setOpenId(Strings.EMPTY);
        req.setMemberId(StringUtils.isEmpty(sendMsgReqDTO.getMemberId()) ? "0" : sendMsgReqDTO.getMemberId());
        req.setImKey(req.filter1());
        req.setMsgType(MsgTypeEnum.SYSTEM.getMsgType());
        req.setSessionKey(sessionKey);
        return req;
    }

    private void pushSystemMsg(SafeNightWarnMsgReqDTO msgReqDTO) {
        // 推送消息到当前会话
        try {
            Simple<?> currentSimple = pushApi.safeNightMsgPush(msgReqDTO);
            if (Objects.nonNull(currentSimple) && Objects.equals(currentSimple.getStatus(), Simple.OK.getStatus())) {
                LoggerUtils.info(log, "[pushToMultipleSessions] 当前会话推送成功，sessionKey：{}，msgId：{}",
                        msgReqDTO.getSessionKey(), msgReqDTO.getMsgId());
            } else {
                LoggerUtils.warn(log, "[pushToMultipleSessions] 当前会话推送失败，sessionKey：{}，msgId：{}，响应：{}",
                        msgReqDTO.getSessionKey(), msgReqDTO.getMsgId(), FastJsonUtils.toJSONString(currentSimple));
            }
        } catch (Exception e) {
            LoggerUtils.error(log, "[pushToMultipleSessions] 当前会话推送异常，sessionKey：{}，msgId：{}",
                    msgReqDTO.getSessionKey(), msgReqDTO.getMsgId(), e);
        }
    }

    private void saveRecord(SendMsgReqDTO req, OrderDetailDTO orderDetail, String content) {
        SfcImMsgRecord sfcImMsgRecord = new SfcImMsgRecord();
        BeanUtils.copyProperties(req, sfcImMsgRecord);
        sfcImMsgRecord.setSessionKey(req.getSessionKey());
        sfcImMsgRecord.setMsgId(UUID.randomUUID().toString().replace("-", Strings.EMPTY));
        sfcImMsgRecord.setMsgSender(MsgSenderEnum.SYS.getCode());
        sfcImMsgRecord.setMemberId(StringUtils.isEmpty(orderDetail.getMemberId()) ? 0 : Long.parseLong(orderDetail.getMemberId()));
        sfcImMsgRecord.setMsgType(MsgTypeEnum.SYSTEM.getMsgType());
        sfcImMsgRecord.setSceneType(req.getSceneType());
        sfcImMsgRecord.setMsgStatus(MsgStatusEnum.READ.getCode());
        sfcImMsgRecord.setMsgContent(SystemMsgErrorMappingEnum.mapErrorMessage(content));
        sfcImMsgRecord.setMsgSendStatus(MsgSendStatusEnum.SUCCESS.getCode());
        sfcImMsgRecord.setMsgVersion(MSG_VERSION);
        sfcImMsgRecord.setOrderStatus(orderDetail.getStatus());
        sfcImMsgRecord.setUpdateTime(new Date());
        sfcImMsgRecord.setOrderStatus(orderDetail.getStatus());
        sfcImMsgRecordDao.save(sfcImMsgRecord);
    }


    private void savePassageRecord(SendMsgReqDTO req, OrderDetailDTO orderDetail) throws ValidateException {
        //组装消息存储入参
        SfcImMsgRecord msg = buildMsg(req, orderDetail);
        msg.setMsgSendStatus(MsgSendStatusEnum.FAIL.getCode());
        msg.setUpdateTime(new Date());
        msg.setOrderStatus(orderDetail.getStatus());
        sfcImMsgRecordDao.save(msg);
    }
}
