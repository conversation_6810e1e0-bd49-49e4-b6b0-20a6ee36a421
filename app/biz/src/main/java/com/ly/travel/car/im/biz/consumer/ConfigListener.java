package com.ly.travel.car.im.biz.consumer;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.bean.Simple;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.tcbase.config.ChangeConfigData;
import com.ly.tcbase.config.ConfigCenterClient;
import com.ly.tcbase.config.ConfigChanged;
import com.ly.travel.car.im.facade.enums.MsgTypeEnum;
import com.ly.travel.car.im.integration.client.api.PushApi;
import com.ly.travel.car.im.integration.request.DriverMsgPushReqDTO;
import com.ly.travel.car.im.model.dto.VersionDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.UUID;

@Service
@Slf4j
public class ConfigListener {
    @Resource
    private PushApi pushApi;
    private static final String key = "im.ver";
    private static final String version = "v2";

    @PostConstruct
    public void versionListener() {
        try {
            ConfigCenterClient.onConfigChanged(new ConfigChanged() {
                @Override
                public void configChanged(ChangeConfigData changeInfo) {
                    changeInfo.getUpdateList().forEach(e -> {
                        if (StringUtils.equalsIgnoreCase(e.getKey(), key)) {
                            VersionDTO versionDTO = JSONObject.parseObject(e.getValue(), VersionDTO.class);
                            LoggerUtils.info(log, "[versionListener] IM版本配置信息：{}", FastJsonUtils.toJSONString(versionDTO));
                            //v2和配置版本不一致时 触发广播通知
                            if (!StringUtils.equals(version, versionDTO.getVersion())) {
                                DriverMsgPushReqDTO req = new DriverMsgPushReqDTO();
                                req.setMsgType(MsgTypeEnum.SWITCH_NOTIFICATION.getMsgType());
                                req.setMsgId(UUID.randomUUID().toString().replace("-", Strings.EMPTY));

                                //切换开关通知
                                LoggerUtils.info(log, "[versionListener] 版本切换通知 req：{}", FastJsonUtils.toJSONString(req));
                                Simple<?> simple = pushApi.driverMsgPush(req);
                                LoggerUtils.info(log, "[versionListener] 版本切换通知 rsp：{}", FastJsonUtils.toJSONString(simple));
                            }
                        }
                    });
                }
            });
        } catch (Exception e) {
            LoggerUtils.warn(log, "[versionListener] 版本切换通知异常", e);
        }
    }
}
