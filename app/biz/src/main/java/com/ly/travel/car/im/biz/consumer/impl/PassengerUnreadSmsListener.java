package com.ly.travel.car.im.biz.consumer.impl;

import com.ly.car.utils.CarAssert;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.im.biz.annotations.ChatMsgTag;
import com.ly.travel.car.im.biz.consumer.SmsListener;
import com.ly.travel.car.im.biz.service.OrderService;
import com.ly.travel.car.im.dal.dao.SfcImMsgRecordDao;
import com.ly.travel.car.im.dal.entity.SfcImMsgRecord;
import com.ly.travel.car.im.integration.client.api.SmsApi;
import com.ly.travel.car.im.integration.client.redis.impl.RedisClientProxy;
import com.ly.travel.car.im.integration.request.SmsReqDTO;
import com.ly.travel.car.im.integration.response.SmsRspDTO;
import com.ly.travel.car.im.integration.utils.RedisKeyBuilder;
import com.ly.travel.car.im.model.config.ImConfigCenter;
import com.ly.travel.car.im.model.dto.CarPassengerUnreadSmsDTO;
import com.ly.travel.car.im.model.dto.OrderDetailDTO;
import com.ly.travel.car.im.model.dto.SmsRuleConfigDTO;
import com.ly.travel.car.im.model.enums.ChatMsgTagEnum;
import com.ly.travel.car.im.model.enums.GenerateLinkTypeEnum;
import com.ly.travel.car.im.model.enums.MsgStatusEnum;
import com.ly.travel.car.im.model.utils.DateToolsUtils;
import com.ly.travel.car.tradecore.model.enums.OrderState;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Service
@ChatMsgTag(ChatMsgTagEnum.im_passenger_unread_sms)
@Slf4j
public class PassengerUnreadSmsListener implements SmsListener {
    @Resource
    private RedisClientProxy redisClientProxy;
    @Resource
    private SfcImMsgRecordDao sfcImMsgRecordDao;
    @Resource
    private OrderService orderService;
    @Resource
    private SmsApi smsApi;

    @Override
    public boolean handle(String msgBody) {
        LoggerUtils.info(log, "[passengerUnreadSmsListener][req] 乘客未读短信发送 req：{}", msgBody);
        CarPassengerUnreadSmsDTO dto = FastJsonUtils.fromJSONString(msgBody, CarPassengerUnreadSmsDTO.class);
        String orderId = dto.getOrderId();
        boolean holdLock = Boolean.FALSE;
        try {

            holdLock = redisClientProxy.setnx(RedisKeyBuilder.orderLock(orderId), System.currentTimeMillis() + Strings.EMPTY, 60);
            CarAssert.isTrue(holdLock, "乘客未读短信发送，获取IM订单锁失败：" + orderId, dto);

            SfcImMsgRecord msgRecord = sfcImMsgRecordDao.findByMsgId(dto.getMsgId());
            if (Objects.isNull(msgRecord)) {
                LoggerUtils.warn(log, "[passengerUnreadSmsListener][{}] 消息记录不存在", orderId);
                return Boolean.TRUE;
            }

            if (Objects.equals(msgRecord.getMsgStatus(), MsgStatusEnum.READ.getCode())) {
                LoggerUtils.warn(log, "[passengerUnreadSmsListener][{}] 消息已读，忽略", orderId);
                return Boolean.TRUE;
            }

            OrderDetailDTO orderDetail = orderService.getOrderDetail(orderId);
            if (Objects.isNull(orderDetail)) {
                LoggerUtils.warn(log, "[passengerUnreadSmsListener][{}] 订单不存在", orderId);
                return Boolean.TRUE;
            }

            if (Objects.equals(OrderState.CANCELED.getCode(), orderDetail.getStatus())) {
                LoggerUtils.warn(log, "[passengerUnreadSmsListener][{}] 订单已取消，忽略", orderId);
                return Boolean.TRUE;
            }

            String num = redisClientProxy.getString(RedisKeyBuilder.IM_SMS_NUM_KEY + orderId);
            int imSmsNum = StringUtils.isNotBlank(num) ? Integer.parseInt(num) : 0;
            SmsRuleConfigDTO smsRuleConfigDTO = ImConfigCenter.matchIMSmsRuleConfigByPlatId(orderDetail.getPlatId());
            if (imSmsNum >= smsRuleConfigDTO.getSentLimitCount()) {
                LoggerUtils.warn(log, "[passengerUnreadSmsListener][{}] 当日发送数量已到上限，忽略", orderId);
                return Boolean.TRUE;
            }

            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("@BookingTime", DateToolsUtils.toString(dto.getEarliestTime(), DateToolsUtils.PATTERN_DATE_TIME_ZERO));
            paramMap.put("@Destination", orderDetail.getEndAddress());
            String url = smsApi.generateLinkApi(orderDetail, GenerateLinkTypeEnum.SMS.getType());
            if (StringUtils.isNotEmpty(url) && "-1".equals(url)) {
                LoggerUtils.warn(log, "[passengerUnreadSmsListener][{}] 订单渠道不支持发短信", orderId);
                return Boolean.TRUE;
            }

            paramMap.put("@Link", url);
            SmsReqDTO req = new SmsReqDTO();
            req.setNodeId(170171);
            req.setMobile(dto.getPassengerPhone());
            req.setInputParameters(paramMap);

            SmsRspDTO rsp = smsApi.send(req);
            if (StringUtils.equals(rsp.getCode(), "0")) {
                long timeout = DateToolsUtils.getLeftTimeOfToday() + (long) (smsRuleConfigDTO.getDay() - 1) * 24 * 3600;
                redisClientProxy.save(RedisKeyBuilder.IM_SMS_NUM_KEY + orderId, String.valueOf(imSmsNum + 1), timeout);
                LoggerUtils.info(log, "[passengerUnreadSmsListener][{}] 乘客未读短信发送成功: {}", orderId, rsp.getMsg());
            } else {
                LoggerUtils.warn(log, "[passengerUnreadSmsListener][{}] 乘客未读短信发送失败: {}", orderId, rsp.getMsg());
            }
            return Boolean.TRUE;
        } catch (Exception e) {
            LoggerUtils.warn(log, "[passengerUnreadSmsListener][{}] 乘客未读短信发送异常", orderId, e);
        } finally {
            if (holdLock) {
                redisClientProxy.remove(RedisKeyBuilder.orderLock(orderId));
            }
        }
        return Boolean.FALSE;
    }
}
