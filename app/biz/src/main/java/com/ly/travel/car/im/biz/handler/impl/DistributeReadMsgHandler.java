package com.ly.travel.car.im.biz.handler.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.sof.facade.base.BaseResponseDTO;
import com.ly.sof.utils.log.LogContextUtils;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.im.biz.config.AsyncConfig;
import com.ly.travel.car.im.biz.error.BizErrorFactory;
import com.ly.travel.car.im.biz.error.ValidateException;
import com.ly.travel.car.im.biz.handler.BaseTools;
import com.ly.travel.car.im.biz.handler.MsgHandler;
import com.ly.travel.car.im.biz.service.SendService;
import com.ly.travel.car.im.biz.utils.SessionKeyUtil;
import com.ly.travel.car.im.dal.dao.SfcImInfoDao;
import com.ly.travel.car.im.dal.dao.SfcImMsgRecordDao;
import com.ly.travel.car.im.dal.entity.SfcImInfo;
import com.ly.travel.car.im.dal.entity.SfcImMsgRecord;
import com.ly.travel.car.im.facade.dto.ContentDTO;
import com.ly.travel.car.im.facade.dto.MemberInfoDTO;
import com.ly.travel.car.im.facade.dto.ThirdPartyWechatBaseResponse;
import com.ly.travel.car.im.facade.enums.MsgSenderEnum;
import com.ly.travel.car.im.facade.enums.MsgTypeEnum;
import com.ly.travel.car.im.facade.request.SendMsgReqDTO;
import com.ly.travel.car.im.facade.response.SendMsgRspDTO;
import com.ly.travel.car.im.integration.client.redis.impl.RedisClientProxy;
import com.ly.travel.car.im.integration.utils.RedisKeyBuilder;
import com.ly.travel.car.im.model.config.ImConfigCenter;
import com.ly.travel.car.im.model.dto.OrderDetailDTO;
import com.ly.travel.car.im.model.dto.ThirdPartyConfigDTO;
import com.ly.travel.car.im.model.enums.MsgStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.ly.travel.car.im.biz.utils.DecryptMemberInfoUtil.getMemberIdListByMemberId;
import static com.ly.travel.car.im.biz.utils.DecryptMemberInfoUtil.getMemberIdListByUnionId;
import static com.ly.travel.car.im.integration.utils.RedisKeyBuilder.IM_DISTRIBUTION_MSG_KEY;

/**
 * 已读消息处理器(已读消息不落库)
 */
@Service
@Slf4j
public class DistributeReadMsgHandler extends BaseTools implements MsgHandler {
    @Resource
    private SendService sendService;
    @Resource
    private SfcImMsgRecordDao sfcImMsgRecordDao;
    @Resource
    private SfcImInfoDao sfcImInfoDao;
    @Resource
    private RedisClientProxy redisClientProxy;

    @Resource
    private AsyncConfig asyncConfig;

    private static final Integer SESSION_KEY_LENGTH = 3;
    private static final BizErrorFactory BIZ_ERROR_FACTORY = BizErrorFactory.getInstance();

    private static final String YueYue = "YueYue";

    @Override
    public SendMsgRspDTO handle(SendMsgReqDTO req, OrderDetailDTO orderDetail) throws ValidateException {
        LogContextUtils.setCategory("DistributeReadMsgHandler");
        LogContextUtils.setFilter1(req.filter1());
        //会话信息
        SfcImInfo sfcImInfo = getSfcImInfo(req.getSessionKey(),orderDetail.getPlatId());
        req.getMsgContent().setMsgType(MsgTypeEnum.READ_ACK.getMsgType());
        req.setMsgId(IdUtil.getSnowflake(redisClientProxy.incrby(IM_DISTRIBUTION_MSG_KEY, 1) % 31).nextIdStr());
        //发送已读消息

        CompletableFuture.runAsync(() -> {
            //已读特殊处理
            String supplierCode = req.getSupplierCode();
            ContentDTO contentDTO = FastJsonUtils.fromJSONString(req.content(), ContentDTO.class);
            //读取乘客未读消息
            ArrayList<String> sessionKeys = new ArrayList<>();
            sessionKeys.add(req.getSessionKey());
            String newSessionKey = SessionKeyUtil.getNewSessionKey(req.getSessionKey());
            if (StringUtils.isNotEmpty(newSessionKey)) {
                sessionKeys.add(newSessionKey);
            }
            List<SfcImMsgRecord> sfcImMsgRecords = sfcImMsgRecordDao.queryUnreadMsg(sessionKeys, MsgSenderEnum.DRIVER.getCode());
            LoggerUtils.info(log, "查询未读司机消息：{}", FastJsonUtils.toJSONString(sfcImMsgRecords));
            if (CollectionUtils.isEmpty(sfcImMsgRecords)) {
                return;
            }
                contentDTO.setData(supplierCode.contains(YueYue) ? sfcImMsgRecords.stream()
                        .map(SfcImMsgRecord::getMsgId)
                        .map(id -> id.length() > 32 ? id.substring(0, 32) : id)
                        .collect(Collectors.joining(",")) : sfcImMsgRecords.stream()
                        .map(SfcImMsgRecord::getMsgId)
                        .collect(Collectors.joining(",")));
                req.setReadContent(FastJsonUtils.toJSONString(contentDTO));
            BaseResponseDTO baseResponseDTO = null;
            try {
                baseResponseDTO = sendService.sendMsg(req, orderDetail);
            } catch (ValidateException e) {
                LoggerUtils.warn(log, "乘客消息发送司机异常 req：{}, errorCode: {}, errorMessage: {}",
                        FastJsonUtils.toJSONString(req),
                        e.getErrorCode(),
                        e.getError().getMessage(),
                        e);
            }
            if (Objects.nonNull(baseResponseDTO)) {
                LoggerUtils.info(log, "消息发送成功，result：{}", FastJsonUtils.toJSONString(baseResponseDTO));
            }

            //更新乘客已读消息来源
            updateUnreadMsgSource(req);

            //更新司机发送乘客消息
            updateUnreadMsg(req.getSessionKey(), sfcImInfo);
        }, asyncConfig.readMsgSendExecutor());
        return SendMsgRspDTO.success(req.getTraceId(), req.getMsgContent().getMsgId());
    }

    /**
     * 更新乘客已读消息来源
     */
    private void updateUnreadMsgSource(SendMsgReqDTO req) {
        String readSourceRefId = req.getReadSourceRefId();
        String pageSource = req.getPageSource();

        try {
            if (StringUtils.isEmpty(pageSource) && StringUtils.isEmpty(readSourceRefId)) {
                LoggerUtils.warn(log, "[updateUnreadMsgSource][{}] 乘客已读消息来源为空", req.getMsgId());
                return;
            }


            //读取乘客未读消息
            ArrayList<String> sessionKeys = new ArrayList<>();
            sessionKeys.add(req.getSessionKey());
            String newSessionKey = SessionKeyUtil.getNewSessionKey(req.getSessionKey());
            if (StringUtils.isNotEmpty(newSessionKey)) {
                sessionKeys.add(newSessionKey);
            }
            List<SfcImMsgRecord> unReadMsgRecordList = sfcImMsgRecordDao.queryUnreadMsg(sessionKeys, MsgSenderEnum.DRIVER.getCode());
            if (CollectionUtils.isEmpty(unReadMsgRecordList)) {
                LoggerUtils.warn(log, "[updateUnreadMsgSource][{}] 读取司机发消息记录为空", req.getMsgId());
                return;
            }

            unReadMsgRecordList.forEach(sfcImMsgRecord -> {
                sfcImMsgRecord.setPageSource(urlDecode(pageSource));
                sfcImMsgRecord.setReadSourceRefid(StringUtils.isNotEmpty(readSourceRefId) ? readSourceRefId : Strings.EMPTY);
                sfcImMsgRecord.setUpdateTime(new Date());
                sfcImMsgRecordDao.updateById(sfcImMsgRecord);
            });
        } catch (Exception e) {
            LoggerUtils.error(log, "[updateUnreadMsgSource][{}] 更新乘客已读消息来源异常", req.getMsgId(), e);
        }
    }

    private void updateUnreadMsg(String sessionKey, SfcImInfo sfcImInfo) {
        LoggerUtils.info(log, "[updateUnreadMsg] 更新司机发送乘客消息，sfcImInfo：{}", FastJsonUtils.toJSONString(sfcImInfo));
        if (Objects.nonNull(sfcImInfo)) {
        //更新所有对话为已读
        SfcImMsgRecord sfcImMsgRecord = new SfcImMsgRecord();
        sfcImMsgRecord.setUpdateTime(new Date());
        sfcImMsgRecord.setMsgStatus(MsgStatusEnum.READ.getCode());

        sfcImMsgRecordDao.update(sfcImMsgRecord, new LambdaQueryWrapper<SfcImMsgRecord>()
                .eq(SfcImMsgRecord::getOrderId, sfcImInfo.getOrderId())
                .eq(SfcImMsgRecord::getMsgStatus, MsgStatusEnum.UNREAD)
                .eq(SfcImMsgRecord::getMsgSender, MsgSenderEnum.DRIVER.getCode()));
         //更新乘客未读的车主消息数量
            SfcImInfo updateUnread = new SfcImInfo();
            updateUnread.setId(sfcImInfo.getId());
            updateUnread.setUpdateTime(new Date());
            updateUnread.setUnreadMsgNum(0);
            sfcImInfoDao.updateById(updateUnread);
            redisClientProxy.remove(RedisKeyBuilder.IM_INFO_KEY + sfcImInfo.getSessionKey());

            //更新替代车主
            String userId = "";
            String oldSessionKey = sfcImInfo.getSessionKey();
            String oldUserId = splitOldSessionKey4UserId(oldSessionKey);
            if (NumberUtil.isLong(oldUserId)) {
                userId = getUnionIdByMemberId(oldUserId);
            } else if (StringUtils.isNotBlank(oldUserId)) {
                Long memberIdByUnionId = getMemberIdByUnionId(oldUserId);
                userId = Objects.nonNull(memberIdByUnionId) ? memberIdByUnionId.toString() : null;
            }
            String otherSessionKey = replaceSessionKey(oldSessionKey, userId);
            if (StringUtils.isNotEmpty(otherSessionKey)) {
                SfcImInfo otherSession = sfcImInfoDao.findBySessionKey(otherSessionKey, null);
                if (Objects.nonNull(otherSession)) {
                    otherSession.setUpdateTime(new Date());
                    otherSession.setUnreadMsgNum(0);
                    sfcImInfoDao.updateById(otherSession);
                    redisClientProxy.remove(RedisKeyBuilder.IM_INFO_KEY + otherSession.getSessionKey());
                }
            }
        }else {
            //更新替代车主
            String userId = "";
            String oldSessionKey = sessionKey;
            String oldUserId = splitOldSessionKey4UserId(oldSessionKey);
            if (NumberUtil.isLong(oldUserId)) {
                userId = getUnionIdByMemberId(oldUserId);
            } else if (StringUtils.isNotBlank(oldUserId)) {
                Long memberIdByUnionId = getMemberIdByUnionId(oldUserId);
                userId = Objects.nonNull(memberIdByUnionId) ? memberIdByUnionId.toString() : null;
            }
            String otherSessionKey = replaceSessionKey(oldSessionKey, userId);
            if (StringUtils.isNotEmpty(otherSessionKey)) {
                SfcImInfo otherSession = sfcImInfoDao.findBySessionKey(otherSessionKey, null);
                if (Objects.nonNull(otherSession)) {
                    otherSession.setUpdateTime(new Date());
                    otherSession.setUnreadMsgNum(0);
                    sfcImInfoDao.updateById(otherSession);
                    redisClientProxy.remove(RedisKeyBuilder.IM_INFO_KEY + otherSession.getSessionKey());
                }
            }
        }
    }

    private String replaceSessionKey(String sessionKey, String userId) {
        if(StringUtils.isBlank(sessionKey)|| StringUtils.isBlank(userId)){
            return null;
        }
        // 会话标识（供应商Code/子供应商Code + 会员ID/unionId + 司机ID）  例子CaoCao_1234567890_1234567890
        // 将中间的换成userId
        String[] sessionKeyParts = sessionKey.split("_");
        if (sessionKeyParts.length < SESSION_KEY_LENGTH) {
            LoggerUtils.warn(log, "会话标识格式不正确，无法替换 sessionKey: {}", sessionKey);
            return null;
        }
        if(sessionKeyParts.length == SESSION_KEY_LENGTH){
            return sessionKeyParts[0] + "_" + userId + "_" + sessionKeyParts[2];
        }

        LoggerUtils.warn(log, "会话标识分隔后超过3位  sessionKey: {}", sessionKey);
        return sessionKeyParts[0] + "_" + userId + "_" + sessionKeyParts[SESSION_KEY_LENGTH - 1];

    }

    private String getUnionIdByMemberId(String memberId) {

        ThirdPartyConfigDTO thirdPartyConfigDTO = ImConfigCenter.getThirdPartyConfig();
        ThirdPartyWechatBaseResponse response = null;
        if (StringUtils.isNotBlank(memberId)) {
            //如果是APP端，memberId是数字
            response = getMemberIdListByMemberId(memberId, thirdPartyConfigDTO);
        }
        if(Objects.nonNull(response)&& CollectionUtils.isNotEmpty(response.getData())){
            return response.getData().stream().filter(i -> Objects.equals(i.getMemberSystem(), 33))
                    .map(MemberInfoDTO::getUnionId).findFirst().orElse(null);
        }
        return null;
    }


    private Long getMemberIdByUnionId(String unionId) {
        ThirdPartyConfigDTO thirdPartyConfigDTO = ImConfigCenter.getThirdPartyConfig();
        ThirdPartyWechatBaseResponse response = null;
        if (StringUtils.isNotBlank(unionId)) {
            response = getMemberIdListByUnionId(unionId, thirdPartyConfigDTO);
        }
        if(Objects.nonNull(response)&& CollectionUtils.isNotEmpty(response.getData())){
            return response.getData().stream().filter(i -> Objects.equals(i.getMemberSystem(), 0))
                    .map(MemberInfoDTO::getMemberId).findFirst().orElse(null);
        }
        return null;
    }


    private static String splitOldSessionKey4UserId(String oldSessionKey) {
        String[] splits = oldSessionKey.split("_");
        if(splits.length == SESSION_KEY_LENGTH){
            return   splits[1];
        }else if(splits.length > SESSION_KEY_LENGTH){
            // XXXX_XXXX_XXXX_XXXX_XXX  中间的都认为是用户ID
            return String.join("_", Arrays.copyOfRange(splits, 1, splits.length - 1));
        }
        return null;
    }

    private static String urlDecode(String pageSource) {
        if (StringUtils.isEmpty(pageSource)) {
            return Strings.EMPTY;
        }

        try {
            return URLDecoder.decode(pageSource, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            LoggerUtils.error(log, "[urlDecode] pageSource：{}", pageSource, e);
        }

        return Strings.EMPTY;
    }
}
