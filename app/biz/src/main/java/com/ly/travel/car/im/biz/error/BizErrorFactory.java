/**
 * LY.com Inc.
 * Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.ly.travel.car.im.biz.error;

import com.ly.sof.api.error.AbstractErrorFactory;
import com.ly.sof.api.error.LYError;

/**
 * BizErrorFactory
 *
 * @version Id: BizErrorFactory, v 0.1 2023/12/13 16:38 icanci Exp $
 */
public class BizErrorFactory extends AbstractErrorFactory {
    /**
     * 分隔符
     */
    private static final String SPLIT = ",";

    /**
     * 获取异常工厂单例
     *
     * @return 异常工厂单例
     */
    public static BizErrorFactory getInstance() {
        return BizErrorFactoryHolder.INSTANCE;
    }

    private static final class BizErrorFactoryHolder {
        /**
         * 单例
         */
        private static final BizErrorFactory INSTANCE = new BizErrorFactory();
    }

    /**
     * 获取资源文件名称
     *
     * @return 资源文件名称
     */
    @Override
    protected String provideErrorBundleName() {
        return "biz";
    }

    /**
     * LY0510010001=订单不存在异常, 订单号:{0}
     *
     * @param orderSerialNo 订单号
     * @return 同程异常
     */
    public LYError orderNotFoundError(String orderSerialNo) {
        return createError("LY0510010001", orderSerialNo);
    }

    /**
     * LY0510010007=会话不存在异常, id:{0}
     *
     * @param id 主键
     * @return 同程异常
     */
    public LYError chatInfoNotFoundError(String id) {
        return createError("LY0510010007", id);
    }

    /**
     * LY0510010002=该消息含敏感词，无法发送，msgId:{0}
     *
     * @return
     */
    public LYError msgSensitiveWordsError(String msgId) {
        return createError("LY0510010002", msgId);
    }

    /**
     * LY0510010003=消息接收处理异常，msgId:{0}
     *
     * @return
     */
    public LYError msgReceiveHandleError(String msgId) {
        return createError("LY0510010003", msgId);
    }

    /**
     * LY0510010004=发送消息受限，msgId:{0}
     *
     * @return
     */
    public LYError msgLimitError(String msgId) {
        return createError("LY0510010004", msgId);
    }

    /**
     * LY0510010005=消息发送失败异常，msgId:{0}
     *
     * @return
     */
    public LYError msgSendFailError(String msgId) {
        return createError("LY0510010005", msgId);
    }

    /**
     * LY0510010006=获取并发锁失败
     *
     * @return
     */
    public LYError frequentError() {
        return createError("LY0510010006");
    }

    /**
     * LY0510010008=消息发送重复异常, 消息id:{0}
     *
     * @return
     */
    public LYError duplicateInformation(String msgId) {
        return createError("LY0510010008", msgId);
    }

    /**
     * LY0510010009=未知消息类型异常, 消息类型:{0}
     *
     * @return
     */
    public LYError unknownMsgTypeError(Integer msgType) {
        return createError("LY0510010009", msgType);
    }

    /**
     * LY0510010010=供应商订单号查询同程订单号为空异常, 供应商订单号:{0}
     *
     * @return
     */
    public LYError supOrderQueryTcOrderError(String supplierOrderId) {
        return createError("LY0510010010", supplierOrderId);
    }

    /**
     * LY0510010011=订单处于派单异常，订单号:{0}
     *
     * @return
     */
    public LYError orderDispatchError(String orderId) {
        return createError("LY0510010011", orderId);
    }

    /**
     * LY0510010012=订单已取消异常，订单号:{0}
     *
     * @return
     */
    public LYError orderCancelError(String orderId) {
        return createError("LY0510010012", orderId);
    }

    /**
     * LY0510010013=订单已改派异常，订单号:{0}
     *
     * @return
     */
    public LYError orderReassignmentError(String orderId) {
        return createError("LY0510010013", orderId);
    }

    /**
     * LY0510010014=司机不支持聊天异常，司机会话id:{0}
     *
     * @return
     */
    public LYError driverNotChatError(String driverChatId) {
        return createError("LY0510010014", driverChatId);
    }

    /**
     * LY0510010015=发送内容触发风控敏感词，脱敏后内容：{0}
     *
     * @return
     */
    public LYError riskDesensitization(String content) {
        return createError("LY0510010015", content);
    }
}
