package com.ly.travel.car.im.biz.service.impl;

import com.ly.travel.car.im.biz.service.WxSubService;
import com.ly.travel.car.im.facade.dto.BaseDTO;
import com.ly.travel.car.im.facade.request.GetMsgTemplateReqDTO;
import com.ly.travel.car.im.facade.response.CallBackRspDTO;
import com.ly.travel.car.im.facade.response.GetMsgTemplateRspDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class WxSubServiceImpl implements WxSubService {

    @Override
    public GetMsgTemplateRspDTO getMsgTemplate(GetMsgTemplateReqDTO req) {
        return null;
    }

    @Override
    public CallBackRspDTO callBack(BaseDTO req) {
        return null;
    }
}
