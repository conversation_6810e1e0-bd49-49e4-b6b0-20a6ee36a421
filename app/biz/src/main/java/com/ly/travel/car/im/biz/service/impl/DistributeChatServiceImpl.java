package com.ly.travel.car.im.biz.service.impl;

import cn.hutool.core.map.CaseInsensitiveMap;
import cn.hutool.core.util.NumberUtil;
import com.google.common.collect.Lists;
import com.ly.dal.util.DateUtil;
import com.ly.sof.api.mq.common.SerializeEnum;
import com.ly.sof.api.mq.producer.DefaultProducer;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.im.biz.config.AsyncConfig;
import com.ly.travel.car.im.biz.error.BizErrorFactory;
import com.ly.travel.car.im.biz.error.ValidateException;
import com.ly.travel.car.im.biz.handler.MsgHandler;
import com.ly.travel.car.im.biz.service.ChatService;
import com.ly.travel.car.im.biz.service.DistributeChatService;
import com.ly.travel.car.im.biz.service.OrderService;
import com.ly.travel.car.im.biz.service.SendService;
import com.ly.travel.car.im.dal.dao.SfcImInfoDao;
import com.ly.travel.car.im.dal.dao.SfcImMsgRecordDao;
import com.ly.travel.car.im.dal.entity.SfcImInfo;
import com.ly.travel.car.im.dal.entity.SfcImMsgRecord;
import com.ly.travel.car.im.facade.dto.*;
import com.ly.travel.car.im.facade.enums.MsgSenderEnum;
import com.ly.travel.car.im.facade.enums.MsgTypeEnum;
import com.ly.travel.car.im.facade.request.*;
import com.ly.travel.car.im.facade.response.*;
import com.ly.travel.car.im.integration.client.es.ImDispatchClient;
import com.ly.travel.car.im.integration.client.es.common.ESSearchResponse;
import com.ly.travel.car.im.integration.client.es.imdispatch.ImDispatchToElasticSearchVO;
import com.ly.travel.car.im.integration.client.redis.impl.RedisClientProxy;
import com.ly.travel.car.im.integration.utils.RedisKeyBuilder;
import com.ly.travel.car.im.model.config.ImConfigCenter;
import com.ly.travel.car.im.model.dto.*;
import com.ly.travel.car.im.model.enums.*;
import com.ly.travel.car.im.model.utils.PlatformUtils;
import com.ly.travel.shared.mobility.supply.crm.client.repository.SupplierInterfaceCapabilityRepository;
import com.ly.travel.shared.mobility.supply.crm.core.model.dto.SupplierInterfaceCapabilityDTO;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.driverOnlineStatus.DriverOnlineStatusRequest;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.driverOnlineStatus.DriverOnlineStatusResponse;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.uploadUserStatus.UploadUserStatusRequest;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.uploadUserStatus.UploadUserStatusResponse;
import com.ly.travel.shared.mobility.supply.order.core.facade.order.response.OrderRelationResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ly.travel.car.im.biz.utils.DecryptMemberInfoUtil.*;

@Service
@Slf4j
public class DistributeChatServiceImpl implements DistributeChatService {
    private static final BizErrorFactory BIZ_ERROR_FACTORY = BizErrorFactory.getInstance();
    @Resource
    private OrderService orderService;
    @Resource
    private SfcImMsgRecordDao sfcImMsgRecordDao;
    @Resource
    private ApplicationContext           applicationContext;


    @Resource(name = "producer")
    protected DefaultProducer defaultProducer;

    @Resource
    private AsyncConfig asyncConfig;

    private Map<String, MsgHandler>      msgHandlerMap;

    private static final String          ERROR_CODE        = HttpStatus.INTERNAL_SERVER_ERROR.value() + Strings.EMPTY;


    private static final Integer SESSION_KEY_LENGTH = 3;

    @Value("${chat.dispatch.mq.topic}")
    private String dispatchTopic;
    
    /**
     * 初始化消息处理器
     */
    @PostConstruct
    public void initMsgHandler() {
        Map<String, MsgHandler> beansMap = applicationContext.getBeansOfType(MsgHandler.class);
        msgHandlerMap = new CaseInsensitiveMap<>(beansMap);
    }

    @Override
    public SendMsgRspDTO sendMsg(DistributeSendMsgReqDTO req) {
        LoggerUtils.info(log, "发送消息，req：{}", FastJsonUtils.toJSONString(req));
        try {
            //投递消息MQ
            dispatchMsg(req);

            //重复消息校验
            duplicateInformationValid(req);

            //订单信息
            OrderDetailDTO orderDetail = orderService.getOrderDetail(req.getOrderId());
            if (Objects.isNull(orderDetail)) {
                throw new ValidateException(BIZ_ERROR_FACTORY.orderNotFoundError(req.getOrderId()));
            }

            //根据消息类型获取对应的处理器
            MsgHandler msgHandler = msgHandlerMap.get(MsgTypeEnum.getHandlerName(req.getMsgType()));
            if (Objects.isNull(msgHandler)) {
                throw new ValidateException(BIZ_ERROR_FACTORY.unknownMsgTypeError(req.getMsgType()));
            }

            //调用对应的处理器处理消息
            SendMsgReqDTO sendMsgReqDTO = buildRequest(req);
            return msgHandler.handle(sendMsgReqDTO, orderDetail);
        } catch (ValidateException validateException) {
            return SendMsgRspDTO.fail(req.getTraceId(), validateException.getErrorCode(), validateException.getError().getMessage(), req.getMsgContent().getMsgId());
        } catch (Exception e) {
            LoggerUtils.warn(log, "乘客消息发送司机异常 req：{}", FastJsonUtils.toJSONString(req), e);
            return SendMsgRspDTO.fail(req.getTraceId(), ERROR_CODE, "消息发送失败", req.getMsgContent().getMsgId());
        }
    }

    private static SendMsgReqDTO buildRequest(DistributeSendMsgReqDTO req) {
        SendMsgReqDTO sendMsgReqDTO = new SendMsgReqDTO();
        BeanUtils.copyProperties(req, sendMsgReqDTO);
        MsgContentDTO msgContentDTO = new MsgContentDTO();
        BeanUtils.copyProperties(req.getMsgContent(), msgContentDTO);
        sendMsgReqDTO.setMsgContent(msgContentDTO);
        return sendMsgReqDTO;
    }

    private void dispatchMsg(DistributeSendMsgReqDTO req) {
        // 发送消息到消息分发MQ
        if (Objects.equals(MsgTypeEnum.READ_ACK.getMsgType(), req.getMsgType())){
            return;
        }
        CompletableFuture.runAsync(() -> {
            try {
            ChatMessageDispatchDTO dispatchMsg = new ChatMessageDispatchDTO();
            dispatchMsg.setOrderId(req.getOrderId());
            dispatchMsg.setMsgId(req.getMsgId());
            dispatchMsg.setSupplierCode(req.getSupplierCode());
            dispatchMsg.setStatus(MsgDispatchStatusEnum.RECEIVE.getCode());
            dispatchMsg.setLatestTime(req.getMsgSendTime() != null ? DateUtil.date2String(req.getMsgSendTime()) : DateUtil.date2String(new Date()));
            ArrayList<ChatMessageDispatchDTO> maps = new ArrayList<>();
            maps.add(dispatchMsg);
            LoggerUtils.info(log, "发送消息分发MQ：{}", FastJsonUtils.toJSONString(maps));
            defaultProducer.send(dispatchTopic, ChatMsgTagEnum.im_chat_message_dispatch.getName(),
                    maps, Long.valueOf(ImConfigCenter.getConfigValue(ConfigCenterKeyEnum.im_dispatch_send_msg_timeout)), 0, new HashMap<>(), SerializeEnum.FASTJSON);
            } catch (Exception e) {
                LoggerUtils.error(log, "发送消息分发MQ异常: {}", req.getOrderId(), e);
                throw new CompletionException(e);
            }
        }, asyncConfig.mqSendExecutor());

    }

    private void duplicateInformationValid(DistributeSendMsgReqDTO req) throws ValidateException {
        SfcImMsgRecord sfcImMsgRecord = sfcImMsgRecordDao.queryRepeatMsg(req.getSessionKey(), req.getMsgId(), MsgSenderEnum.PASSENGER.getCode(),
                MsgSendStatusEnum.SUCCESS.getCode());
        if (Objects.nonNull(sfcImMsgRecord)) {
            throw new ValidateException(BIZ_ERROR_FACTORY.duplicateInformation(req.getMsgId()));
        }
    }


}
