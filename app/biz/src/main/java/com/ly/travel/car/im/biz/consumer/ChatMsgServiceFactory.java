package com.ly.travel.car.im.biz.consumer;

import com.ly.travel.car.im.biz.annotations.ChatMsgTag;
import com.ly.travel.car.im.model.enums.ChatMsgTagEnum;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
public class ChatMsgServiceFactory {
    @Resource
    private List<SmsListener> smsListeners;
    private final Map<ChatMsgTagEnum, SmsListener> compensationServiceMap = new EnumMap<>(ChatMsgTagEnum.class);

    @PostConstruct
    public void init() {
        for (SmsListener service : smsListeners) {
            ChatMsgTag annotation = service.getClass().getAnnotation(ChatMsgTag.class);
            if (Objects.nonNull(annotation)) {
                compensationServiceMap.put(annotation.value(), service);
            }
        }
    }

    public SmsListener getService(ChatMsgTagEnum code) {
        return compensationServiceMap.get(code);
    }
}
