package com.ly.travel.car.im.biz.service;

import com.ly.travel.shared.mobility.supply.order.core.facade.order.response.OrderRelationResponse;
import com.ly.travel.shared.mobility.supply.order.core.facade.order.response.QueryDispatchListResponse;
import com.ly.travel.shared.mobility.supply.order.core.facade.order.response.QueryDriverInfoResponse;
import com.ly.travel.car.im.model.dto.OrderDetailDTO;

public interface OrderService {

    /**
     * 查询订单详情
     *
     * @param orderId
     * @return
     */
    OrderDetailDTO getOrderDetail(String orderId);


    /**
     * 获取司机车辆信息
     *
     * @param tcOrderNo
     * @param thirdOrderNo
     * @return
     */
    QueryDriverInfoResponse queryDriverInfo(String tcOrderNo, String thirdOrderNo);


    /**
     * 供应商订单号查询同程订单号
     *
     * @param supplierCode
     * @param thirdOrderNo
     * @return
     */
    OrderRelationResponse orderRelationInfo(String supplierCode, String thirdOrderNo);

    /**
     *  查询供应链订单
     * @param tcOrderNo
     * @return
     */
    QueryDispatchListResponse queryDispatchList(String tcOrderNo);
}
