package com.ly.travel.car.im.biz.error;

import com.ly.sof.api.error.LYError;
import com.ly.sof.api.exception.LYException;

/**
 * IM领域异常
 * <AUTHOR>
 * @version Id: OrderBaseException, v 0.1 2024/2/23 15:43 icanci Exp $
 */
public class ImChatBaseException extends LYException {
    /** 序列化版本 */
    private static final long serialVersionUID = -2199134784883522913L;

    /** 全链路id */
    private String            traceId;

    /**
     * 构造器
     *
     * @param cause
     */
    public ImChatBaseException(Throwable cause) {
        super(cause);
    }

    /**
     * 构造器
     *
     * @param error 同程异常
     */
    public ImChatBaseException(LYError error) {
        super(error);
    }

    /**
     * 构造器
     *
     * @param error 同程异常
     * @param cause 错误
     */
    public ImChatBaseException(LYError error, Throwable cause) {
        super(error, cause);
    }

    /**
     * 构造器
     *
     * @param traceId 全链路id
     * @param cause   错误
     */
    public ImChatBaseException(String traceId, Throwable cause) {
        super(cause);
        this.traceId = traceId;
    }

    /**
     * Gets the value of traceId.
     *
     * @return the value of traceId
     */
    public String getTraceId() {
        return traceId;
    }

    /**
     * Sets the traceId. *
     * <p>You can use getTraceId() to get the value of traceId</p >
     * @param traceId traceId
     */
    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }
}
