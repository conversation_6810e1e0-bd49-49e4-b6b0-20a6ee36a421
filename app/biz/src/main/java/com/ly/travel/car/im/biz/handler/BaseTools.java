package com.ly.travel.car.im.biz.handler;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.dubbo.common.json.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.ly.car.utils.JsonUtils;
import com.ly.sof.utils.log.LogContextUtils;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.im.biz.utils.ImUtils;
import com.ly.travel.car.im.dal.dao.SfcImInfoDao;
import com.ly.travel.car.im.dal.entity.SfcImInfo;
import com.ly.travel.car.im.dal.entity.SfcImMsgRecord;
import com.ly.travel.car.im.facade.dto.MemberInfoDTO;
import com.ly.travel.car.im.facade.dto.ThirdPartyWechatBaseResponse;
import com.ly.travel.car.im.facade.enums.MsgSenderEnum;
import com.ly.travel.car.im.facade.enums.MsgTypeEnum;
import com.ly.travel.car.im.facade.request.SendMsgReqDTO;
import com.ly.travel.car.im.integration.client.redis.impl.RedisClientProxy;
import com.ly.travel.car.im.integration.utils.RedisKeyBuilder;
import com.ly.travel.car.im.model.config.ImConfigCenter;
import com.ly.travel.car.im.model.dto.OrderDetailDTO;
import com.ly.travel.car.im.model.dto.ThirdPartyConfigDTO;
import com.ly.travel.car.im.model.enums.MsgSendStatusEnum;
import com.ly.travel.car.im.model.enums.MsgStatusEnum;
import com.ly.travel.car.im.model.enums.OrderChannelEnum;
import com.ly.travel.car.im.model.utils.PlatformUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

import static com.ly.travel.car.im.biz.utils.DecryptMemberInfoUtil.getMemberIdListByMemberId;
import static com.ly.travel.car.im.biz.utils.DecryptMemberInfoUtil.getMemberIdListByUnionId;

@Service
@Slf4j
public abstract class BaseTools {
    @Resource
    private RedisClientProxy redisClientProxy;
    @Resource
    private SfcImInfoDao sfcImInfoDao;
    public static final Integer MSG_VERSION = 2;

    public SfcImMsgRecord buildSysMsg(SendMsgReqDTO req, Integer msgType, String msgContent, boolean generateId, OrderDetailDTO orderDetail) {
        SfcImMsgRecord sfcImMsgRecord = new SfcImMsgRecord();
        BeanUtils.copyProperties(req, sfcImMsgRecord);
        sfcImMsgRecord.setSessionKey(req.getSessionKey());
        sfcImMsgRecord.setMsgId(StringUtils.isNotBlank(req.getMsgId()) ? req.getMsgId() : UUID.randomUUID().toString().replace("-", Strings.EMPTY));
        if (generateId) {
            sfcImMsgRecord.setMsgId(UUID.randomUUID().toString().replace("-", Strings.EMPTY));
        }
        sfcImMsgRecord.setMsgSender(MsgSenderEnum.SYS.getCode());
        sfcImMsgRecord.setMemberId(StringUtils.isEmpty(orderDetail.getMemberId()) ? 0 : Long.parseLong(orderDetail.getMemberId()));
        sfcImMsgRecord.setMsgType(msgType);
        sfcImMsgRecord.setSceneType(req.getSceneType());
        sfcImMsgRecord.setMsgStatus(MsgStatusEnum.READ.getCode());
        sfcImMsgRecord.setMsgContent(msgContent);
        sfcImMsgRecord.setMsgSendStatus(MsgSendStatusEnum.SUCCESS.getCode());
        sfcImMsgRecord.setMsgVersion(MSG_VERSION);
        sfcImMsgRecord.setOrderStatus(orderDetail.getStatus());
        return sfcImMsgRecord;
    }

    public SfcImInfo getSfcImInfo(String sessionKey,Integer platId) {
        SfcImInfo sfcImInfo = null;
        String key = (Objects.nonNull(platId) &&  OrderChannelEnum.isMaDa(platId)) ? ":MaDa" : "";
        String imInfoStr = redisClientProxy.getString(RedisKeyBuilder.IM_INFO_KEY + sessionKey + key);

        if (StringUtils.isNotBlank(imInfoStr)) {
            LoggerUtils.info(log, "[getSfcImInfo] 发送消息，redis获取imInfo：{}", imInfoStr);
            sfcImInfo = FastJsonUtils.fromJSONString(imInfoStr, SfcImInfo.class);
        }

        if (Objects.isNull(sfcImInfo)) {
            sfcImInfo = sfcImInfoDao.findBySessionKey(sessionKey,platId);
            redisClientProxy.setnx(RedisKeyBuilder.IM_INFO_KEY + sessionKey + key, FastJsonUtils.toJSONString(sfcImInfo), 7200);
            LoggerUtils.info(log, "[getSfcImInfo] 发送消息，db获取imInfo：{}", FastJsonUtils.toJSONString(sfcImInfo));
        }
        return sfcImInfo;
    }

    public SfcImMsgRecord buildMsg(SendMsgReqDTO req, OrderDetailDTO orderDetail) {
        SfcImMsgRecord sfcImMsgRecord = new SfcImMsgRecord();
        sfcImMsgRecord.setSessionKey(req.getSessionKey());
        sfcImMsgRecord.setMsgId(req.getMsgId());
        sfcImMsgRecord.setMsgSender(MsgSenderEnum.PASSENGER.getCode());
        sfcImMsgRecord.setUnionId(orderDetail.getUnionId());
        sfcImMsgRecord.setMemberId(StringUtils.isEmpty(orderDetail.getMemberId()) ? 0 : Long.parseLong(orderDetail.getMemberId()));
        sfcImMsgRecord.setPlateNumber(req.getPlateNumber());
        sfcImMsgRecord.setSupplierCode(req.getSupplierCode());
        sfcImMsgRecord.setSubSupplierCode(req.getSupplierCode());
        sfcImMsgRecord.setDriverNickName(req.getDriverNickName());
        sfcImMsgRecord.setDriverId(req.getDriverId());
        sfcImMsgRecord.setOrderId(req.getOrderId());
        sfcImMsgRecord.setSupplierOrderId(req.getSupplierOrderId());
        sfcImMsgRecord.setMsgType(req.getMsgType());
        sfcImMsgRecord.setSceneType(req.getSceneType());
        sfcImMsgRecord.setMsgStatus(MsgStatusEnum.UNREAD.getCode());
        sfcImMsgRecord.setMsgVersion(MSG_VERSION);
        sfcImMsgRecord.setMsgContent(req.content());
        sfcImMsgRecord.setMsgSendStatus(MsgSendStatusEnum.FAIL.getCode());
        sfcImMsgRecord.setPlatform(req.getPlatform());
        return sfcImMsgRecord;
    }

    public void insertOrUpdateSfcImInfo(SendMsgReqDTO req, SfcImInfo sfcImInfo, SfcImMsgRecord sfcImMsgRecord,int plateId) {

        LogContextUtils.setCategory("insertOrUpdateSfcImInfo");

        if (Objects.equals(sfcImMsgRecord.getMsgSendStatus(), MsgSendStatusEnum.FAIL.getCode())) {
            return;
        }

        if (Objects.nonNull(sfcImInfo) && sfcImInfo.getId() != 0L) {
            SfcImInfo update = new SfcImInfo();
            update.setOrderId(sfcImMsgRecord.getOrderId());
            StringBuilder orderIdsBuilder = new StringBuilder();

            if (StringUtils.isNotEmpty(sfcImInfo.getAllOrderIds())) {
                if (!sfcImInfo.getAllOrderIds().contains(sfcImMsgRecord.getOrderId())) {
                    String[] allOrderIdsArray = sfcImInfo.getAllOrderIds().split(",");
                    if (allOrderIdsArray.length > ImConfigCenter.getHistoryOrderCount()) {
                        // 如果长度超过，移除第一个ID
                        sfcImInfo.setAllOrderIds(String.join(",", Arrays.copyOfRange(allOrderIdsArray, 1, allOrderIdsArray.length)));
                    }
                    // 重新拼接已有的订单ID
                    orderIdsBuilder.append(sfcImInfo.getAllOrderIds()).append(",").append(sfcImMsgRecord.getOrderId());
                } else {
                    // 如果订单ID已存在，直接使用当前的订单ID
                    orderIdsBuilder.append(sfcImInfo.getAllOrderIds());
                }
            } else {
                // 添加新的订单ID
                orderIdsBuilder.append(sfcImMsgRecord.getOrderId());
            }
            update.setAllOrderIds(orderIdsBuilder.toString());
            update.setSupplierOrderId(sfcImMsgRecord.getSupplierOrderId());
            update.setDriverNickName(req.getDriverNickName());
            update.setLatestMsgTime(Objects.isNull(sfcImMsgRecord.getMsgSendTime()) ? new Date() : sfcImMsgRecord.getMsgSendTime());
            update.setLatestMsgRecordId(sfcImMsgRecord.getId());
            ImUtils.setLatestMsgDesc(update, req.getMsgType(), req.getMsgContent().getContent(), sfcImMsgRecord.getMsgSender());
            update.setDeleted(0);
            LoggerUtils.info(log, "{}发送消息更新sfcImInfo：{}", req.getImKey(), FastJsonUtils.toJSONString(update));
            update.setUpdateTime(new Date());
            if (StringUtils.isNotEmpty(sfcImMsgRecord.getPlateNumber())) {
                update.setPlateNumber(sfcImMsgRecord.getPlateNumber());
            }else if (StringUtils.isNotEmpty(req.getPlateNumber())) {
                update.setPlateNumber(req.getPlateNumber());
            }
            processAlternateMemberId(req, update, ImConfigCenter.getThirdPartyConfig());
            sfcImInfoDao.update(update, new LambdaQueryWrapper<SfcImInfo>().eq(SfcImInfo::getId, sfcImInfo.getId()));
            boolean remove = redisClientProxy.remove(RedisKeyBuilder.IM_INFO_KEY + sfcImInfo.getSessionKey());
            LoggerUtils.info(log, "[deleteSfc][{}] deleteSfc redis：{}", RedisKeyBuilder.IM_INFO_KEY + sfcImInfo.getSessionKey(), String.valueOf(remove));

        } else {
            SfcImInfo msgInfo = buildMsgInfo(sfcImMsgRecord, req, plateId);
            LoggerUtils.info(log, "insert SfcImInfoMapper：{}", FastJsonUtils.toJSONString(msgInfo));
            msgInfo.setUpdateTime(new Date());
            sfcImInfoDao.save(msgInfo);
            redisClientProxy.setnx(RedisKeyBuilder.IM_INFO_KEY + msgInfo.getSessionKey(), FastJsonUtils.toJSONString(msgInfo), 7200);
        }


    }

    public SfcImMsgRecord distributeBuildMsg(SendMsgReqDTO req, OrderDetailDTO orderDetail) {
        SfcImMsgRecord sfcImMsgRecord = new SfcImMsgRecord();
        sfcImMsgRecord.setSessionKey(req.getSessionKey());
        sfcImMsgRecord.setMsgId(req.getMsgId());
        sfcImMsgRecord.setMsgSender(MsgSenderEnum.PASSENGER.getCode());
        sfcImMsgRecord.setUnionId(orderDetail.getUnionId());
        sfcImMsgRecord.setMemberId(StringUtils.isEmpty(orderDetail.getMemberId()) ? 0 : Long.parseLong(orderDetail.getMemberId()));
        if (StringUtils.isNotEmpty(req.getPlateNumber())){
            sfcImMsgRecord.setPlateNumber(req.getPlateNumber());
        }else {
            sfcImMsgRecord.setPlateNumber(orderDetail.getPlateNumber());
        }
        sfcImMsgRecord.setSupplierCode(req.getSupplierCode());
        sfcImMsgRecord.setSubSupplierCode(req.getSupplierCode());
        sfcImMsgRecord.setDriverNickName(req.getDriverNickName());
        sfcImMsgRecord.setDriverId(req.getDriverId());
        sfcImMsgRecord.setOrderId(req.getOrderId());
        sfcImMsgRecord.setSupplierOrderId(req.getSupplierOrderId());
        sfcImMsgRecord.setMsgType(MsgTypeEnum.CUSTOM_TEXT.getMsgType());
        sfcImMsgRecord.setSceneType(req.getSceneType());
        sfcImMsgRecord.setMsgStatus(MsgStatusEnum.UNREAD.getCode());
        sfcImMsgRecord.setMsgVersion(MSG_VERSION);
        sfcImMsgRecord.setMsgContent(JsonUtils.json(req.getMsgContent()));
        sfcImMsgRecord.setMsgSendStatus(MsgSendStatusEnum.FAIL.getCode());
        sfcImMsgRecord.setPlatform(req.getPlatform());
        return sfcImMsgRecord;
    }

    public SfcImInfo buildMsgInfo(SfcImMsgRecord sfcImMsgRecord, SendMsgReqDTO req,int plateId) {
        SfcImInfo sfcImInfo = new SfcImInfo();
        sfcImInfo.setLatestMsgTime(Objects.isNull(sfcImMsgRecord.getMsgSendTime()) ? new Date() : sfcImMsgRecord.getMsgSendTime());
        sfcImInfo.setSessionKey(req.getSessionKey());
        sfcImInfo.setUnionId(StringUtils.isEmpty(sfcImMsgRecord.getUnionId())? req.getUnionId() : sfcImMsgRecord.getUnionId());
        Long memberId = Optional.ofNullable(req.getMemberId())
                .filter(StringUtils::isNotBlank)
                .filter(NumberUtil::isLong)
                .map(id -> {
                    try {
                        return Long.parseLong(id);
                    } catch (NumberFormatException e) {
                        LoggerUtils.warn(log, "MemberId格式转换异常: {}", id, e);
                        return null;
                    }
                })
                .orElse(sfcImMsgRecord.getMemberId());
        sfcImInfo.setMemberId(memberId);
        sfcImInfo.setDriverId(sfcImMsgRecord.getDriverId());
        if (StringUtils.isNotEmpty(sfcImMsgRecord.getPlateNumber())) {
            sfcImInfo.setPlateNumber(sfcImMsgRecord.getPlateNumber());
        }else if (StringUtils.isNotEmpty(req.getPlateNumber())) {
            sfcImInfo.setPlateNumber(req.getPlateNumber());
        }
        sfcImInfo.setSupplierCode(sfcImMsgRecord.getSupplierCode());
        sfcImInfo.setSubSupplierCode(sfcImMsgRecord.getSupplierCode());
        if (StringUtils.isNotEmpty(sfcImMsgRecord.getDriverNickName())){
            sfcImInfo.setDriverNickName(sfcImMsgRecord.getDriverNickName());
        }
        sfcImInfo.setUnreadMsgNum(0);
        sfcImInfo.setLatestMsgRecordId(sfcImMsgRecord.getId());
        sfcImInfo.setLatestMsgType(sfcImMsgRecord.getMsgType());
        sfcImInfo.setOrderId(sfcImMsgRecord.getOrderId());
        sfcImInfo.setAllOrderIds(sfcImMsgRecord.getOrderId());
        sfcImInfo.setSupplierOrderId(sfcImMsgRecord.getSupplierOrderId());
        sfcImInfo.setPlateId(plateId);
        ImUtils.setLatestMsgDesc(sfcImInfo, req.getMsgContent().getMsgType(), req.getMsgContent().getContent(), sfcImMsgRecord.getMsgSender());
        processAlternateMemberId(req, sfcImInfo, ImConfigCenter.getThirdPartyConfig());
        return sfcImInfo;
    }

    /**
     * 处理替代会员ID逻辑
     */
    private void processAlternateMemberId(SendMsgReqDTO req, SfcImInfo update, ThirdPartyConfigDTO thirdPartyConfigDTO) {
        LoggerUtils.info(log, "processAlternateMemberId SfcImInfoMapper：{}", FastJsonUtils.toJSONString(update));
        // 处理微信平台
        if (StringUtils.isNotBlank(req.getUnionId()) && PlatformUtils.isWX(req.getPlatform())) {
            processWechatAlternateMemberId(req, update, thirdPartyConfigDTO);
        }
        
        // 处理APP平台
        if (StringUtils.isNotBlank(req.getMemberId()) && OrderChannelEnum.isTCAppWithoutMada(req.getPlatId())) {
            processAppAlternateMemberId(req, update, thirdPartyConfigDTO);
        }
    }

    /**
     * 处理微信平台的替代会员ID
     */
    private void processWechatAlternateMemberId(SendMsgReqDTO req, SfcImInfo update, ThirdPartyConfigDTO thirdPartyConfigDTO) {
        ThirdPartyWechatBaseResponse response = getMemberIdListByUnionId(req.getUnionId(), thirdPartyConfigDTO);
        if (Objects.isNull(response) || CollectionUtils.isEmpty(response.getData())) {
            return;
        }

        String wxMemberId = response.getData().stream()
                .filter(i -> Objects.equals(i.getMemberSystem(), 33))
                .map(MemberInfoDTO::getMemberId)
                .map(String::valueOf)
                .findFirst()
                .orElse(null);

        if (StringUtils.isNotEmpty(wxMemberId)) {
            update.setMemberId(Long.valueOf(wxMemberId));
        }

        String memberId = response.getData().stream()
                .filter(i -> Objects.equals(i.getMemberSystem(), 0))
                .map(MemberInfoDTO::getMemberId)
                .map(String::valueOf)
                .findFirst()
                .orElse(null);

        LoggerUtils.info(log, "processAlternateMemberId memberId：{}", memberId);
        if (StringUtils.isNotEmpty(memberId)) {
            updateAlternateMemberId(req, update, memberId, req.getUnionId());
        }
    }

    /**
     * 处理APP平台的替代会员ID
     */
    private void processAppAlternateMemberId(SendMsgReqDTO req, SfcImInfo update, ThirdPartyConfigDTO thirdPartyConfigDTO) {
        ThirdPartyWechatBaseResponse response = getMemberIdListByMemberId(req.getMemberId(), thirdPartyConfigDTO);
        if (Objects.isNull(response) || CollectionUtils.isEmpty(response.getData())) {
            return;
        }
        MemberInfoDTO appMemberInfoDTO = response.getData().stream()
                .filter(i -> Objects.equals(i.getMemberSystem(), 0))
                .findFirst()
                .orElse(null);
        if (Objects.nonNull(appMemberInfoDTO)){
            update.setMemberId(appMemberInfoDTO.getMemberId());
        }

        MemberInfoDTO memberInfoDTO = response.getData().stream()
                .filter(i -> Objects.equals(i.getMemberSystem(), 33))
                .findFirst()
                .orElse(null);
        LoggerUtils.info(log, "processAlternateMemberId memberInfoDTO：{}", FastJsonUtils.toJSONString(memberInfoDTO));

        if (Objects.nonNull(memberInfoDTO) && StringUtils.isNotEmpty(memberInfoDTO.getUnionId())) {
            updateAlternateMemberId(req, update, memberInfoDTO.getMemberId().toString(), req.getMemberId());
        }
    }

    /**
     * 更新替代会员ID
     */
    private void updateAlternateMemberId(SendMsgReqDTO req, SfcImInfo update, String memberId, String userKey) {
        String sessionKey = req.getSupplierCode() + "_" + userKey + "_" + req.getDriverId();
        SfcImInfo alternateImInfo = sfcImInfoDao.findBySessionKey(sessionKey, req.getPlatId());
        LoggerUtils.info(log, "processAlternateMemberId sessionKey：{}", sessionKey);

        if (Objects.nonNull(alternateImInfo)) {
            update.setAlternateMemberId(Long.parseLong(memberId));
            
            if (Objects.equals(alternateImInfo.getAlternateMemberId(), 0L)) {
                alternateImInfo.setAlternateMemberId(update.getMemberId());
                alternateImInfo.setLatestMsgDesc(update.getLatestMsgDesc());
                alternateImInfo.setUpdateTime(new Date());
                sfcImInfoDao.updateById(alternateImInfo);
            }
        }
    }
}
