package com.ly.travel.car.im.biz.service;

import com.ly.travel.car.im.facade.request.CloseChatActionReqDTO;
import com.ly.travel.car.im.facade.request.GetMsgReqDTO;
import com.ly.travel.car.im.facade.request.OpenChatActionReqDTO;
import com.ly.travel.car.im.facade.response.CloseChatActionRspDTO;
import com.ly.travel.car.im.facade.response.GetMsgRspDTO;
import com.ly.travel.car.im.facade.response.OpenChatActionRspDTO;

public interface SupplierChatService {

    /**
     * 获取司机消息
     */
    GetMsgRspDTO getMsg(GetMsgReqDTO req);

    /**
     * 关闭会话  -- 约约特性
     */
    CloseChatActionRspDTO closeChatAction(CloseChatActionReqDTO req);

    /**
     * 创建会话 -- 约约特性
     */
    OpenChatActionRspDTO openChatAction(OpenChatActionReqDTO req);
}
