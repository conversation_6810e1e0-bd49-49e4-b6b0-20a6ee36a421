package com.ly.travel.car.im.biz.service.impl;

import com.ly.sof.utils.log.LogContextUtils;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.travel.car.im.model.enums.OrderChannelEnum;
import com.ly.travel.shared.mobility.supply.order.core.facade.order.request.QueryDispatchListRequest;
import com.ly.travel.shared.mobility.supply.order.core.facade.order.response.QueryDispatchListResponse;
import org.springframework.stereotype.Service;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSONObject;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.common.model.enums.ServiceType;
import com.ly.travel.car.im.biz.service.OrderService;
import com.ly.travel.car.im.integration.client.order.OrderClient;
import com.ly.travel.car.orderservice.facade.model.opsi.*;
import com.ly.travel.car.orderservice.facade.request.order.OrderDetailRequest;
import com.ly.travel.car.orderservice.facade.response.order.OrderDetailResponse;
import com.ly.travel.car.tradecore.model.enums.BusinessType;
import com.ly.travel.car.tradecore.model.enums.OrderState;
import com.ly.travel.car.tradecore.model.ext.TicketExtVO;
import com.ly.travel.shared.mobility.supply.order.core.facade.order.request.OrderRelationRequest;
import com.ly.travel.shared.mobility.supply.order.core.facade.order.request.QueryDriverInfoRequest;
import com.ly.travel.shared.mobility.supply.order.core.facade.order.response.OrderRelationResponse;
import com.ly.travel.shared.mobility.supply.order.core.facade.order.response.QueryDriverInfoResponse;
import com.ly.travel.shared.mobility.supply.trade.core.model.order.common.CarInfoVO;
import com.ly.travel.shared.mobility.supply.trade.core.model.order.common.DriverInfoVO;
import com.ly.travel.car.im.model.dto.OrderDetailDTO;
import com.ly.travel.car.im.model.enums.CarpoolStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

@Slf4j
@Service
public class OrderServiceImpl implements OrderService {
    @Resource
    private OrderClient orderClient;

    @Override
    public OrderDetailDTO getOrderDetail(String orderId) {

        LogContextUtils.setCategory("getOrderDetail");
        LogContextUtils.setFilter1(orderId);

        OrderDetailResponse response = psiDetail(orderId);
        if (Objects.isNull(response) || Objects.isNull(response.getOrder())) {
            return null;
        }

        OrderDetailDTO orderDetailDTO = new OrderDetailDTO();
        orderDetailDTO.setOrderId(orderId);

        //基本信息
        assembleBaseInfo(response, orderDetailDTO);

        CarOrder carOrder = response.getOrder();
        List<OpsiRelation> psis = carOrder.getPsis();
        if (CollectionUtils.isEmpty(psis)) {
            return null;
        }

        OpsiRelation opsiRelation = psis.stream().filter(e -> Objects.equals(e.getItem().getBusinessType(), BusinessType.TICKET.getCode())).findFirst().orElse(null);
        if (Objects.isNull(opsiRelation)) {
            return null;
        }

        ItemDetail item = opsiRelation.getItem();
        orderDetailDTO.setBusinessOrderNo(Objects.nonNull(item) ? item.getBusinessOrderNo() : Strings.EMPTY);

        //位置信息
        assemblePositionInfo(opsiRelation, orderDetailDTO);

        //司机车辆信息
        assembleDriverInfo(opsiRelation, orderDetailDTO);

        orderDetailDTO.setCarpoolStatus(transCarpoolStatus(carOrder, opsiRelation));

        LoggerUtils.info(log, "订单详情组装后rsp：{}", FastJsonUtils.toJSONString(orderDetailDTO));

        return orderDetailDTO;
    }

    private OrderDetailResponse psiDetail(String orderId) {
        try {
            LogContextUtils.setCategory("psiDetail");
            LogContextUtils.setFilter1(orderId);
            OrderDetailRequest request = new OrderDetailRequest();
            request.setOrderSerialNo(orderId);
            request.setTraceId(UUID.randomUUID().toString());
            LoggerUtils.info(log, "获取订单详情req：{}", FastJsonUtils.toJSONString(request));
            OrderDetailResponse orderDetailResponse = orderClient.psiDetail(request);
            LoggerUtils.info(log, "获取订单详情rsp：{}", FastJsonUtils.toJSONString(orderDetailResponse));
            if (Objects.nonNull(orderDetailResponse) && orderDetailResponse.isSuccess()) {
                return orderDetailResponse;
            }
        } catch (Exception e) {
            LoggerUtils.error(log, "获取订单详情接口异常：{}", orderId, e);
        }
        return null;
    }

    private void assembleBaseInfo(OrderDetailResponse response, OrderDetailDTO orderDetailDTO) {
        CarOrder carOrder = response.getOrder();
        int orderChannel = carOrder.getOrderChannel();
        //小程序渠道特殊处理
        if (OrderChannelEnum.isXcx(orderChannel)) {
            orderDetailDTO.setUnionId(carOrder.getUnionId());
        }

        orderDetailDTO.setOrderTags(carOrder.getOrderTags());
        orderDetailDTO.setMemberId(carOrder.getMemberId());
        orderDetailDTO.setOpenId(carOrder.getOpenId());
        orderDetailDTO.setStatus(carOrder.getOrderState());
        orderDetailDTO.setPlatId(orderChannel);
        orderDetailDTO.setRefId(Long.valueOf(carOrder.getRefId()));
        orderDetailDTO.setPayStatus(carOrder.getPayState());
        orderDetailDTO.setPayCategory(carOrder.getPayCategory());
        orderDetailDTO.setProductId(carOrder.getOrderType());
        orderDetailDTO.setChannelType(carOrder.getOrderChannel());
        orderDetailDTO.setOrderType(carOrder.getOrderType());
        orderDetailDTO.setPassengerVirtualPhone(carOrder.getContactVirtualPhone());
        orderDetailDTO.setPassengerPhone(carOrder.getContactPhone());

        //yyyy-MM-dd HH:mm:ss
        LocalDateTime departureTimeMin = LocalDateTimeUtil.parse(carOrder.getGmtUsage(), DatePattern.NORM_DATETIME_PATTERN);
        LocalDateTime departureTimeMax = departureTimeMin.plusMinutes(carOrder.getUsageDelay());

        Date earliestTime = Date.from(departureTimeMin.atZone(ZoneId.systemDefault()).toInstant());
        Date latestTime = Date.from(departureTimeMax.atZone(ZoneId.systemDefault()).toInstant());
        orderDetailDTO.setEarliestTime(earliestTime);
        orderDetailDTO.setLatestTime(latestTime);

        List<PayInfo> payInfos = Optional.ofNullable(carOrder.getPayInfos()).orElse(Collections.emptyList());
        double payAmount = payInfos.stream()
                .mapToDouble(payInfo -> Double.parseDouble(payInfo.getAmount()))
                .sum();
        orderDetailDTO.setPayAmount((int) Math.floor(payAmount * 100));
    }

    private void assemblePositionInfo(OpsiRelation opsiRelation, OrderDetailDTO orderDetailDTO) {
        SegmentInfo segment = opsiRelation.getSegment();
        if (Objects.isNull(segment)) {
            return;
        }
        orderDetailDTO.setStartLat(new BigDecimal(segment.getDepLat()));
        orderDetailDTO.setStartLng(new BigDecimal(segment.getDepLon()));
        orderDetailDTO.setEndLat(new BigDecimal(segment.getArrLat()));
        orderDetailDTO.setEndLng(new BigDecimal(segment.getArrLon()));
        orderDetailDTO.setStartAddress(segment.getDepartureAddress());
        orderDetailDTO.setEndAddress(segment.getArrivalAddress());

        int startCityId = Integer.parseInt(segment.getDepartureCityCode());
        int endCityId = Integer.parseInt(segment.getArrivalCityCode());
        orderDetailDTO.setStartCityId(startCityId);
        orderDetailDTO.setEndCityId(endCityId);
        orderDetailDTO.setCrossCity(Objects.equals(startCityId, endCityId) ? 0 : 1);
    }

    private void assembleDriverInfo(OpsiRelation opsiRelation, OrderDetailDTO orderDetailDTO) {
        ItemDetail item = opsiRelation.getItem();
        if (Objects.isNull(item) || StringUtils.isEmpty(item.getExt())) {
            return;
        }

        TicketExtVO ticketExtVO = JSONObject.parseObject(item.getExt(), TicketExtVO.class);
        orderDetailDTO.setDriverName(ticketExtVO.getDriverName());
        orderDetailDTO.setDriverPhone(ticketExtVO.getDriverPhone());
        orderDetailDTO.setPlateNumber(ticketExtVO.getCarNum());
        orderDetailDTO.setSupplierCode(item.getSupplierCode());
        orderDetailDTO.setSupplierOrderId(item.getThirdOrderNo());
        orderDetailDTO.setEstimateKilo(BigDecimal.valueOf(ticketExtVO.getRunDistance() / 1000).setScale(2, RoundingMode.HALF_UP));
        orderDetailDTO.setCarColor(ticketExtVO.getColor());
        orderDetailDTO.setCarBrand(ticketExtVO.getBrand());

        if (orderDetailDTO.getStatus() > OrderState.CANCELED.getCode()) {
            QueryDriverInfoResponse queryDriverInfoResponse = queryDriverInfo(opsiRelation.getOrderSerialNo(), Strings.EMPTY);
            if (Objects.isNull(queryDriverInfoResponse)) {
                return;
            }

            DriverInfoVO driverInfo = queryDriverInfoResponse.getDriverInfo();
            if (Objects.nonNull(driverInfo)) {
                orderDetailDTO.setDriverChatFlag(driverInfo.getDriverChatFlag());
                orderDetailDTO.setDriverChatId(driverInfo.getDriverChatId());
                orderDetailDTO.setDriverId(driverInfo.getDriverId());
            }

            CarInfoVO carInfo = queryDriverInfoResponse.getCarInfo();
            if (Objects.nonNull(carInfo)) {
                orderDetailDTO.setCarColor(carInfo.getColor());
                orderDetailDTO.setCarBrand(carInfo.getBrand());
            }
        }
    }

    public QueryDriverInfoResponse queryDriverInfo(String tcOrderNo, String thirdOrderNo) {
        try {
            QueryDriverInfoRequest request = new QueryDriverInfoRequest();
            request.setOrderSerialNo(tcOrderNo);
            request.setSupplierOrderSerialNo(thirdOrderNo);
            request.setTraceId(UUID.randomUUID().toString());
            LoggerUtils.info(log, "[queryDriverInfo][{}] 获取司机车辆信息req：{}", tcOrderNo, FastJsonUtils.toJSONString(request));
            QueryDriverInfoResponse queryDriverInfoResponse = orderClient.queryDriverInfo(request);
            LoggerUtils.info(log, "[queryDriverInfo] 获取司机车辆信息rsp：{}", FastJsonUtils.toJSONString(queryDriverInfoResponse));
            if (Objects.nonNull(queryDriverInfoResponse) && queryDriverInfoResponse.isSuccess()) {
                return queryDriverInfoResponse;
            }
        } catch (Exception e) {
            LoggerUtils.error(log, "[queryDriverInfo][{}] 获取司机车辆信息异常：{}", tcOrderNo, thirdOrderNo, e);
        }
        return null;
    }

    private Integer transCarpoolStatus(CarOrder carOrder, OpsiRelation opsiRelation) {
        int orderState = carOrder.getOrderState();

        if (OrderState.DISPATCHING.getCode() >= orderState) {  //下单前
            ItemDetail item = opsiRelation.getItem();
            Integer serviceType = FastJsonUtils.fromJSONString(item.getExt(), TicketExtVO.class).getServiceType();
            return (Objects.equals(ServiceType.CAR_POOL.getCode(), serviceType) || Objects.equals(ServiceType.WILLING_CAR_POOL.getCode(), serviceType))
                    ? CarpoolStatusEnum.CARPOOLING.getCode() : CarpoolStatusEnum.NOT_CARPOOL.getCode();
        }

        if (OrderState.RECEIVING_ORDER.getCode() <= orderState) {  //接单后
            List<String> orderTags = Optional.ofNullable(carOrder.getOrderTags()).orElse(Collections.emptyList());
            return orderTags.contains("CARPOOL_ORDER") ? CarpoolStatusEnum.CARPOOL_SUCCESS.getCode() : CarpoolStatusEnum.CARPOOL_UNSUCCESS.getCode();
        }

        return CarpoolStatusEnum.NOT_CARPOOL.getCode();
    }

    public OrderRelationResponse orderRelationInfo(String supplierCode, String thirdOrderNo) {
        try {
            LogContextUtils.setCategory("orderRelationInfo");
            LogContextUtils.setFilter1(thirdOrderNo);
            OrderRelationRequest request = new OrderRelationRequest();
            request.setThirdOrderSerialNo(thirdOrderNo);
            request.setIncludePhone(Boolean.TRUE);
            request.setTraceId(UUID.randomUUID().toString());
            LoggerUtils.info(log, "供应商订单号查询同程订单号req：{}", FastJsonUtils.toJSONString(request));
            OrderRelationResponse orderRelationResponse = orderClient.orderRelationInfo(request);
            LoggerUtils.info(log, "供应商订单号查询同程订单号rsp：{}", FastJsonUtils.toJSONString(orderRelationResponse));
            if (Objects.nonNull(orderRelationResponse) && orderRelationResponse.isSuccess()) {
                return orderRelationResponse;
            }
        } catch (Exception e) {
            LoggerUtils.error(log, "供应商订单号查询同程订单号异常：{}", supplierCode, e);
        }
        return null;
    }

    @Override
    public QueryDispatchListResponse queryDispatchList(String tcOrderNo) {
        try {
            LogContextUtils.setCategory("queryDispatchList");
            LogContextUtils.setFilter1(tcOrderNo);
            QueryDispatchListRequest request = new QueryDispatchListRequest();
            request.setOrderSerialNo(tcOrderNo);
            request.setTraceId(UUID.randomUUID().toString());
            LoggerUtils.info(log, "查询供应商订单号req：{}", FastJsonUtils.toJSONString(request));
            QueryDispatchListResponse dispatchListResponse = orderClient.queryDispatchList(request);
            LoggerUtils.info(log, "查询供应商订单号rsp：{}", FastJsonUtils.toJSONString(dispatchListResponse));
            if (Objects.nonNull(dispatchListResponse) && dispatchListResponse.isSuccess()) {
                return dispatchListResponse;
            }
        } catch (Exception e) {
            LoggerUtils.error(log, "查询供应商订单号异常：{}", tcOrderNo, e);
        }
        return null;
    }
}
