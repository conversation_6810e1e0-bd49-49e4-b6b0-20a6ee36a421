package com.ly.travel.car.im.biz.utils;

import cn.hutool.core.util.NumberUtil;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.travel.car.im.facade.dto.MemberInfoDTO;
import com.ly.travel.car.im.facade.dto.ThirdPartyWechatBaseResponse;
import com.ly.travel.car.im.model.config.ImConfigCenter;
import com.ly.travel.car.im.model.dto.ThirdPartyConfigDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Objects;

@Slf4j
public class SessionKeyUtil {

    private static final Integer SESSION_KEY_LENGTH = 3;
    public static String getNewSessionKey(String oldSessionKey) {
        String otherSessionKey;
        String userId = "";
        String oldUserId = splitOldSessionKey4UserId(oldSessionKey);
        if(NumberUtil.isLong(oldUserId)){
            userId = getUnionIdByMemberId(oldUserId);
        }else if(StringUtils.isNotBlank(oldUserId)){
            Long memberIdByUnionId = getMemberIdByUnionId(oldUserId);
            userId = Objects.nonNull(memberIdByUnionId)? memberIdByUnionId.toString():null;
        }
        otherSessionKey = replaceSessionKey(oldSessionKey, userId);
        return otherSessionKey;
    }

    private static String splitOldSessionKey4UserId(String oldSessionKey) {
        String[] splits = oldSessionKey.split("_");
        if(splits.length == SESSION_KEY_LENGTH){
            return   splits[1];
        }else if(splits.length > SESSION_KEY_LENGTH){
            // XXXX_XXXX_XXXX_XXXX_XXX  中间的都认为是用户ID
            return String.join("_", Arrays.copyOfRange(splits, 1, splits.length - 1));
        }
        return null;
    }

    private static String getUnionIdByMemberId(String memberId) {

        ThirdPartyConfigDTO thirdPartyConfigDTO = ImConfigCenter.getThirdPartyConfig();
        ThirdPartyWechatBaseResponse response = null;
        if (StringUtils.isNotBlank(memberId)) {
            //如果是APP端，memberId是数字
            response = DecryptMemberInfoUtil.getMemberIdListByMemberId(memberId, thirdPartyConfigDTO);
        }
        if(Objects.nonNull(response)&& CollectionUtils.isNotEmpty(response.getData())){
            return response.getData().stream().filter(i -> Objects.equals(i.getMemberSystem(), 33))
                    .map(MemberInfoDTO::getUnionId).findFirst().orElse(null);
        }
        return null;
    }


    private static Long getMemberIdByUnionId(String unionId) {
        ThirdPartyConfigDTO thirdPartyConfigDTO = ImConfigCenter.getThirdPartyConfig();
        ThirdPartyWechatBaseResponse response = null;
        if (StringUtils.isNotBlank(unionId)) {
            response = DecryptMemberInfoUtil.getMemberIdListByUnionId(unionId, thirdPartyConfigDTO);
        }
        if(Objects.nonNull(response)&& CollectionUtils.isNotEmpty(response.getData())){
            return response.getData().stream().filter(i -> Objects.equals(i.getMemberSystem(), 0))
                    .map(MemberInfoDTO::getMemberId).findFirst().orElse(null);
        }
        return null;
    }

    private static String replaceSessionKey(String sessionKey, String userId) {
        if(StringUtils.isBlank(sessionKey)|| StringUtils.isBlank(userId)){
            return null;
        }
        // 会话标识（供应商Code/子供应商Code + 会员ID/unionId + 司机ID）  例子CaoCao_1234567890_1234567890
        // 将中间的换成userId
        String[] sessionKeyParts = sessionKey.split("_");
        if (sessionKeyParts.length < SESSION_KEY_LENGTH) {
            LoggerUtils.warn(log, "会话标识格式不正确，无法替换 sessionKey: {}", sessionKey);
            return null;
        }
        if(sessionKeyParts.length == SESSION_KEY_LENGTH){
            return sessionKeyParts[0] + "_" + userId + "_" + sessionKeyParts[2];
        }

        LoggerUtils.warn(log, "会话标识分隔后超过3位  sessionKey: {}", sessionKey);
        return sessionKeyParts[0] + "_" + userId + "_" + sessionKeyParts[SESSION_KEY_LENGTH - 1];

    }

}
