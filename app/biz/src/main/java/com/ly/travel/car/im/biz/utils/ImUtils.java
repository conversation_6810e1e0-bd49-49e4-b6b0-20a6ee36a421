package com.ly.travel.car.im.biz.utils;

import com.ly.travel.car.im.dal.entity.SfcImInfo;
import com.ly.travel.car.im.facade.enums.MsgSenderEnum;
import com.ly.travel.car.im.model.dto.OrderDetailDTO;
import com.ly.travel.car.im.model.enums.OrderChannelEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;

public class ImUtils {
    private static final String SEPARATOR = "_";
    private static final String SUPPLIER = "MadaSaas";

    public static String buildSessionKey(String supplierCode, String imKey, String driverId) {
        if (StringUtils.startsWithIgnoreCase(supplierCode, SUPPLIER)) {
            return SUPPLIER + SEPARATOR + imKey + SEPARATOR + driverId;
        }
        return supplierCode + SEPARATOR + imKey + SEPARATOR + driverId;
    }

    public static void setLatestMsgDesc(SfcImInfo sfcImInfo, Integer msgType, String content, Integer msgSender) {
        Integer passengerCode = MsgSenderEnum.PASSENGER.getCode();
        switch (msgType) {
            case 0:
            case 1:
            case 9:
            case 10:
            case 13:
            case 14:
                sfcImInfo.setLatestMsgDesc(content);
                break;
            case 2:
                sfcImInfo.setLatestMsgDesc(passengerCode.equals(msgSender) ? "【您发送一个定位】" : "【您收到一个定位】");
                break;
            case 3:
                sfcImInfo.setLatestMsgDesc(passengerCode.equals(msgSender) ? "【您发送一条语音】" : "【您收到一条语音】");
                break;
            case 4:
                sfcImInfo.setLatestMsgDesc(passengerCode.equals(msgSender) ? "【您发送一张图片】" : "【您收到一张图片】");
                break;
            case 11:
                sfcImInfo.setLatestMsgDesc(Strings.EMPTY);
                break;
        }
    }

    public static String getSessionKey(OrderDetailDTO orderDetail, String supplierCode, String driverId) {
        String imKey = "";
        if (OrderChannelEnum.isXcx(orderDetail.getChannelType())) {
            imKey = orderDetail.getUnionId();
        } else {
            imKey = orderDetail.getMemberId();
        }

        return buildSessionKey(supplierCode, imKey, driverId);
    }
}
