package com.ly.travel.car.im.biz.service.impl;

import cn.hutool.core.map.CaseInsensitiveMap;
import cn.hutool.core.util.NumberUtil;
import com.google.common.collect.Lists;
import com.ly.dal.util.DateUtil;
import com.ly.sof.api.mq.common.SerializeEnum;
import com.ly.sof.api.mq.producer.DefaultProducer;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.im.biz.config.AsyncConfig;
import com.ly.travel.car.im.biz.error.BizErrorFactory;
import com.ly.travel.car.im.biz.error.ValidateException;
import com.ly.travel.car.im.biz.handler.MsgHandler;
import com.ly.travel.car.im.biz.service.ChatService;
import com.ly.travel.car.im.biz.service.OrderService;
import com.ly.travel.car.im.biz.service.SendService;
import com.ly.travel.car.im.biz.utils.ImUtils;
import com.ly.travel.car.im.dal.dao.SfcImInfoDao;
import com.ly.travel.car.im.dal.dao.SfcImMsgRecordDao;
import com.ly.travel.car.im.dal.entity.SfcImInfo;
import com.ly.travel.car.im.dal.entity.SfcImMsgRecord;
import com.ly.travel.car.im.facade.dto.*;
import com.ly.travel.car.im.facade.enums.MsgSenderEnum;
import com.ly.travel.car.im.facade.enums.MsgTypeEnum;
import com.ly.travel.car.im.facade.request.*;
import com.ly.travel.car.im.facade.response.*;
import com.ly.travel.car.im.integration.client.es.ImDispatchClient;
import com.ly.travel.car.im.integration.client.es.common.ESSearchResponse;
import com.ly.travel.car.im.integration.client.es.imdispatch.ImDispatchToElasticSearchVO;
import com.ly.travel.car.im.integration.client.redis.impl.RedisClientProxy;
import com.ly.travel.car.im.integration.utils.RedisKeyBuilder;
import com.ly.travel.car.im.integration.client.order.OrderClient;
import com.ly.travel.car.im.model.config.ImConfigCenter;
import com.ly.travel.car.im.model.dto.*;
import com.ly.travel.car.im.model.enums.*;
import com.ly.travel.car.im.model.utils.PlatformUtils;
import com.ly.travel.shared.mobility.supply.crm.client.repository.SupplierInterfaceCapabilityRepository;
import com.ly.travel.shared.mobility.supply.crm.core.model.dto.SupplierInterfaceCapabilityDTO;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.driverOnlineStatus.DriverOnlineStatusRequest;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.driverOnlineStatus.DriverOnlineStatusResponse;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.uploadUserStatus.UploadUserStatusRequest;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.uploadUserStatus.UploadUserStatusResponse;
import com.ly.travel.shared.mobility.supply.order.core.facade.OrderFacade;
import com.ly.travel.shared.mobility.supply.order.core.facade.order.request.QueryDispatchListRequest;
import com.ly.travel.shared.mobility.supply.order.core.facade.order.response.OrderRelationResponse;
import com.ly.travel.shared.mobility.supply.order.core.facade.order.response.QueryDispatchListResponse;
import com.ly.travel.shared.mobility.supply.trade.core.model.dispatch.DispatchVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ly.travel.car.im.biz.utils.DecryptMemberInfoUtil.*;

@Service
@Slf4j
public class ChatServiceImpl implements ChatService {
    private static final BizErrorFactory BIZ_ERROR_FACTORY = BizErrorFactory.getInstance();
    @Resource
    private OrderService orderService;
    @Resource
    private SfcImInfoDao sfcImInfoDao;
    @Resource
    private SendService sendService;
    @Resource
    private SfcImMsgRecordDao sfcImMsgRecordDao;

    @Resource
    private OrderFacade orderFacade;
    @Resource
    private ApplicationContext           applicationContext;

    @Resource
    private SupplierInterfaceCapabilityRepository supplierInterfaceCapabilityRepository;

    @Resource
    private ImDispatchClient imDispatchClient;

    @Resource(name = "producer")
    protected DefaultProducer defaultProducer;

    @Resource
    private AsyncConfig asyncConfig;

    private Map<String, MsgHandler>      msgHandlerMap;

    @Resource
    private RedisClientProxy redisClientProxy;
    private static final String          ERROR_CODE        = HttpStatus.INTERNAL_SERVER_ERROR.value() + Strings.EMPTY;

    private static final Integer TIME_OUT = 60000;

    private static final Integer SESSION_KEY_LENGTH = 3;

    @Value("${chat.dispatch.mq.topic}")
    private String dispatchTopic;


    private static final Integer CONVERSATION_HALL = 1;
    private static final Integer TencentTravelApplet = 1343;

    /**
     * 初始化消息处理器
     */
    @PostConstruct
    public void initMsgHandler() {
        Map<String, MsgHandler> beansMap = applicationContext.getBeansOfType(MsgHandler.class);
        msgHandlerMap = new CaseInsensitiveMap<>(beansMap);
    }

    @Override
    public SendMsgRspDTO sendMsg(SendMsgReqDTO req) {
        LoggerUtils.info(log, "发送消息，req：{}", FastJsonUtils.toJSONString(req));
        try {
            //投递消息MQ
            dispatchMsg(req);

            //重复消息校验
            duplicateInformationValid(req);

            //订单信息
            OrderDetailDTO orderDetail = orderService.getOrderDetail(req.getOrderId());
            if (Objects.isNull(orderDetail)) {
                throw new ValidateException(BIZ_ERROR_FACTORY.orderNotFoundError(req.getOrderId()));
            }

            //根据消息类型获取对应的处理器
            MsgHandler msgHandler = msgHandlerMap.get(MsgTypeEnum.getHandlerName(req.getMsgType()));
            if (Objects.isNull(msgHandler)) {
                throw new ValidateException(BIZ_ERROR_FACTORY.unknownMsgTypeError(req.getMsgType()));
            }

            //调用对应的处理器处理消息
            return msgHandler.handle(req, orderDetail);

        } catch (ValidateException validateException) {
            return SendMsgRspDTO.fail(req.getTraceId(), validateException.getErrorCode(), validateException.getError().getMessage(), req.getMsgContent().getMsgId());
        } catch (Exception e) {
            LoggerUtils.warn(log, "乘客消息发送司机异常 req：{}", FastJsonUtils.toJSONString(req), e);
            return SendMsgRspDTO.fail(req.getTraceId(), ERROR_CODE, "消息发送失败", req.getMsgContent().getMsgId());
        }
    }

    private void dispatchMsg(SendMsgReqDTO req) {
        // 发送消息到消息分发MQ
        if (Objects.equals(MsgTypeEnum.READ_ACK.getMsgType(), req.getMsgType())){
            return;
        }
        CompletableFuture.runAsync(() -> {
            try {
            ChatMessageDispatchDTO dispatchMsg = new ChatMessageDispatchDTO();
            dispatchMsg.setOrderId(req.getOrderId());
            dispatchMsg.setMsgId(req.getMsgId());
            dispatchMsg.setSupplierCode(req.getSupplierCode());
            dispatchMsg.setStatus(MsgDispatchStatusEnum.RECEIVE.getCode());
            dispatchMsg.setLatestTime(req.getMsgSendTime() != null ? DateUtil.date2String(req.getMsgSendTime()) : DateUtil.date2String(new Date()));
            ArrayList<ChatMessageDispatchDTO> maps = new ArrayList<>();
            maps.add(dispatchMsg);
            LoggerUtils.info(log, "发送消息分发MQ：{}", FastJsonUtils.toJSONString(maps));
            defaultProducer.send(dispatchTopic, ChatMsgTagEnum.im_chat_message_dispatch.getName(),
                    maps, Long.valueOf(ImConfigCenter.getConfigValue(ConfigCenterKeyEnum.im_dispatch_send_msg_timeout)), 0, new HashMap<>(), SerializeEnum.FASTJSON);
            } catch (Exception e) {
                LoggerUtils.error(log, "发送消息分发MQ异常: {}", req.getOrderId(), e);
                throw new CompletionException(e);
            }
        }, asyncConfig.mqSendExecutor());

    }

    private void duplicateInformationValid(SendMsgReqDTO req) throws ValidateException {
        SfcImMsgRecord sfcImMsgRecord = sfcImMsgRecordDao.queryRepeatMsg(req.getSessionKey(), req.getMsgId(), MsgSenderEnum.PASSENGER.getCode(),
                MsgSendStatusEnum.SUCCESS.getCode());
        if (Objects.nonNull(sfcImMsgRecord)) {
            throw new ValidateException(BIZ_ERROR_FACTORY.duplicateInformation(req.getMsgId()));
        }
    }

    @Override
    public QueryMsgRspDTO queryMsgList(QueryMsgReqDTO req) {
        LoggerUtils.info(log, "查询消息列表，req：{}", FastJsonUtils.toJSONString(req));

        List<MsgDTO> rspList = new ArrayList<>();
        long total = 0;

        String unionId = req.getUnionId();
        String memberId = req.getMemberId();
        String platform = req.getPlatform();
        String orderSerialNo = req.getOrderSerialNo();
        if (StringUtils.isAllEmpty(unionId, req.getSessionKey(), memberId)) {
            LoggerUtils.warn(log, "查询消息列表，用户信息为空");
            return QueryMsgRspDTO.success(req.getTraceId(), new Pagination<>(rspList, total, new Pagination.Pageable()));
        }

        //防止查询数据过多，目前写死查询6个月内的消息列表
        String createTime = DateUtil.date2String(DateUtil.addMonth(new Date(), -6));
        QueryMsgDTO queryMsgDTO = new QueryMsgDTO();
        queryMsgDTO.setPage(req.getPage());
        queryMsgDTO.setSize(req.getSize());
        //给了订单号，。不需要限制时间，一个订单没几个聊天记录
        if (StringUtils.isEmpty(orderSerialNo)) {
            queryMsgDTO.setLatestMsgTime(createTime);
        } else {
            queryMsgDTO.setOrderSerialNo(orderSerialNo);
        }


        List<SfcImInfo> sfcImInfoList = new ArrayList<>();
        DecryptMemberConfigDTO decryptMemberConfig = ImConfigCenter.getDecryptMemberConfig();

        if (Objects.nonNull(req.getPlatId()) && req.getPlatId() != 0) {
            if (decryptMemberConfig.getPlateIds().contains(req.getPlatId())) {
                //支付宝、马达会员解密
                BaseResponse checkMemberTokenDto = getMemberInfo(memberId, decryptMemberConfig);
                memberId = Objects.equals(checkMemberTokenDto.getCode(), "0") ? checkMemberTokenDto.getData().getMemberId() : memberId;
                platform = "APP";
            } else if (OrderChannelEnum.RIDE.getCode() == req.getPlatId()) {
                //乘车呗
                String memberIdDecrypt = PlatformUtils.memberIdDecrypt(memberId);
                memberId = Objects.nonNull(memberIdDecrypt) ? memberIdDecrypt : memberId;
                platform = "APP";
            } else if (Objects.equals(platform, "APP")) {
                //app兜底解密
                String handlerMemberId = PlatformUtils.handlerMemberId(memberId, platform);
                memberId = StringUtils.isNotEmpty(handlerMemberId) ? handlerMemberId : memberId;
            }
        } else {
            //老版本 APP & WX 逻辑
            if (Objects.equals(platform, "APP")) {
                //app解密
                String handlerMemberId = PlatformUtils.handlerMemberId(memberId, platform);
                memberId = StringUtils.isNotEmpty(handlerMemberId) ? handlerMemberId : memberId;
            }
        }

        LoggerUtils.info(log, "解密后unionId: {},memberId: {},platform: {}", unionId, memberId, platform);
        if ((StringUtils.isNotBlank(memberId) && Objects.equals(platform, "APP")) ||
                StringUtils.isNotBlank(unionId) && Objects.equals(platform, "WX")) {
            if (Objects.equals(platform, "APP")) {
                queryMsgDTO.setMemberId(Long.valueOf(memberId));
                queryMsgDTO.setUnionId(null);
                unionId = "";
            } else {
                queryMsgDTO.setMemberId(null);
                queryMsgDTO.setUnionId(unionId);
            }
            List<MemberInfoDTO>  memberInfoList = getMemberIds4AppAndWx(memberId, unionId);

            queryMsgDTO.setMemberIds(memberInfoList.stream().map(MemberInfoDTO::getMemberId).collect(Collectors.toList()));

            if (Objects.nonNull(req.getPlatId())) {
                //马达隔离会话
                SessionRuleConfigDTO sessionKeyRule = ImConfigCenter.getSessionKeyRule(req.getPlatId());
                if (sessionKeyRule.getIsIsolate() == 1) {
                    queryMsgDTO.setPlateId(req.getPlatId());
                }
            }
            sfcImInfoList = sfcImInfoDao.findByUserInfoPage(queryMsgDTO);
            //相同会话聚合
            sfcImInfoList = new ArrayList<>(sfcImInfoList.stream().collect(Collectors.toMap(i -> i.getSupplierCode() + "_" + i.getDriverId(), Function.identity(),
                    (existing, replacement) -> {
                        // 保留latestMsgTime 最近的
                        if (existing.getUpdateTime().compareTo(replacement.getUpdateTime()) < 0) {
                            return replacement;
                        }
                        return existing;
                    })).values());
            sfcImInfoList.sort(Comparator.comparing(SfcImInfo::getLatestMsgTime).reversed());
            if (OrderChannelEnum.isXcx(req.getPlatId()) || OrderChannelEnum.isTCAppWithoutMada(req.getPlatId())) {
                // 微信和ANDORID和IOS  sessionKey需要强制统一
                for (SfcImInfo sfcImInfo : sfcImInfoList) {
                    if (StringUtils.isNotEmpty(req.getSessionKey())) {
                        sfcImInfo.setSessionKey(req.getSessionKey());
                    } else {
                        if (OrderChannelEnum.isXcx(req.getPlatId())) {
                            sfcImInfo.setSessionKey(sfcImInfo.getSupplierCode() + "_" + unionId + "_" + sfcImInfo.getDriverId());
                        } else {
                            sfcImInfo.setSessionKey(sfcImInfo.getSupplierCode() + "_" + memberId + "_" + sfcImInfo.getDriverId());
                        }
                    }
                }
            }

            List<SfcImInfo> totalList = sfcImInfoDao.findUnreadMsgInfo(queryMsgDTO);
            total = totalList.stream().collect(Collectors.toMap(i -> i.getSupplierCode() + "_" + i.getDriverId(), Function.identity(),
                    (existing, replacement) -> {
                        // 保留latestMsgTime 最近的
                        if (existing.getUpdateTime().compareTo(replacement.getUpdateTime()) < 0) {
                            return replacement;
                        }
                        return existing;
                    })).size();
        }



        LoggerUtils.info(log, "用户：{}  聊天列表信息：{}", req.filter1(), FastJsonUtils.toJSONString(sfcImInfoList));
        rspList = sfcImInfoList.stream().map(sfcImInfo -> {
            MsgDTO dto = new MsgDTO();
            BeanUtils.copyProperties(sfcImInfo, dto);
            return dto;
        }).collect(Collectors.toList());

        if (StringUtils.isNotEmpty(orderSerialNo)) {
            //带订单号查询的，多返回一个 消息总数
            rspList.forEach(i -> {
                try {
                    i.setTotalMsgNum(sfcImMsgRecordDao.selectCount(i.getSessionKey(), null, i.getOrderId(), null));
                } catch (Exception e) {
                    LoggerUtils.error(log, "查询会话总条数异常：{}", e.getMessage(), e);
                    i.setTotalMsgNum(0L);
                }
            });
        }

        LoggerUtils.info(log, "查询消息列表，rsp：{}  total：{}", FastJsonUtils.toJSONString(rspList), total);

        return QueryMsgRspDTO.success(req.getTraceId(), new Pagination<>(rspList, total, new Pagination.Pageable()));
    }

    private List<MemberInfoDTO> getMemberIds4AppAndWx(String memberId, String unionId) {
        List<MemberInfoDTO> memberIds = new ArrayList<>();
        ThirdPartyConfigDTO thirdPartyConfigDTO = ImConfigCenter.getThirdPartyConfig();
        ThirdPartyWechatBaseResponse response = null;
        if (StringUtils.isNotBlank(unionId)) {
            //如果是微信端，unionId是字符串
            response = getMemberIdListByUnionId(unionId, thirdPartyConfigDTO);
        } else if (StringUtils.isNotBlank(memberId)) {
            //如果是APP端，memberId是数字
            response = getMemberIdListByMemberId(memberId, thirdPartyConfigDTO);
        }
        if(Objects.nonNull(response)&& CollectionUtils.isNotEmpty(response.getData())){
            memberIds = response.getData().stream().filter(i -> Objects.equals(i.getMemberSystem(), 0)
                            || Objects.equals(i.getMemberSystem(), 33))
                    .collect(Collectors.toList());
        }
        return memberIds;
    }

    private String getUnionIdByMemberId(String memberId) {

        ThirdPartyConfigDTO thirdPartyConfigDTO = ImConfigCenter.getThirdPartyConfig();
        ThirdPartyWechatBaseResponse response = null;
        if (StringUtils.isNotBlank(memberId)) {
            //如果是APP端，memberId是数字
            response = getMemberIdListByMemberId(memberId, thirdPartyConfigDTO);
        }
        if(Objects.nonNull(response)&& CollectionUtils.isNotEmpty(response.getData())){
            return response.getData().stream().filter(i -> Objects.equals(i.getMemberSystem(), 33))
                    .map(MemberInfoDTO::getUnionId).findFirst().orElse(null);
        }
        return null;
    }


    private Long getMemberIdByUnionId(String unionId) {
        ThirdPartyConfigDTO thirdPartyConfigDTO = ImConfigCenter.getThirdPartyConfig();
        ThirdPartyWechatBaseResponse response = null;
        if (StringUtils.isNotBlank(unionId)) {
            response = getMemberIdListByUnionId(unionId, thirdPartyConfigDTO);
        }
        if(Objects.nonNull(response)&& CollectionUtils.isNotEmpty(response.getData())){
            return response.getData().stream().filter(i -> Objects.equals(i.getMemberSystem(), 0))
                    .map(MemberInfoDTO::getMemberId).findFirst().orElse(null);
        }
        return null;
    }


    @Override
    public QueryMsgRecordRspDTO queryMsgRecord(QueryMsgRecordReqDTO req) {
        LoggerUtils.info(log, "查询聊天记录，req：{}", FastJsonUtils.toJSONString(req));

        List<MsgRecordDTO> msgRecordDTOS = new ArrayList<>();
        String msgSendTime = null;
        if (Objects.nonNull(req.getLastMsgRecordId())) {
            SfcImMsgRecord lastSfcImMsgRecord = sfcImMsgRecordDao.getById(req.getLastMsgRecordId());
            LoggerUtils.info(log, "上一次消息记录：{}", FastJsonUtils.toJSONString(lastSfcImMsgRecord));
            if (Objects.nonNull(lastSfcImMsgRecord)) {
                msgSendTime = DateUtil.date2String(lastSfcImMsgRecord.getMsgSendTime());
            }
        }

        //查询方向 0-向下 1-向上
        Integer queryDirection = req.getQueryDirection();
        queryDirection = Objects.isNull(queryDirection) ? 0 : queryDirection;

        //查询条数
        Integer queryNum = req.getQueryNum();
        queryNum = Objects.isNull(queryNum) ? 15 : queryNum;

        //防止查询数据过多，目前写死查询6个月内的聊天记录
        String createTime = DateUtil.date2String(DateUtil.addMonth(new Date(), -6));

        QueryMsgRecordReqDTO msgRecordReq = new QueryMsgRecordReqDTO();
        BeanUtils.copyProperties(req, msgRecordReq);
        msgRecordReq.setCreateTime(createTime);
        msgRecordReq.setMsgSendTime(msgSendTime);
        msgRecordReq.setQueryDirection(queryDirection);
        msgRecordReq.setQueryNum(queryNum);

        List<String> sessionKeys = Lists.newArrayList(req.getSessionKey());

        if(OrderChannelEnum.isXcx(req.getPlatId()) || OrderChannelEnum.isTCAppWithoutMada(req.getPlatId())){
            String otherSessionKey = null;
            String userId = null;
            String oldSessionKey = req.getSessionKey();
            otherSessionKey = getNewSessionKey(oldSessionKey, userId);
            // 不管是不是null
            sessionKeys.add(otherSessionKey);
        }


        long start = System.currentTimeMillis();
        List<SfcImMsgRecord> sfcImMsgRecords = sfcImMsgRecordDao.queryMsgRecord(sessionKeys.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList()),
                createTime, msgSendTime, queryDirection, queryNum);
        LoggerUtils.info(log, "聊天记录条数：{}，内容：{}，req：{}，延迟：{}", sfcImMsgRecords.size(), FastJsonUtils.toJSONString(sfcImMsgRecords),
                FastJsonUtils.toJSONString(msgRecordReq), System.currentTimeMillis() - start);

        if (CollectionUtils.isEmpty(sfcImMsgRecords)) {
            LoggerUtils.warn(log, "未查询到聊天记录，查询req：{}", FastJsonUtils.toJSONString(msgRecordReq));
            return QueryMsgRecordRspDTO.success(req.getTraceId(), msgRecordDTOS);
        }

        Collections.reverse(sfcImMsgRecords);
        msgRecordDTOS = sfcImMsgRecords.stream().map(msgRecord -> {
            MsgRecordDTO dto = new MsgRecordDTO();
            BeanUtils.copyProperties(msgRecord, dto);
            dto.setMsgRecordId(msgRecord.getId());
            ImMsgRecordDTO imMsgRecordDTO = new ImMsgRecordDTO();
            BeanUtils.copyProperties(msgRecord, imMsgRecordDTO);
            MsgContentDTO.build(dto, imMsgRecordDTO);
            return dto;
        }).collect(Collectors.toList());

        List<MsgRecordDTO> recordDTOS = msgRecordDTOS.stream().filter(msgRecordDTO -> Objects.equals(msgRecordDTO.getMsgType(), MsgTypeEnum.TIP.getMsgType())
                        || Objects.equals(msgRecordDTO.getMsgType(), MsgTypeEnum.NIGHT_WARN.getMsgType()))
                .collect(Collectors.toList());
        msgRecordDTOS.removeIf(msgRecordDTO -> Objects.equals(msgRecordDTO.getMsgType(), MsgTypeEnum.TIP.getMsgType())
                || Objects.equals(msgRecordDTO.getMsgType(), MsgTypeEnum.NIGHT_WARN.getMsgType()));
        msgRecordDTOS.stream().forEach(msgRecordDTO -> recordDTOS.add(msgRecordDTO));

        LoggerUtils.info(log, "查询聊天记录，rsp：{}", req.filter1(), FastJsonUtils.toJSONString(recordDTOS));
        Optional<MsgRecordDTO> lastMsgRecord = recordDTOS.stream().min(Comparator.comparing(MsgRecordDTO::getMsgSendTime));
        if (lastMsgRecord.isPresent()) {
            Long lastMsgRecordId = lastMsgRecord.get().getMsgRecordId();
            LoggerUtils.info(log, "查询消息列表，lastMsgRecordId：{}", lastMsgRecordId);
            return QueryMsgRecordRspDTO.success(req.getTraceId(), recordDTOS, lastMsgRecordId);
        }
        return QueryMsgRecordRspDTO.success(req.getTraceId(), recordDTOS);
    }

    private String getNewSessionKey(String oldSessionKey, String userId) {
        String otherSessionKey;
        String oldUserId = splitOldSessionKey4UserId(oldSessionKey);
        if(NumberUtil.isLong(oldUserId)){
            userId = getUnionIdByMemberId(oldUserId);
        }else if(StringUtils.isNotBlank(oldUserId)){
            Long memberIdByUnionId = getMemberIdByUnionId(oldUserId);
            userId = Objects.nonNull(memberIdByUnionId)? memberIdByUnionId.toString():null;
        }
        otherSessionKey = replaceSessionKey(oldSessionKey, userId);
        return otherSessionKey;
    }

    private static String splitOldSessionKey4UserId(String oldSessionKey) {
        String[] splits = oldSessionKey.split("_");
        if(splits.length == SESSION_KEY_LENGTH){
            return   splits[1];
        }else if(splits.length > SESSION_KEY_LENGTH){
            // XXXX_XXXX_XXXX_XXXX_XXX  中间的都认为是用户ID
            return String.join("_", Arrays.copyOfRange(splits, 1, splits.length - 1));
        }
        return null;
    }

    @Override
    public GetQuickMsgRspDTO getQuickMsgList(GetQuickMsgReqDTO req) {
        LoggerUtils.info(log, "获取消息快捷语 req：{}", FastJsonUtils.toJSONString(req));

        try {
            MsgConfigDTO msgConfig = ImConfigCenter.getMsgConfig(req.getProductType(), req.getOrderId());
            assert msgConfig != null;
            List<String> quickMsgList = msgConfig.getQuickMsgList().stream().filter(quickMsg -> quickMsg.getSceneTypeList().contains(req.getSceneType())).findFirst()
                    .map(MsgConfigDTO.QuickMsg::getMsgList).orElse(Collections.emptyList());

            String orderId = req.getOrderId();
            OrderDetailDTO orderDetail = orderService.getOrderDetail(orderId);
            if (Objects.isNull(orderDetail)) {
                LoggerUtils.warn(log, "订单不存在" + orderId);
                throw new ValidateException(BIZ_ERROR_FACTORY.orderNotFoundError(orderId));
            }

            int status = orderDetail.getStatus();
            if (!Objects.equals(status, OrderStatusEnum.DISPATCHING.getCode()) || Objects.equals(status, OrderStatusEnum.RE_DISPATCHING.getCode())) {
                LoggerUtils.warn(log, "订单状态不在约束范围内：{}", status);
                return GetQuickMsgRspDTO.success(req.getTraceId(), quickMsgList);
            }

            LoggerUtils.info(log, "获取消息快捷语 rsp：{}", FastJsonUtils.toJSONString(quickMsgList));

            return GetQuickMsgRspDTO.success(req.getTraceId(), quickMsgList);
        } catch (ValidateException validateException) {
            return GetQuickMsgRspDTO.fail(req.getTraceId(), validateException.getErrorCode(), validateException.getError().getMessage());
        } catch (Exception e) {
            LoggerUtils.error(log, "获取消息快捷语异常 req：{}", FastJsonUtils.toJSONString(req), e);
            return GetQuickMsgRspDTO.fail(req.getTraceId(), ERROR_CODE, "获取消息快捷语失败");
        }
    }

    @Override
    public GetDriverOnlineStatusRspDTO getDriverOnlineStatus(GetDriverOnlineStatusReqDTO req) {
        LoggerUtils.info(log, "[getDriverOnlineStatus] 获取司机在离线状态 req：{}", FastJsonUtils.toJSONString(req));

        try {
            OrderRelationResponse orderRelationResponse = orderService.orderRelationInfo(Strings.EMPTY, req.getSupplierOrderId());
            if (Objects.isNull(orderRelationResponse)) {
                throw new ValidateException(BIZ_ERROR_FACTORY.supOrderQueryTcOrderError(req.getSupplierOrderId()));
            }
            DriverOnlineStatusRequest driverOnlineStatusRequest = new DriverOnlineStatusRequest();
            driverOnlineStatusRequest.setDriverId(req.getDriverId());
            driverOnlineStatusRequest.setPhoneNo(orderRelationResponse.getPhoneNo());
            String supplierCode = orderRelationResponse.getSupplierCode();
            driverOnlineStatusRequest.setSupplierCode(supplierCode);
            driverOnlineStatusRequest.setSupplierSerialNo(req.getSupplierOrderId());
            driverOnlineStatusRequest.setTcSerialNo(orderRelationResponse.getOrderSerialNo());
            driverOnlineStatusRequest.setMemberId(orderRelationResponse.getMemberId());
            driverOnlineStatusRequest.setTraceId(req.getTraceId());
            DriverOnlineStatusResponse driverOnlineStatusResponse = sendService.getDriverOnlineStatus(driverOnlineStatusRequest);
            if (Objects.nonNull(driverOnlineStatusResponse)) {
                DriverOnlineStatusDTO driverOnlineStatusDTO = new DriverOnlineStatusDTO();
                driverOnlineStatusDTO.setOnlineStatus(driverOnlineStatusResponse.getStatus());
                driverOnlineStatusDTO.setStatusText(driverOnlineStatusResponse.getStatusText());
                driverOnlineStatusDTO.setStatusColor(driverOnlineStatusResponse.getStatusColor());
                driverOnlineStatusDTO.setTips(driverOnlineStatusResponse.getTips());
                return GetDriverOnlineStatusRspDTO.success(req.getTraceId(), driverOnlineStatusDTO);
            }
        } catch (ValidateException validateException) {
            return GetDriverOnlineStatusRspDTO.fail(req.getTraceId(), validateException.getErrorCode(), validateException.getError().getMessage());
        } catch (Exception e) {
            LoggerUtils.error(log, "[getDriverOnlineStatus] 获取司机在离线状态异常 req：{}", FastJsonUtils.toJSONString(req), e);
        }
        return GetDriverOnlineStatusRspDTO.fail(req.getTraceId(), ERROR_CODE, "获取司机在离线状态异常:" + req.getDriverId());
    }

    @Override
    public UploadUserStatusRspDTO uploadUserStatus(UploadUserStatusReqDTO req) {
        LoggerUtils.info(log, "[uploadUserStatus] 上报用户状态 req：{}", FastJsonUtils.toJSONString(req));

        try {
            OrderRelationResponse orderRelationResponse = orderService.orderRelationInfo(Strings.EMPTY, req.getSupplierOrderId());
            if (Objects.isNull(orderRelationResponse)) {
                throw new ValidateException(BIZ_ERROR_FACTORY.supOrderQueryTcOrderError(req.getSupplierOrderId()));
            }
            UploadUserStatusRequest uploadUserStatusRequest = new UploadUserStatusRequest();
            uploadUserStatusRequest.setTcSerialNo(orderRelationResponse.getOrderSerialNo());
            uploadUserStatusRequest.setPhoneNo(orderRelationResponse.getPhoneNo());
            uploadUserStatusRequest.setSupplierSerialNo(req.getSupplierOrderId());
            uploadUserStatusRequest.setSupplierCode(orderRelationResponse.getSupplierCode());
            uploadUserStatusRequest.setMemberId(orderRelationResponse.getMemberId());
            uploadUserStatusRequest.setTraceId(req.getTraceId());
            UploadUserStatusResponse uploadUserStatusResponse = sendService.uploadUserStatus(uploadUserStatusRequest);
            if (Objects.nonNull(uploadUserStatusResponse)) {
                return UploadUserStatusRspDTO.success(req.getTraceId());
            }
        } catch (ValidateException validateException) {
            return UploadUserStatusRspDTO.fail(req.getTraceId(), validateException.getErrorCode(), validateException.getError().getMessage());
        } catch (Exception e) {
            LoggerUtils.error(log, "[uploadUserStatus] 上报用户状态异常 req：{}", FastJsonUtils.toJSONString(req), e);
        }
        return UploadUserStatusRspDTO.fail(req.getTraceId(), ERROR_CODE, "上报用户状态失败:" + req.getSupplierOrderId());
    }

    @Override
    public CheckAllowSendMsgRspDTO checkAllowSendMsg(CheckAllowSendMsgReqDTO req) {
        QueryDispatchListResponse queryDispatchListResponse = orderService.queryDispatchList(req.getTcOrderId());
        if (Objects.nonNull(queryDispatchListResponse) && queryDispatchListResponse.isSuccess()
                && CollectionUtils.isNotEmpty(queryDispatchListResponse.getDispatchVOList())) {
            Optional<DispatchVO> supplierOrder = queryDispatchListResponse.getDispatchVOList().stream().filter(item -> item.getThirdOrderSerialNo().equals(req.getSupplierOrderId())).findFirst();
            if (!supplierOrder.isPresent()){
                CheckAllowSendMsgRspDTO result = CheckAllowSendMsgRspDTO.fail(req.getTraceId(), ERROR_CODE, "订单不存在");
                result.setAllowSend(false);
                result.setSuccess(true);
                return  result;
            }
            if (supplierOrder.get().getDispatchState()==6){
                CheckAllowSendMsgRspDTO result = CheckAllowSendMsgRspDTO.fail(req.getTraceId(), ERROR_CODE, "订单已取消");
                result.setAllowSend(false);
                result.setSuccess(true);
                return result;
            }
            CheckAllowSendMsgRspDTO result = CheckAllowSendMsgRspDTO.success(req.getTraceId());
            result.setAllowSend(true);
            return result;
        }
        CheckAllowSendMsgRspDTO result = CheckAllowSendMsgRspDTO.fail(req.getTraceId(), ERROR_CODE, "检查发送消息权限失败");
        result.setAllowSend(false);
        return result;
    }

    @Override
    public DeleteMsgRspDTO deleteMsg(DeleteMsgReqDTO req) {
        LoggerUtils.info(log, "[deleteMsg][{}] 删除消息列表，req：{}", req.filter1(), FastJsonUtils.toJSONString(req));

        try {
            SfcImInfo sfcImInfo = sfcImInfoDao.getById(req.getId());
            SfcImInfo anotherSfcImInfo = null;
            if (Objects.isNull(sfcImInfo)) {
                throw new ValidateException(BIZ_ERROR_FACTORY.chatInfoNotFoundError(req.getId() + Strings.EMPTY));
            }
            String memberId = req.getMemberId();
            String unionId = req.getUnionId();
            if ((StringUtils.isBlank(unionId) && PlatformUtils.isWX(req.getPlatform()))
            || (StringUtils.isBlank(memberId) && PlatformUtils.isApp(req.getPlatform()))){
                LoggerUtils.warn(log, "[queryUnreadMsg][{}] 删除消息，用户信息为空", req.filter1());
                return DeleteMsgRspDTO.fail(req.getTraceId(), ERROR_CODE, "删除消息失败", req.getId());
            }

            List<Long> ids = Lists.newArrayList(req.getId());
            List<String> unionIds =  Lists.newArrayList(sfcImInfo.getUnionId());
            List<Long> memberIds =  Lists.newArrayList(sfcImInfo.getMemberId());
            if(OrderChannelEnum.isXcx(req.getPlatId()) || OrderChannelEnum.isTCAppWithoutMada(req.getPlatId())){
                String otherSessionKey = null;
                String userId = null;
                String oldSessionKey = sfcImInfo.getSessionKey();
                if (StringUtils.isBlank(sfcImInfo.getUnionId()) && Objects.nonNull(sfcImInfo.getMemberId())) {
                    userId = getUnionIdByMemberId(sfcImInfo.getMemberId().toString());
                    if (StringUtils.isNotEmpty(userId)){
                        unionIds.add(userId);
                    }
                } else {
                    Long memberIdByUnionId = getMemberIdByUnionId(sfcImInfo.getUnionId());
                    userId = Objects.nonNull(memberIdByUnionId) ? memberIdByUnionId.toString() : null;
                    if (StringUtils.isNotEmpty(userId)){
                        memberIds.add(Long.valueOf(userId));
                    }
                }
                otherSessionKey = replaceSessionKey(oldSessionKey, userId);
                anotherSfcImInfo  =  !StringUtils.isBlank(otherSessionKey)?sfcImInfoDao.findBySessionKey(otherSessionKey,null):null;
                if(Objects.nonNull(anotherSfcImInfo)){
                    ids.add(anotherSfcImInfo.getId());
                }
            }


            if (PlatformUtils.isWX(req.getPlatform()) && !unionIds.contains(unionId)) {
                return DeleteMsgRspDTO.fail(req.getTraceId(), ERROR_CODE, "删除消息失败", req.getId());
            }

            DecryptMemberConfigDTO decryptMemberConfig = ImConfigCenter.getDecryptMemberConfig();
            if (Objects.nonNull(req.getPlatId()) && req.getPlatId() != 0) {
                if (CollectionUtils.isNotEmpty(Objects.requireNonNull(decryptMemberConfig).getPlateIds())
                        && decryptMemberConfig.getPlateIds().contains(req.getPlatId())) {
                    BaseResponse checkMemberTokenDto = getMemberInfo(memberId, decryptMemberConfig);
                    memberId = Objects.equals(checkMemberTokenDto.getCode(), "0") ? checkMemberTokenDto.getData().getMemberId() : memberId;
                } else if (OrderChannelEnum.RIDE.getCode() == req.getPlatId()) {
                    memberId = StringUtils.isEmpty(memberId) ? null : PlatformUtils.memberIdDecrypt(memberId);
                } else if (Objects.equals(req.getPlatform(), "APP")) {
                    //app兜底解密
                    String handlerMemberId = PlatformUtils.handlerMemberId(memberId, req.getPlatform());
                    memberId = StringUtils.isNotEmpty(handlerMemberId) ? handlerMemberId : memberId;
                }
            } else {
                memberId = StringUtils.isEmpty(memberId) ? null : PlatformUtils.handlerMemberId(memberId, req.getPlatform());
            }

            if(PlatformUtils.isApp(req.getPlatform())){
                if (StringUtils.isBlank(memberId)  || !NumberUtil.isLong(memberId) || !memberIds.contains(Long.valueOf(memberId) )) {
                    return DeleteMsgRspDTO.fail(req.getTraceId(), ERROR_CODE, "删除消息失败", req.getId());
                }
            }

            //删除对应会话消息
            sfcImInfoDao.removeByIds(ids);
            redisClientProxy.remove(RedisKeyBuilder.IM_INFO_KEY + sfcImInfo.getSessionKey());
            if (Objects.nonNull(anotherSfcImInfo)){
                redisClientProxy.remove(RedisKeyBuilder.IM_INFO_KEY + anotherSfcImInfo.getSessionKey());
            }
            LoggerUtils.info(log, "[deleteMsg][{}] 删除消息列表成功", req.filter1());
            return DeleteMsgRspDTO.success(req.getTraceId(), req.getId());
        } catch (ValidateException validateException) {
            return DeleteMsgRspDTO.fail(req.getTraceId(), validateException.getErrorCode(), validateException.getError().getMessage(), req.getId());
        } catch (Exception e) {
            LoggerUtils.error(log, "[deleteMsg] 删除消息异常 req：{}", FastJsonUtils.toJSONString(req), e);
            return DeleteMsgRspDTO.fail(req.getTraceId(), ERROR_CODE, "删除消息失败", req.getId());
        }
    }

    public static void main(String[] args) {
        System.out.println( PlatformUtils.handlerMemberId("482587c04a4bda47aac2f49f0212c084", "APP"));
    }
    @Override
    public QueryUnreadMsgRspDTO queryUnreadMsg(QueryUnreadMsgReqDTO req) {
        LoggerUtils.info(log, "[queryUnreadMsg][{}] 查询未读消息列表，req：{}", req.filter1(), FastJsonUtils.toJSONString(req));

        UnreadMsgDTO unreadMsg = new UnreadMsgDTO();
        String memberId = req.getMemberId();
        String unionId = req.getUnionId();
        if (StringUtils.isAllEmpty(unionId, req.getSessionKey(), memberId)) {
            LoggerUtils.warn(log, "[queryUnreadMsg][{}] 查询未读消息列表，用户信息为空", req.filter1());
            return QueryUnreadMsgRspDTO.success(req.getTraceId(), unreadMsg);
        }

        //防止查询数据过多，目前写死查询6个月内的消息列表
        String createTime = DateUtil.date2String(DateUtil.addMonth(new Date(), -6));

        QueryMsgDTO queryMsgDTO = new QueryMsgDTO();
        BeanUtils.copyProperties(req, queryMsgDTO);
        queryMsgDTO.setLatestMsgTime(createTime);
        queryMsgDTO.setOnlyQueryUnreadMsg(Boolean.TRUE);

        List<SfcImInfo> sfcImInfoList = new ArrayList<>();

        if (StringUtils.isNotBlank(unionId) && PlatformUtils.isWX(req.getPlatform())) {
            queryMsgDTO.setMemberId(null);
            queryMsgDTO.setUnionId(unionId);
            sfcImInfoList = sfcImInfoDao.findUnreadMsgInfo(queryMsgDTO);
        }

        DecryptMemberConfigDTO decryptMemberConfig = ImConfigCenter.getDecryptMemberConfig();

        if (Objects.nonNull(req.getPlatId()) && (req.getPlatId()) != 0) {
            if (CollectionUtils.isNotEmpty(Objects.requireNonNull(decryptMemberConfig).getPlateIds())
                    && decryptMemberConfig.getPlateIds().contains(req.getPlatId())) {
                BaseResponse checkMemberTokenDto = getMemberInfo(memberId, decryptMemberConfig);
                memberId = Objects.equals(checkMemberTokenDto.getCode(), "0") ? checkMemberTokenDto.getData().getMemberId() : memberId;
            } else if (OrderChannelEnum.RIDE.getCode() == req.getPlatId()) {
                memberId = StringUtils.isEmpty(memberId) ? null : PlatformUtils.memberIdDecrypt(memberId);
            }
        } else {
            memberId = StringUtils.isEmpty(memberId) ? null : PlatformUtils.handlerMemberId(memberId, req.getPlatform());
        }
        if (StringUtils.isNotBlank(memberId) && PlatformUtils.isApp(req.getPlatform())) {
            queryMsgDTO.setMemberId(Long.valueOf(memberId));
            queryMsgDTO.setUnionId(null);
            queryMsgDTO.setSessionKey(req.getSessionKey());
            List<SfcImInfo> sfcImInfos = sfcImInfoDao.findUnreadMsgInfo(queryMsgDTO);
            sfcImInfoList.addAll(sfcImInfos);
        }

        LoggerUtils.info(log, "[queryUnreadMsg]  用户：{}  未读聊天列表信息：{}", req.filter1(), FastJsonUtils.toJSONString(sfcImInfoList));

        //全部未读消息数量
        unreadMsg.setTotalUnreadNum(sfcImInfoList.stream().collect(Collectors.summingInt(SfcImInfo::getUnreadMsgNum)));
        unreadMsg.setMsgList(sfcImInfoList.stream().map(sfcImInfo -> {
            MsgDTO dto = new MsgDTO();
            BeanUtils.copyProperties(sfcImInfo, dto);
            return dto;
        }).collect(Collectors.toList()));

        LoggerUtils.info(log, "[queryUnreadMsg][{}] 查询未读消息列表，rsp：{}", req.filter1(), FastJsonUtils.toJSONString(unreadMsg));
        return QueryUnreadMsgRspDTO.success(req.getTraceId(), unreadMsg);
    }

    @Override
    public QueryUnreadMsgCountRspDTO queryUnreadMsgCount(QueryUnreadMsgCountReqDTO req) {
        LoggerUtils.info(log, "查询未读消息列表数量，req：{}", FastJsonUtils.toJSONString(req));

        UnreadMsgDTO unreadMsg = new UnreadMsgDTO();
        String unionId = req.getUnionId();
        String sessionKey = req.getSessionKey();
        String memberId = req.getMemberId();
        if (StringUtils.isAllEmpty(unionId, sessionKey, memberId)) {
            LoggerUtils.warn(log, "查询未读消息列表数量，用户信息为空");
            return QueryUnreadMsgCountRspDTO.success(req.getTraceId(), unreadMsg);
        }

        String platform = req.getPlatform();

        DecryptMemberConfigDTO decryptMemberConfig = ImConfigCenter.getDecryptMemberConfig();
        if (Objects.nonNull(req.getPlatId()) && Objects.nonNull(OrderChannelEnum.getByPlatId(req.getPlatId()))) {
            if (decryptMemberConfig.getPlateIds().contains(req.getPlatId())) {
                //支付宝、马达会员解密
                BaseResponse checkMemberTokenDto = getMemberInfo(memberId, decryptMemberConfig);
                memberId = Objects.equals(checkMemberTokenDto.getCode(), "0") ? checkMemberTokenDto.getData().getMemberId() : memberId;
            } else if (OrderChannelEnum.RIDE.getCode() == req.getPlatId()) {
                //乘车呗
                String memberIdDecrypt = PlatformUtils.memberIdDecrypt(memberId);
                memberId = Objects.nonNull(memberIdDecrypt) ? memberIdDecrypt : memberId;
            } else if (Objects.equals(req.getPlatform(), "APP")) {
                //app兜底解密
                String handlerMemberId = PlatformUtils.handlerMemberId(memberId, req.getPlatform());
                memberId = StringUtils.isNotEmpty(handlerMemberId) ? handlerMemberId : memberId;
            }
        } else {
            //老版本 APP & WX 逻辑
            if (Objects.equals(req.getPlatform(), "APP")) {
                //app解密
                String handlerMemberId = PlatformUtils.handlerMemberId(memberId, req.getPlatform());
                memberId = StringUtils.isNotEmpty(handlerMemberId) ? handlerMemberId : memberId;
            }
        }
        //防止查询数据过多，目前写死查询6个月内的消息列表
        String createTime = DateUtil.date2String(DateUtil.addMonth(new Date(), -6));

        List<SfcImInfo> sfcImInfoList = new ArrayList<>();

        if(OrderChannelEnum.isXcx(req.getPlatId()) || OrderChannelEnum.isTCAppWithoutMada(req.getPlatId())){
            List<SfcImInfo> sfcImInfoTmpList = new ArrayList<>();
            String  appSessionKey = null;
            String  wxSessionKey = null;
            String   anotherMemberId = null;
            String  anotherUnionId = null;
            if (StringUtils.isAllEmpty(unionId, memberId)) {
                LoggerUtils.warn(log, "查询未读消息列表数量，用户信息为空");
                return QueryUnreadMsgCountRspDTO.success(req.getTraceId(), unreadMsg);
            }
            if(StringUtils.isNotBlank(unionId) && PlatformUtils.isWX(platform)){
                wxSessionKey = sessionKey;
                Long memberIdByUnionId = getMemberIdByUnionId(unionId);
                anotherMemberId = Objects.nonNull(memberIdByUnionId)? memberIdByUnionId.toString() : null;
                appSessionKey = replaceSessionKey(wxSessionKey,anotherMemberId);
            }

            if(StringUtils.isNotBlank(memberId) && PlatformUtils.isApp(platform)){
                appSessionKey = sessionKey;
                anotherUnionId = getUnionIdByMemberId(memberId);
                wxSessionKey = replaceSessionKey(appSessionKey, anotherUnionId);
            }

            if (!StringUtils.isAllEmpty(unionId,anotherUnionId)) {
                QueryMsgDTO queryMsgDTO = new QueryMsgDTO();
                BeanUtils.copyProperties(req, queryMsgDTO);
                queryMsgDTO.setLatestMsgTime(createTime);
                queryMsgDTO.setSessionKey(wxSessionKey);
                queryMsgDTO.setMemberId(null);
                queryMsgDTO.setUnionId(StringUtils.isBlank(unionId) ? anotherUnionId : unionId);
                sfcImInfoTmpList.addAll(sfcImInfoDao.findUnreadMsgInfo(queryMsgDTO));
                if(!StringUtils.isBlank(unionId)){
                    memberId = null;
                }
            }
            if (!StringUtils.isAllEmpty(memberId,anotherMemberId)) {
                QueryMsgDTO queryMsgDTO = new QueryMsgDTO();
                BeanUtils.copyProperties(req, queryMsgDTO);
                queryMsgDTO.setLatestMsgTime(createTime);
                queryMsgDTO.setSessionKey(appSessionKey);
                queryMsgDTO.setMemberId(Long.valueOf(StringUtils.isBlank(memberId) ? anotherMemberId : memberId) );
                queryMsgDTO.setUnionId(null);
                sfcImInfoTmpList.addAll(sfcImInfoDao.findUnreadMsgInfo(queryMsgDTO));
            }

            LoggerUtils.info(log, "用户：{}  未读聊天列表信息sfcImInfoTmpList：{}", req.filter1(), FastJsonUtils.toJSONString(sfcImInfoTmpList));
            sfcImInfoList = new ArrayList<>(sfcImInfoTmpList.stream().collect(Collectors.toMap(i -> i.getSupplierCode() + "_" + i.getDriverId(), Function.identity(),
                    (existing, replacement) -> {
                        // 保留UpdateTime 最近的
                        if (existing.getUpdateTime().compareTo(replacement.getUpdateTime()) < 0) {
                            return replacement;
                        }
                        return existing;
                    })).values());
        }else{
            // 非小程序和非马达APP的逻辑
            QueryMsgDTO queryMsgDTO = new QueryMsgDTO();
            BeanUtils.copyProperties(req, queryMsgDTO);
            queryMsgDTO.setLatestMsgTime(createTime);
            queryMsgDTO.setOnlyQueryUnreadMsg(Boolean.TRUE);

            if (StringUtils.isNotBlank(memberId) && PlatformUtils.isApp(platform)) {
                if (Objects.nonNull(req.getPlatId())) {
                    SessionRuleConfigDTO sessionKeyRule = ImConfigCenter.getSessionKeyRule(req.getPlatId());
                    if (sessionKeyRule.getIsIsolate() == 1) {
                        queryMsgDTO.setPlateId(req.getPlatId());
                    }
                }
                queryMsgDTO.setMemberId(Long.valueOf(memberId));
                queryMsgDTO.setUnionId(null);
                queryMsgDTO.setSessionKey(sessionKey);
                List<SfcImInfo> sfcImInfos = sfcImInfoDao.findUnreadMsgInfo(queryMsgDTO);
                sfcImInfoList.addAll(sfcImInfos);
            }
        }
        LoggerUtils.info(log, "用户：{}  未读聊天列表信息：{}", req.filter1(), FastJsonUtils.toJSONString(sfcImInfoList));
         sfcImInfoList = sfcImInfoList.stream().filter(e -> e.getUnreadMsgNum() > 0).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(sfcImInfoList)) {
            List<SfcImInfo> sfcImInfos = null;
            switch (req.getProductType()) {
                case 0:
                    List<MsgDTO> msgList = sfcImInfoList.stream().filter(e -> StringUtils.equals(e.getOrderId().substring(0, 3), ProductTypeNum.SFC.getOrderPrefix()))
                            .map(sfcImInfo -> {
                                MsgDTO dto = new MsgDTO();
                                BeanUtils.copyProperties(sfcImInfo, dto);
                                return dto;
                            }).collect(Collectors.toList());

                    unreadMsg.setMsgList(msgList);
                    unreadMsg.setSfcUnreadNum(sfcImInfoList.stream().filter(e -> StringUtils.equals(e.getOrderId().substring(0, 3), ProductTypeNum.SFC.getOrderPrefix()))
                            .mapToInt(SfcImInfo::getUnreadMsgNum).sum());
                    break;
                case 1:
                    sfcImInfos = sfcImInfoList.stream().filter(e -> StringUtils.equals(e.getOrderId().substring(0, 3), ProductTypeNum.WYC.getOrderPrefix()))
                            .collect(Collectors.toList());
                    unreadMsg.setWycUnreadNum(sfcImInfos.stream().collect(Collectors.summingInt(SfcImInfo::getUnreadMsgNum)));
                    break;
                case 2:
                    sfcImInfos = sfcImInfoList.stream().filter(e -> StringUtils.equalsIgnoreCase(e.getOrderId().substring(0, 3), ProductTypeNum.WNC.getOrderPrefix()))
                            .collect(Collectors.toList());
                    unreadMsg.setWncUnreadNum(sfcImInfos.stream().collect(Collectors.summingInt(SfcImInfo::getUnreadMsgNum)));
                    break;
            }
        }

        LoggerUtils.info(log, "查询未读消息数量，rsp：{}", FastJsonUtils.toJSONString(unreadMsg));
        return QueryUnreadMsgCountRspDTO.success(req.getTraceId(), unreadMsg);
    }

    private String replaceSessionKey(String sessionKey, String userId) {
        if(StringUtils.isBlank(sessionKey)|| StringUtils.isBlank(userId)){
            return null;
        }
        // 会话标识（供应商Code/子供应商Code + 会员ID/unionId + 司机ID）  例子CaoCao_1234567890_1234567890
        // 将中间的换成userId
        String[] sessionKeyParts = sessionKey.split("_");
        if (sessionKeyParts.length < SESSION_KEY_LENGTH) {
            LoggerUtils.warn(log, "会话标识格式不正确，无法替换 sessionKey: {}", sessionKey);
            return null;
        }
        if(sessionKeyParts.length == SESSION_KEY_LENGTH){
            return sessionKeyParts[0] + "_" + userId + "_" + sessionKeyParts[2];
        }

        LoggerUtils.warn(log, "会话标识分隔后超过3位  sessionKey: {}", sessionKey);
        return sessionKeyParts[0] + "_" + userId + "_" + sessionKeyParts[SESSION_KEY_LENGTH - 1];

    }


    @Override
    public QueryIsDriverUnreadMsgRspDTO queryIsDriverUnreadMsg(QueryIsDriverUnreadMsgReqDTO req) {
        LoggerUtils.info(log, "[queryIsDriverUnreadMsg] 查询是否有司机未读消息，req：{}", FastJsonUtils.toJSONString(req));
        List<DriverUnreadMsgDTO> driverMessageRequests = req.getDriverMessageRequests();
        List<SfcImInfo> sfcImInfoList = Optional.ofNullable(sfcImInfoDao.findDriverUnreadMsgInfo(driverMessageRequests)).orElse(Collections.emptyList());
        Set<String> driverIds = driverMessageRequests.stream().map(DriverUnreadMsgDTO::getDriverId).collect(Collectors.toSet());
        List<DriverUnreadMsgDTO> collect = sfcImInfoList.stream().filter(dto -> driverIds.contains(dto.getDriverId())).map(sfcImInfo -> {
            DriverUnreadMsgDTO driverUnreadMsgDTO = new DriverUnreadMsgDTO();
            driverUnreadMsgDTO.setDriverId(sfcImInfo.getDriverId());
            driverUnreadMsgDTO.setOrderId(sfcImInfo.getOrderId());
            driverUnreadMsgDTO.setUnreadMsgNum(sfcImInfo.getUnreadMsgNum());
            return driverUnreadMsgDTO;
        }).collect(Collectors.toList());
        LoggerUtils.info(log, "[queryIsDriverUnreadMsg] 查询是否有司机未读消息，rsp：{}", FastJsonUtils.toJSONString(collect));
        return QueryIsDriverUnreadMsgRspDTO.success(req.getTraceId(), collect);
    }

    @Override
    public GetVersionRspDTO getVersion(GetVersionReqDTO req) {
        LoggerUtils.info(log, "获取版本，req：{}", FastJsonUtils.toJSONString(req));

        Integer type = req.getType();
        String platform = req.getPlatform();
        Integer platId = req.getPlatId();
        VersionDTO versionDTO = FastJsonUtils.fromJSONString(ImConfigCenter.getVersion(), VersionDTO.class);
        String version = versionDTO.getVersion();
        String link = Strings.EMPTY;

        if (Objects.equals(type, 0)) { //列表页
            if ("v1".equals(version)) {
                link = versionDTO.getList().getOldLink();
            } else if ("v2".equals(version)) {
                if (PlatformUtils.isApp(platform)) {
                    link = getAppListLink(req, platId, link, versionDTO);
                } else if (PlatformUtils.isWX(platform)) {
                    link = getWXListLink(req, platId, link, versionDTO);
                }
            }
        } else if (Objects.equals(type, 1)) { //聊天页
            if ("v1".equals(version)) {
                link = versionDTO.getChat().getOldLink();
            } else if ("v2".equals(version)) {
                if (PlatformUtils.isApp(platform)) {
                    link = getAppChatLink(req, platId, link, versionDTO);
                } else if (PlatformUtils.isWX(platform)) {
                    link = getWXChatLink(req, platId, link, versionDTO);

                }
            }
        }
        return GetVersionRspDTO.success(req.getTraceId(), version, link);
    }

    private static String getAppListLink(GetVersionReqDTO req, Integer platId, String link, VersionDTO versionDTO) {
        if (Objects.nonNull(platId) && platId != 0) {
            if (OrderChannelEnum.alipay.getCode() == (req.getPlatId())) {
                link = versionDTO.getList().getAlipayLink();
            } else if (OrderChannelEnum.SHARED_alipay.getCode() == (req.getPlatId())) {
                link = versionDTO.getList().getSharedAlipayLink();
            } else if (OrderChannelEnum.MaDa_IOS.getCode() == (req.getPlatId()) ||
                    OrderChannelEnum.MaDa_Android.getCode() == (req.getPlatId())) {
                link = versionDTO.getList().getMadaLink();
            } else if (OrderChannelEnum.RIDE.getCode() == (req.getPlatId())) {
                link = versionDTO.getList().getRideLink();
            } else if (OrderChannelEnum.ELONG_IOS.getCode() == (req.getPlatId()) ||
                    OrderChannelEnum.ELONG_Android.getCode() == (req.getPlatId())){
                link = versionDTO.getList().getElongLink();
            }  else {
                link = versionDTO.getList().getNewAppLink();
            }
        } else {
            link = versionDTO.getList().getNewAppLink();
        }
        return link;
    }

    private static String getWXListLink(GetVersionReqDTO req, Integer platId, String link, VersionDTO versionDTO) {
        if (Objects.nonNull(platId) && platId != 0) {
            if (OrderChannelEnum.IN_XCX.getCode() == (req.getPlatId())) {
                link = versionDTO.getList().getInLink();
            } else {
                link = versionDTO.getList().getNewWxLink();
            }
        } else {
            link = versionDTO.getList().getNewWxLink();
        }
        return link;
    }

    private static String getWXChatLink(GetVersionReqDTO req, Integer platId, String link, VersionDTO versionDTO) {
        if (Objects.nonNull(platId) && platId != 0) {
            if (OrderChannelEnum.IN_XCX.getCode() == (req.getPlatId())) {
                link = versionDTO.getChat().getInLink();
            } else {
                link = versionDTO.getChat().getNewWxLink();
            }
        } else {
            link = versionDTO.getChat().getNewWxLink();
        }
        return link;
    }

    private static String getAppChatLink(GetVersionReqDTO req, Integer platId, String link, VersionDTO versionDTO) {
        if (Objects.nonNull(platId) && platId != 0) {
            if (OrderChannelEnum.alipay.getCode() == (req.getPlatId())) {
                link = versionDTO.getChat().getAlipayLink();
            } else if (OrderChannelEnum.SHARED_alipay.getCode() == (req.getPlatId())) {
                link = versionDTO.getChat().getSharedAlipayLink();
            } else if (OrderChannelEnum.MaDa_IOS.getCode() == (req.getPlatId()) ||
                    OrderChannelEnum.MaDa_Android.getCode() == (req.getPlatId())) {
                link = versionDTO.getChat().getMadaLink();
            } else if (OrderChannelEnum.RIDE.getCode() == (req.getPlatId())) {
                link = versionDTO.getChat().getRideLink();
            } else if (OrderChannelEnum.ELONG_IOS.getCode() == (req.getPlatId()) ||
                    OrderChannelEnum.ELONG_Android.getCode() == (req.getPlatId())){
                link = versionDTO.getChat().getElongLink();
            } else {
                link = versionDTO.getChat().getNewAppLink();
            }
        } else {
            link = versionDTO.getChat().getNewAppLink();
        }
        return link;
    }
    @Override
    public GetImKeyInfosRspDTO getImKeyInfos(GetImKeyInfosReqDTO req) {

        SessionRuleConfigDTO sessionKeyRule = ImConfigCenter.getSessionKeyRule(req.getPlateId());
        LoggerUtils.info(log, "[sessionKeyRule][{}] sessionKeyRule配置：{}", req.getDriverCode(), FastJsonUtils.toJSONString(sessionKeyRule));
        String imSessionKeyFinishTime = "";
        if (req.getSource() == CONVERSATION_HALL) {
            String imSessionKey = getRoomSessionKey(req, sessionKeyRule);
            return GetImKeyInfosRspDTO.success(req.getTraceId(), imSessionKey, 24, imSessionKeyFinishTime);
        }

        if (Objects.equals(req.getPlateId(), TencentTravelApplet) && ImConfigCenter.isCloseChat()) {
            LoggerUtils.info(log, "[sessionKeyRule][{}] 腾讯出行屏蔽im入口，无im：{}", req.getDriverCode(), req.getTraceId());
            return GetImKeyInfosRspDTO.success(req.getTraceId());
        }

        if (Objects.isNull(req.getOrderState())) {
            LoggerUtils.info(log, "[sessionKeyRule][{}] 无订单状态，无im：{}", req.getDriverCode(), req.getTraceId());
            return GetImKeyInfosRspDTO.success(req.getTraceId());
        }
        OrderStatusEnum orderState = OrderStatusEnum.getByCode(req.getOrderState());
        // 接单 - 完单 都可以有im
        if (orderState != OrderStatusEnum.RECEIVING_ORDER && orderState.getCode() < OrderStatusEnum.AWAITING_TRAVEL.getCode()) {
            LoggerUtils.info(log, "[sessionKeyRule][{}] 非接单 - 完单 无im：{}", req.getDriverCode(), req.getTraceId());
            return GetImKeyInfosRspDTO.success(req.getTraceId());
        }

        // 司机code不为空 可以有im
        if (StringUtils.isBlank(req.getDriverCode())) {
            LoggerUtils.info(log, "[sessionKeyRule][{}] 司机code为空 无im：{}", req.getDriverCode(), req.getTraceId());
            return GetImKeyInfosRspDTO.success(req.getTraceId());
        }

        // 完单后 24小时内可以有im
        if (orderState.getCode() >= OrderStatusEnum.TRIP_FINISHED.getCode() &&
                (StringUtils.isEmpty(req.getGmtTripFinished()) || new Date().after(DateUtil.addMinute(DateUtil.string2Date(req.getGmtTripFinished()), 60 * 24)))) {
            LoggerUtils.info(log, "[sessionKeyRule][{}] 完单后 24小时外无im：{}", req.getDriverCode(), req.getTraceId());
            return GetImKeyInfosRspDTO.success(req.getTraceId());
        }

        //供应商能力支持判断
        SupplierInterfaceCapabilityDTO supplierCapacity = supplierInterfaceCapabilityRepository.getBySupplierCode(req.getSupplierCode());
        if (!supplierCapacity.getCapabilitySet().contains("onlinecar.sendImMsg") && !supplierCapacity.getCapabilitySet().contains("hitchRide.sendImMsg")){
            LoggerUtils.info(log, "[sessionKeyRule][{}] 供应商不支持im：{}", req.getDriverCode(), req.getTraceId());
            return GetImKeyInfosRspDTO.success(req.getTraceId());
        }

        //会话标识（供应商Code/子供应商Code + 会员ID/unionId + 司机ID）
        String madaSaas = "MadaSaas";
        String supplierCode = req.getSupplierCode();
        if (StringUtils.startsWithIgnoreCase(supplierCode, madaSaas)) {
            supplierCode = madaSaas;
        }
        String userKey = sessionKeyRule.getIdType() == 1 ? req.getMemberId() : req.getUnionId();

        String imSessionKey = supplierCode + "_" + userKey + "_" + req.getDriverCode();


        //已经完单，用完单时间 + 24小时, 非完单，用当前时间 + 24小时
        if (OrderStatusEnum.getByCode(req.getOrderState()).getCode() >= OrderStatusEnum.TRIP_FINISHED.getCode()) {
            imSessionKeyFinishTime =
                    DateUtil.date2String(DateUtil.addMinute(DateUtil.string2Date(req.getGmtTripFinished()), 60 * req.getImFinishDelayHour()), DateUtil.DATE_PATTERN_YYYY_MM_DD_HH_MM_SS);
        }
        return GetImKeyInfosRspDTO.success(req.getTraceId(), imSessionKey, 24, imSessionKeyFinishTime);
    }


    @Override
    public QueryMsgRecordRspDTO queryInnerMsgRecord(QueryMsgRecordReqDTO req) {
        QueryDispatchListRequest queryDispatchListRequest = new QueryDispatchListRequest();
        QueryDispatchListResponse queryDispatchListResponse = orderFacade.queryDispatchList(queryDispatchListRequest);

        LoggerUtils.info(log, "查询聊天记录，req：{}", FastJsonUtils.toJSONString(req));

        List<MsgRecordDTO> msgRecordDTOS = new ArrayList<>();


        // 设置分页参数
        Integer pageNum = req.getPageNum();
        pageNum = Objects.isNull(pageNum) ? 1 : pageNum; // 默认第1页
        req.setPageNum(pageNum);
        
        Integer pageSize = req.getPageSize();
        pageSize = Objects.isNull(pageSize) ? 15 : pageSize; // 默认每页15条
        req.setPageSize(pageSize);

        if (StringUtils.isEmpty(req.getOrderId())|| StringUtils.isEmpty(req.getUserId())){
            LoggerUtils.info(log, "限制查询：{}", req.getTraceId());
            QueryMsgRecordRspDTO response =   QueryMsgRecordRspDTO.success(req.getTraceId(), msgRecordDTOS);
            response.setCurrentPage(pageNum);
            response.setPageSize(pageSize);
            response.setTotal(0L);
            return response;
        }

        // 记录开始查询时间
        long start = System.currentTimeMillis();
        
        // 查询聊天记录总数
        Long total = sfcImMsgRecordDao.countMsgRecord(req);
        LoggerUtils.info(log, "聊天记录总数：{}", total);
        
        // 查询聊天记录
        List<SfcImMsgRecord> sfcImMsgRecords = sfcImMsgRecordDao.queryMsgRecord(req);
        LoggerUtils.info(log, "聊天记录条数：{}，req：{}，延迟：{}", sfcImMsgRecords.size(), 
                FastJsonUtils.toJSONString(req), System.currentTimeMillis() - start);

        if (CollectionUtils.isEmpty(sfcImMsgRecords)) {
            LoggerUtils.warn(log, "未查询到聊天记录，查询req：{}", FastJsonUtils.toJSONString(req));
            QueryMsgRecordRspDTO emptyResponse = QueryMsgRecordRspDTO.success(req.getTraceId(), msgRecordDTOS);
            emptyResponse.setTotal(total);
            emptyResponse.setCurrentPage(pageNum);
            emptyResponse.setPageSize(pageSize);
            return emptyResponse;
        }

        // 处理查询结果
        msgRecordDTOS = sfcImMsgRecords.stream().map(record -> {
            MsgRecordDTO dto = new MsgRecordDTO();
            BeanUtils.copyProperties(record, dto);
            dto.setMsgRecordId(record.getId());
            
            // 根据msgId查询ES获取消息状态
            if (StringUtils.isNotBlank(record.getMsgId())) {
                try {
                    ESSearchResponse<ImDispatchToElasticSearchVO> esResponse = imDispatchClient.queryByMsgId(record.getMsgId());
                    if (esResponse != null && esResponse.getResult() != null && 
                        esResponse.getResult().getList() != null && !esResponse.getResult().getList().isEmpty()) {
                        // 获取消息状态
                        ImDispatchToElasticSearchVO esRecord = esResponse.getResult().getList().stream().max(Comparator.comparing(ImDispatchToElasticSearchVO::getStatus)).orElse(null);
                        if (esRecord != null && esRecord.getStatus() != null) {
                            // 设置消息状态
                            dto.setDispatchStatus(esRecord.getStatus());
                        }
                    }
                } catch (Exception e) {
                    LoggerUtils.error(log, "查询ES消息状态异常，msgId：{}", record.getMsgId(), e);
                }
            }
            
            ImMsgRecordDTO imMsgRecordDTO = new ImMsgRecordDTO();
            BeanUtils.copyProperties(record, imMsgRecordDTO);
            MsgContentDTO.build(dto, imMsgRecordDTO);
            return dto;
        }).collect(Collectors.toList());

        LoggerUtils.info(log, "查询聊天记录，rsp：{}", req.filter1(), FastJsonUtils.toJSONString(msgRecordDTOS));

        // 创建包含分页信息的响应
        QueryMsgRecordRspDTO response = QueryMsgRecordRspDTO.success(req.getTraceId(), msgRecordDTOS);
        
        // 设置分页信息
        response.setCurrentPage(pageNum);
        response.setPageSize(pageSize);
        response.setTotal(total);
        
        return response;
    }


    private static String getRoomSessionKey(GetImKeyInfosReqDTO req, SessionRuleConfigDTO sessionKeyRule) {
        String memberId = "";
        String imSessionKey = "";
        if (sessionKeyRule.getIdType() == 1) {
            DecryptMemberConfigDTO decryptMemberConfig = ImConfigCenter.getDecryptMemberConfig();
            if (Objects.nonNull(req.getPlateId())
                    && CollectionUtils.isNotEmpty(Objects.requireNonNull(decryptMemberConfig).getPlateIds())
                    && decryptMemberConfig.getPlateIds().contains(req.getPlateId())) {
                BaseResponse checkMemberTokenDto = getMemberInfo(req.getMemberId(), decryptMemberConfig);
                memberId = checkMemberTokenDto.getData().getMemberId();
            } else if (OrderChannelEnum.RIDE.getCode() == req.getPlateId()) {
                //乘车呗
                String memberIdDecrypt = PlatformUtils.memberIdDecrypt(memberId);
                memberId = Objects.nonNull(memberIdDecrypt) ? memberIdDecrypt : memberId;
            } else if (OrderChannelEnum.isAppForDecrypt(req.getPlateId())) {
                memberId = StringUtils.isEmpty(req.getMemberId()) ? null : PlatformUtils.handlerMemberId(req.getMemberId(), "APP");
            }
            imSessionKey = req.getSupplierCode() + "_" + memberId + "_" + req.getDriverCode();
        } else {
            imSessionKey = req.getSupplierCode() + "_" + req.getUnionId() + "_" + req.getDriverCode();
        }
        return imSessionKey;
    }

    @Override
    public GetChatSwitchInfosRspDTO getChatSwitchInfos(GetChatSwitchInfosReqDTO req) {
        LoggerUtils.info(log, "[getChatSwitchInfos] 获取聊天开关信息，req：{}", FastJsonUtils.toJSONString(req));

        GetChatSwitchInfosRspDTO response = GetChatSwitchInfosRspDTO.success(req.getTraceId());
        response.setChatSwitch(ImConfigCenter.isLimitMsg());
        response.setPhoneSwitch(ImConfigCenter.isLimitPhone());

        LoggerUtils.info(log, "[getChatSwitchInfos] 获取聊天开关信息，rsp：{}", FastJsonUtils.toJSONString(response));
        return response;
    }

}
