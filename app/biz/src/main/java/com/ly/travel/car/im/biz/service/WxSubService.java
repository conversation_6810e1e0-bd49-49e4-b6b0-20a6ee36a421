package com.ly.travel.car.im.biz.service;

import com.ly.travel.car.im.facade.dto.BaseDTO;
import com.ly.travel.car.im.facade.request.GetMsgTemplateReqDTO;
import com.ly.travel.car.im.facade.response.CallBackRspDTO;
import com.ly.travel.car.im.facade.response.GetMsgTemplateRspDTO;

public interface WxSubService {

    /**
     * 获取订阅消息模版
     */
    GetMsgTemplateRspDTO getMsgTemplate(GetMsgTemplateReqDTO req);

    /**
     * 订阅回调
     */
    CallBackRspDTO callBack(BaseDTO req);
}
