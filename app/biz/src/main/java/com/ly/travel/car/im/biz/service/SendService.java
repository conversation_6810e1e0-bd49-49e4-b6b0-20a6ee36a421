package com.ly.travel.car.im.biz.service;

import com.ly.sof.facade.base.BaseResponseDTO;
import com.ly.travel.car.im.biz.error.ValidateException;
import com.ly.travel.car.im.facade.request.SendMsgReqDTO;
import com.ly.travel.car.im.model.dto.OrderDetailDTO;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.driverOnlineStatus.DriverOnlineStatusRequest;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.driverOnlineStatus.DriverOnlineStatusResponse;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.imClose.ImCloseRequest;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.imClose.ImCloseResponse;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.imCreate.ImCreateRequest;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.imCreate.ImCreateResponse;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.uploadUserStatus.UploadUserStatusRequest;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.uploadUserStatus.UploadUserStatusResponse;

public interface SendService {

    /**
     * 乘客消息发送司机
     *
     * @param req
     * @param orderDetail
     * @return
     */
    BaseResponseDTO sendMsg(SendMsgReqDTO req, OrderDetailDTO orderDetail) throws ValidateException;

    /**
     * 上报用户状态
     *
     * @param req
     * @return
     */
    UploadUserStatusResponse uploadUserStatus(UploadUserStatusRequest req);

    /**
     * 获取司机在线状态
     *
     * @param req
     */
    DriverOnlineStatusResponse getDriverOnlineStatus(DriverOnlineStatusRequest req);

    /**
     * 创建会话  约约特性
     *
     * @param req
     * @return
     */
    ImCreateResponse openChatAction(ImCreateRequest req);

    /**
     * 关闭会话 约约特性
     *
     * @param req
     * @return
     */
    ImCloseResponse closeChatAction(ImCloseRequest req);
}
