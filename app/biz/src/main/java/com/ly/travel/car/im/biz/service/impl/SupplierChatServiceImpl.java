package com.ly.travel.car.im.biz.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.car.bean.Simple;
import com.ly.dal.util.DateUtil;
import com.ly.sof.api.mq.common.SerializeEnum;
import com.ly.sof.api.mq.producer.DefaultProducer;
import com.ly.sof.utils.log.LogContextUtils;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.distribution.facade.request.IMMessageSendRequest;
import com.ly.travel.car.distribution.facade.request.IMNotifyCallBackReq;
import com.ly.travel.car.distribution.facade.response.IMNotifyCallBackRsp;
import com.ly.travel.car.distribution.facade.response.IMSendMessageResponse;
import com.ly.travel.car.im.biz.config.AsyncConfig;
import com.ly.travel.car.im.biz.error.BizErrorFactory;
import com.ly.travel.car.im.biz.error.ValidateException;
import com.ly.travel.car.im.biz.service.OrderService;
import com.ly.travel.car.im.biz.service.SupplierChatService;
import com.ly.travel.car.im.biz.utils.ImUtils;
import com.ly.travel.car.im.biz.utils.SessionKeyUtil;
import com.ly.travel.car.im.dal.dao.SfcImInfoDao;
import com.ly.travel.car.im.dal.dao.SfcImMsgRecordDao;
import com.ly.travel.car.im.dal.entity.SfcImInfo;
import com.ly.travel.car.im.dal.entity.SfcImMsgRecord;
import com.ly.travel.car.im.facade.dto.*;
import com.ly.travel.car.im.facade.enums.MsgSenderEnum;
import com.ly.travel.car.im.facade.enums.MsgTypeEnum;
import com.ly.travel.car.im.facade.request.CloseChatActionReqDTO;
import com.ly.travel.car.im.facade.request.GetMsgReqDTO;
import com.ly.travel.car.im.facade.request.OpenChatActionReqDTO;
import com.ly.travel.car.im.facade.response.CloseChatActionRspDTO;
import com.ly.travel.car.im.facade.response.GetMsgRspDTO;
import com.ly.travel.car.im.facade.response.OpenChatActionRspDTO;
import com.ly.travel.car.im.integration.client.api.IdGeneratorApi;
import com.ly.travel.car.im.integration.client.api.PushApi;
import com.ly.travel.car.im.integration.client.api.RiskApi;
import com.ly.travel.car.im.integration.client.distribute.DistributeClient;
import com.ly.travel.car.im.integration.client.redis.impl.RedisClientProxy;
import com.ly.travel.car.im.integration.request.DriverMsgPushReqDTO;
import com.ly.travel.car.im.integration.request.RiskQueryReqDTO;
import com.ly.travel.car.im.integration.request.SafeWarnMsgReqDTO;
import com.ly.travel.car.im.integration.response.RiskQueryRspDTO;
import com.ly.travel.car.im.integration.response.RiskQueryDTO;
import com.ly.travel.car.im.integration.throwable.exception.IntegrationException;
import com.ly.travel.car.im.integration.utils.RedisKeyBuilder;
import com.ly.travel.car.im.model.config.ImConfigCenter;
import com.ly.travel.car.im.model.constant.MsgTipsConstant;
import com.ly.travel.car.im.model.constant.RocketDelayLevel;
import com.ly.travel.car.im.model.dto.*;
import com.ly.travel.car.im.model.enums.*;
import com.ly.travel.car.im.model.utils.DesensitizationUtils;
import com.ly.travel.car.im.model.utils.PlatformUtils;
import com.ly.travel.car.tradecore.model.enums.OrderState;
import com.ly.travel.car.tradecore.model.enums.OrderType;
import com.ly.travel.shared.mobility.supply.order.core.facade.order.response.OrderRelationResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;

import static com.ly.travel.car.im.biz.utils.DecryptMemberInfoUtil.getMemberIdListByMemberId;
import static com.ly.travel.car.im.biz.utils.DecryptMemberInfoUtil.getMemberIdListByUnionId;
import static com.ly.travel.car.im.integration.utils.RedisKeyBuilder.IM_DISTRIBUTION_MSG_KEY;


@Service
@Slf4j
public class SupplierChatServiceImpl implements SupplierChatService {
    private static final BizErrorFactory BIZ_ERROR_FACTORY = BizErrorFactory.getInstance();
    @Resource
    private RedisClientProxy redisClientProxy;
    @Resource
    private OrderService orderService;
    @Resource
    private SfcImMsgRecordDao sfcImMsgRecordDao;
    @Resource
    private SfcImInfoDao sfcImInfoDao;
    @Resource
    private RiskApi riskApi;
    @Resource
    private PushApi pushApi;

    @Resource
    private DistributeClient distributedClient;

    @Resource
    private IdGeneratorApi idGeneratorApi;
    @Resource(name = "producer")
    protected DefaultProducer defaultProducer;

    @Resource
    private AsyncConfig asyncConfig;

    @Value("${chat.mq.topic}")
    private String topic;

    @Value("${chat.dispatch.mq.topic}")
    private String dispatchTopic;

    private static final Integer MSG_VERSION = 2;
    private static final Integer TIME_OUT = 60000;
    private static final String YueYue = "YueYue";
    private static final String ERROR_CODE = HttpStatus.INTERNAL_SERVER_ERROR.value() + StringUtils.EMPTY;
    private static final Integer SESSION_KEY_LENGTH = 3;

    @Override
    public GetMsgRspDTO getMsg(GetMsgReqDTO req) {
        LoggerUtils.info(log, "获取司机消息，req：{}", FastJsonUtils.toJSONString(req));
        ContentDTO content = FastJsonUtils.fromJSONString(req.getContent(), ContentDTO.class);

        try {
            OrderRelationResponse orderRelation = orderService.orderRelationInfo(Strings.EMPTY, content.getPassengerOrderGuid());
            if (Objects.isNull(orderRelation)) {
                throw new ValidateException(BIZ_ERROR_FACTORY.supOrderQueryTcOrderError(content.getPassengerOrderGuid()));
            }

            //供应商code重新赋值
            req.setSupplierCode(orderRelation.getSupplierCode());

            //订单详情
            OrderDetailDTO orderDetail = orderService.getOrderDetail(orderRelation.getOrderSerialNo());
            if (Objects.isNull(orderDetail)) {
                throw new ValidateException(BIZ_ERROR_FACTORY.orderNotFoundError(orderRelation.getOrderSerialNo()));
            }

            MsgContentDTO msgContent = MsgContentDTO.build(Integer.valueOf(req.getTcMsgType()), content);
            String sessionKey = ImUtils.getSessionKey(orderDetail, req.getSupplierCode(), content.getFromUserGuid());

            //投递消息MQ
            dispatchMsg(req,orderDetail,msgContent);

            //重复消息校验查询
            SfcImMsgRecord sfcImMsgRecord = sfcImMsgRecordDao.queryRepeatMsg(sessionKey, content.getMsgId(), MsgSenderEnum.DRIVER.getCode(), MsgSendStatusEnum.SUCCESS.getCode());
            if (Objects.nonNull(sfcImMsgRecord)) {
                //约约消息默认请求两次 第二次返回对方正常
                if (req.getSupplierCode().contains(YueYue)) {
                    return GetMsgRspDTO.success(req.getTraceId(), content.getMsgId());
                }
                throw new ValidateException(BIZ_ERROR_FACTORY.duplicateInformation(content.getMsgId()));
            }

            //组装司机消息推送入参
            DriverMsgPushReqDTO driverMsgPushReq = buildDriverMsgPushReq(req, msgContent, orderDetail, orderRelation);

            //消息风控
            checkRisk(msgContent, content, req, orderDetail, orderRelation, driverMsgPushReq);

            //分销消息推送
            if (orderDetail.getPlatId() == OrderChannelEnum.DISTRIBUTION.getCode()) {
                if (!Objects.equals(MsgTypeEnum.READ_ACK.getMsgType(), driverMsgPushReq.getMsgType()) &&
                        !Objects.equals(MsgTypeEnum.CUSTOM_TEXT.getMsgType(), driverMsgPushReq.getMsgType())) {
                    throw new ValidateException(BIZ_ERROR_FACTORY.unknownMsgTypeError(driverMsgPushReq.getMsgType()));
                }
                return sendDistributionMsg(req, orderDetail, driverMsgPushReq, content, msgContent);
            }

            //司机消息推送
            driverMsgPush(driverMsgPushReq,orderDetail);

            //消息及会话存储更新
            saveMsg(req, driverMsgPushReq, msgContent, orderDetail);

            //用户通知
            sendNotice(orderDetail, driverMsgPushReq);

            return GetMsgRspDTO.success(req.getTraceId(), content.getMsgId());
        } catch (ValidateException validateException) {
            return GetMsgRspDTO.fail(req.getTraceId(), validateException.getErrorCode(), validateException.getError().getMessage(), content.getMsgId());
        } catch (Exception e) {
            LoggerUtils.error(log, "司机消息接收异常 req：{}", FastJsonUtils.toJSONString(req), e);
            return GetMsgRspDTO.fail(req.getTraceId(), ERROR_CODE, "司机消息接收异常", content.getMsgId());
        }
    }

    private GetMsgRspDTO sendDistributionMsg(GetMsgReqDTO req, OrderDetailDTO orderDetail, DriverMsgPushReqDTO driverMsgPushReq, ContentDTO content, MsgContentDTO msgContent) throws ValidateException, IntegrationException {
        //分销渠道消息推送
        IMNotifyCallBackReq imNotifyCallBackReq = new IMNotifyCallBackReq();
        driverMsgPushReq.setSessionKey(orderDetail.getSupplierCode()+"_"+orderDetail.getOrderId()+"_"+content.getFromUserGuid());
        List<String> msgIdList = new ArrayList<>();

        //已读消息处理
        if (Objects.equals(MsgTypeEnum.READ_ACK.getMsgType(), driverMsgPushReq.getMsgType())) {
            //读取乘客未读消息
            ArrayList<String> sessionKeys = new ArrayList<>();
            sessionKeys.add(driverMsgPushReq.getSessionKey());
            List<SfcImMsgRecord> sfcImMsgRecordList = sfcImMsgRecordDao.queryUnreadMsg(sessionKeys, MsgSenderEnum.PASSENGER.getCode());
            sfcImMsgRecordList.forEach(e -> msgIdList.add(e.getMsgId()));
            LoggerUtils.info(log, "获取消息消息id列表，msgIdList：{}", FastJsonUtils.toJSONString(msgIdList));
            imNotifyCallBackReq.setMsgIdList(msgIdList);
            imNotifyCallBackReq.setMsgType(MsgTypeEnum.DISTRIBUTE_READ_ACK.getMsgType());
        } else if (Objects.equals(MsgTypeEnum.CUSTOM_TEXT.getMsgType(), driverMsgPushReq.getMsgType())) {
            msgIdList.add(IdUtil.getSnowflake(redisClientProxy.incrby(IM_DISTRIBUTION_MSG_KEY, 1) % 31).nextIdStr());
            imNotifyCallBackReq.setMsgIdList(msgIdList);
            imNotifyCallBackReq.setMsgType(MsgTypeEnum.DISTRIBUTE_CUSTOM_TEXT.getMsgType());
            ContentDTO contentDto = FastJsonUtils.fromJSONString(req.getContent(), ContentDTO.class);
            LocDataDTO locDataDTO = FastJsonUtils.fromJSONString(contentDto.getData(), LocDataDTO.class);
            imNotifyCallBackReq.setMsgContent(locDataDTO.getContent());
        }

        imNotifyCallBackReq.setTcSerialNo(orderDetail.getOrderId());
        imNotifyCallBackReq.setTraceId(req.getTraceId());
        if (CollectionUtils.isEmpty(msgIdList)){
            throw new ValidateException(BIZ_ERROR_FACTORY.msgReceiveHandleError(driverMsgPushReq.getMsgId()));
        }

        IMNotifyCallBackRsp imSendMessageResponse = distributedClient.imNotifyCallBack(imNotifyCallBackReq);

        //已读回执
        SfcImMsgRecord update = new SfcImMsgRecord();
        update.setUpdateTime(new Date());
        update.setMsgStatus(MsgStatusEnum.READ.getCode());
        sfcImMsgRecordDao.update(update, new LambdaQueryWrapper<SfcImMsgRecord>()
                .eq(SfcImMsgRecord::getDriverId, driverMsgPushReq.getDriverId())
                .eq(SfcImMsgRecord::getOrderId, driverMsgPushReq.getOrderId())
                .in(SfcImMsgRecord::getMsgId, msgIdList)
                .eq(SfcImMsgRecord::getMsgStatus, MsgStatusEnum.UNREAD.getCode())
                .eq(SfcImMsgRecord::getMsgSender, MsgSenderEnum.PASSENGER.getCode()));

        if (!imSendMessageResponse.isSuccess()) {
            throw new ValidateException(BIZ_ERROR_FACTORY.msgReceiveHandleError(driverMsgPushReq.getMsgId()));
        }
        //消息及会话存储更新
        saveMsg(req, driverMsgPushReq, msgContent, orderDetail);
        return GetMsgRspDTO.success(req.getTraceId(), content.getMsgId());
    }

    private void dispatchMsg(GetMsgReqDTO req, OrderDetailDTO orderDetail,MsgContentDTO msgContent ) {
        // 发送消息到消息分发MQ
        if (Objects.equals(MsgTypeEnum.READ_ACK.getMsgType(), Integer.valueOf(req.getTcMsgType()))){
            return;
        }
        // 发送消息到消息分发MQ
        CompletableFuture.runAsync(() -> {
            try {
                ChatMessageDispatchDTO dispatchMsg = new ChatMessageDispatchDTO();
                dispatchMsg.setOrderId(orderDetail.getOrderId());
                dispatchMsg.setMsgId(msgContent.getMsgId());
                dispatchMsg.setSupplierCode(req.getSupplierCode());
                dispatchMsg.setStatus(MsgDispatchStatusEnum.RECEIVE.getCode());
                dispatchMsg.setLatestTime(DateUtil.date2String(new Date()));
                ArrayList<ChatMessageDispatchDTO> maps = new ArrayList<>();
                maps.add(dispatchMsg);
                LoggerUtils.info(log, "发送消息分发MQ：{}", FastJsonUtils.toJSONString(maps));
                defaultProducer.send(dispatchTopic, ChatMsgTagEnum.im_chat_message_dispatch.getName(),
                        maps, Long.valueOf(ImConfigCenter.getConfigValue(ConfigCenterKeyEnum.im_dispatch_send_msg_timeout)), 0, new HashMap<>(), SerializeEnum.FASTJSON);
            } catch (Exception e) {
                LoggerUtils.error(log, "发送消息分发MQ异常: {}", orderDetail.getOrderId(), e);
                throw new CompletionException(e);
            }
        },  asyncConfig.mqSendExecutor());


    }

    private void saveMsg(GetMsgReqDTO req, DriverMsgPushReqDTO driverMsgPushReq, MsgContentDTO msgContent, OrderDetailDTO orderDetail) {
        //消息记录
        SfcImMsgRecord msgRecord = insertMsgRecord(req, driverMsgPushReq, orderDetail);

        //会话信息
        insertOrUpdateSfcImInfo(req, driverMsgPushReq, msgRecord, msgContent,orderDetail);
    }


    public void checkRisk(MsgContentDTO msgContent,
                          ContentDTO contentDTO,
                          GetMsgReqDTO req,
                          OrderDetailDTO orderDetail,
                          OrderRelationResponse orderRelation,
                          DriverMsgPushReqDTO driverMsgPushReq) throws ValidateException {
        String content = msgContent.getContent();
        if (!Objects.equals(MsgTypeEnum.READ_ACK.getMsgType(), Integer.valueOf(req.getTcMsgType())) && StringUtils.isNotBlank(content)) {
            //风控校验
            RiskQueryRspDTO riskQueryRsp = msgRisk(orderDetail, content);
            if (riskApi.riskJudge(riskQueryRsp)) {
                //安全提醒风控判断
                boolean warn = safeWarn(orderRelation, contentDTO, driverMsgPushReq, riskQueryRsp);
                //敏感词脱敏
                desensitization(riskQueryRsp, req, driverMsgPushReq);
                if (!warn) {
                    throw new ValidateException(BIZ_ERROR_FACTORY.msgSensitiveWordsError(contentDTO.getMsgId()));
                }
            }
        }
    }

    private DriverMsgPushReqDTO buildDriverMsgPushReq(GetMsgReqDTO req, MsgContentDTO msgContent, OrderDetailDTO orderDetail, OrderRelationResponse orderRelation) {
        ContentDTO contentDto = FastJsonUtils.fromJSONString(req.getContent(), ContentDTO.class);
        DriverMsgPushReqDTO driverMsgPushReq = new DriverMsgPushReqDTO();
        driverMsgPushReq.setMsgId(contentDto.getMsgId());
        driverMsgPushReq.setMsgContent(msgContent);
        driverMsgPushReq.setPlateNumber(orderRelation.getPlateNumber());
        driverMsgPushReq.setSupplierCode(req.getSupplierCode());
        driverMsgPushReq.setDriverId(contentDto.getFromUserGuid());
        driverMsgPushReq.setOrderId(orderRelation.getOrderSerialNo());
        driverMsgPushReq.setSupplierOrderId(contentDto.getPassengerOrderGuid());
        driverMsgPushReq.setUnionId(orderDetail.getUnionId());
        driverMsgPushReq.setOpenId(orderDetail.getOpenId());
        driverMsgPushReq.setMemberId(String.valueOf(orderDetail.getMemberId()));
        driverMsgPushReq.setImKey(driverMsgPushReq.filter1());
        driverMsgPushReq.setMsgType(Integer.valueOf(req.getTcMsgType()));
        driverMsgPushReq.setSessionKey(ImUtils.getSessionKey(orderDetail, req.getSupplierCode(), contentDto.getFromUserGuid()));
        return driverMsgPushReq;
    }

    private RiskQueryRspDTO msgRisk(OrderDetailDTO orderDetail, String content) {
        RiskQueryReqDTO riskQueryReqDTO = new RiskQueryReqDTO();
        riskQueryReqDTO.setMemberId(orderDetail.getMemberId());
        riskQueryReqDTO.setUnionId(orderDetail.getUnionId());
        riskQueryReqDTO.setDriverCardNo(orderDetail.getPlateNumber());
        riskQueryReqDTO.setOrderId(orderDetail.getOrderId());
        riskQueryReqDTO.setMainScene(8);
        riskQueryReqDTO.setChildScene(1);
        riskQueryReqDTO.setText(content);
        return riskApi.riskQuery(riskQueryReqDTO);
    }

    private void insertOrUpdateSfcImInfo(GetMsgReqDTO req, DriverMsgPushReqDTO driverMsgPushReq, SfcImMsgRecord msgRecord, MsgContentDTO msgContent,OrderDetailDTO orderDetail) {
        String sessionKey = driverMsgPushReq.getSessionKey();
        Integer msgType = driverMsgPushReq.getMsgType();
        LogContextUtils.setCategory("insertOrUpdateSfcImInfo");
        //会话信息
        SfcImInfo sfcImInfo = getSfcImInfo(sessionKey,orderDetail.getPlatId());
        if (Objects.nonNull(sfcImInfo)) {
            //非已读消息处理
            if (!Objects.equals(MsgTypeEnum.READ_ACK.getMsgType(), msgType)) {
                sfcImInfo.setLatestMsgTime(Objects.isNull(msgRecord.getMsgSendTime()) ? new Date() : msgRecord.getMsgSendTime());
                sfcImInfo.setLatestMsgRecordId(msgRecord.getId());
                sfcImInfo.setLatestMsgType(msgRecord.getMsgType());
                ImUtils.setLatestMsgDesc(sfcImInfo, msgType, msgContent.getContent(), msgRecord.getMsgSender());

                //读取乘客未读消息
                ArrayList<String> sessionKeys = new ArrayList<>();
                sessionKeys.add(driverMsgPushReq.getSessionKey());
                String newSessionKey = SessionKeyUtil.getNewSessionKey(driverMsgPushReq.getSessionKey());
                if (StringUtils.isNotEmpty(newSessionKey)) {
                    sessionKeys.add(newSessionKey);
                }
                sfcImInfo.setUnreadMsgNum( sfcImMsgRecordDao.queryUnreadMsg(sessionKeys, MsgSenderEnum.DRIVER.getCode()).size());
                extractedOrderId(driverMsgPushReq.getOrderId(), sfcImInfo);
                sfcImInfo.setOrderId(driverMsgPushReq.getOrderId());
                sfcImInfo.setSupplierOrderId(driverMsgPushReq.getSupplierOrderId());
            }

            sfcImInfo.setDeleted(0);
            if (StringUtils.isNotEmpty(msgRecord.getDriverNickName())) {
                sfcImInfo.setDriverNickName(msgRecord.getDriverNickName());
            }
            sfcImInfo.setUpdateTime(new Date());
            boolean b = sfcImInfoDao.updateById(sfcImInfo);
            LoggerUtils.info(log, "update sfcImInfoMapper：{},result:{}", FastJsonUtils.toJSONString(sfcImInfo),b);
            redisClientProxy.remove(RedisKeyBuilder.IM_INFO_KEY + sessionKey);

            if(OrderChannelEnum.isXcx(orderDetail.getPlatId()) || OrderChannelEnum.isTCAppWithoutMada(orderDetail.getPlatId())){
                String otherSessionKey = null;
                String userId = null;
                String oldSessionKey = sfcImInfo.getSessionKey();
                if (StringUtils.isBlank(sfcImInfo.getUnionId()) && Objects.nonNull(sfcImInfo.getMemberId())) {
                    userId = getUnionIdByMemberId(sfcImInfo.getMemberId().toString());
                } else {
                    Long memberIdByUnionId = getMemberIdByUnionId(sfcImInfo.getUnionId());
                    userId = Objects.nonNull(memberIdByUnionId) ? memberIdByUnionId.toString() : null;
                }
                otherSessionKey = replaceSessionKey(oldSessionKey, userId);
                SfcImInfo otherSession = sfcImInfoDao.findBySessionKey(otherSessionKey, null);
               if (Objects.nonNull(otherSession)){
                   otherSession.setLatestMsgDesc(sfcImInfo.getLatestMsgDesc());
                   otherSession.setLatestMsgRecordId(msgRecord.getId());
                   otherSession.setLatestMsgType(sfcImInfo.getLatestMsgType());
                   otherSession.setLatestMsgDesc(sfcImInfo.getLatestMsgDesc());
                   otherSession.setUnreadMsgNum(sfcImInfo.getUnreadMsgNum());
                   otherSession.setAllOrderIds(sfcImInfo.getAllOrderIds());
                   otherSession.setOrderId(sfcImInfo.getOrderId());
                   otherSession.setSupplierOrderId(sfcImInfo.getSupplierOrderId());
                   otherSession.setDriverNickName(sfcImInfo.getDriverNickName());
                   otherSession.setUpdateTime(sfcImInfo.getUpdateTime());
                   sfcImInfoDao.updateById(otherSession);
                   redisClientProxy.remove(RedisKeyBuilder.IM_INFO_KEY + otherSessionKey);
               }
            }
            return;
        }

        //非已读消息处理
        if (!Objects.equals(MsgTypeEnum.READ_ACK.getMsgType(), msgType)) {
            sfcImInfo = new SfcImInfo();
            sfcImInfo.setOrderId(msgRecord.getOrderId());
            sfcImInfo.setAllOrderIds(msgRecord.getOrderId());
            sfcImInfo.setSupplierOrderId(msgRecord.getSupplierOrderId());
            sfcImInfo.setLatestMsgTime(Objects.isNull(msgRecord.getMsgSendTime()) ? new Date() : msgRecord.getMsgSendTime());
            sfcImInfo.setSessionKey(msgRecord.getSessionKey());
            sfcImInfo.setUnionId(msgRecord.getUnionId());
            sfcImInfo.setMemberId(msgRecord.getMemberId());
            sfcImInfo.setDriverId(msgRecord.getDriverId());
            if (StringUtils.isNotEmpty(msgRecord.getDriverNickName())){
                sfcImInfo.setDriverNickName(msgRecord.getDriverNickName());
            }
            sfcImInfo.setPlateNumber(msgRecord.getPlateNumber());
            sfcImInfo.setSupplierCode(msgRecord.getSupplierCode());
            sfcImInfo.setSubSupplierCode(msgRecord.getSupplierCode());
            sfcImInfo.setUnreadMsgNum(0);
            sfcImInfo.setLatestMsgRecordId(msgRecord.getId());
            sfcImInfo.setLatestMsgType(msgRecord.getMsgType());
            sfcImInfo.setTcSessionId(req.getTcConversationId());
            sfcImInfo.setPlateId(orderDetail.getPlatId());
            ImUtils.setLatestMsgDesc(sfcImInfo, msgType, msgContent.getContent(), msgRecord.getMsgSender());
            sfcImInfo.setUpdateTime(new Date());
            LoggerUtils.info(log, "insert sfcImInfoMapper：{}", FastJsonUtils.toJSONString(sfcImInfo));
            sfcImInfoDao.save(sfcImInfo);
            redisClientProxy.setnx(RedisKeyBuilder.IM_INFO_KEY + sfcImInfo.getSessionKey(), FastJsonUtils.toJSONString(sfcImInfo), 7200);
        }
    }

    private static void extractedOrderId(String orderId, SfcImInfo sfcImInfo) {
        StringBuilder orderIdsBuilder = new StringBuilder();
        if (StringUtils.isNotEmpty(sfcImInfo.getAllOrderIds())) {
            if (!sfcImInfo.getAllOrderIds().contains(orderId)) {
                String[] allOrderIdsArray = sfcImInfo.getAllOrderIds().split(",");
                if (allOrderIdsArray.length > ImConfigCenter.getHistoryOrderCount()) {
                    // 如果长度超过，移除第一个ID
                    sfcImInfo.setAllOrderIds(String.join(",", Arrays.copyOfRange(allOrderIdsArray, 1, allOrderIdsArray.length)));
                }
                // 重新拼接已有的订单ID
                orderIdsBuilder.append(sfcImInfo.getAllOrderIds()).append(",").append(orderId);
            } else {
                // 如果订单ID已存在，直接使用当前的订单ID
                orderIdsBuilder.append(sfcImInfo.getAllOrderIds());
            }
        } else {
            // 添加新的订单ID
            orderIdsBuilder.append(orderId);
        }
        sfcImInfo.setAllOrderIds(orderIdsBuilder.toString());
    }

    private SfcImInfo getSfcImInfo(String sessionKey,Integer platId) {
        String key = (Objects.nonNull(platId) &&  OrderChannelEnum.isMaDa(platId)) ? ":MaDa" : "";
        String imInfoStr = redisClientProxy.getString(RedisKeyBuilder.IM_INFO_KEY + sessionKey + key);
        LoggerUtils.info(log, "[getSfcImInfo] 获取消息，redis获取sfcImInfo：{}", imInfoStr);
        SfcImInfo sfcImInfo = StringUtils.isNotBlank(imInfoStr) ? FastJsonUtils.fromJSONString(imInfoStr, SfcImInfo.class) : null;

        if (Objects.isNull(sfcImInfo)) {
            sfcImInfo = sfcImInfoDao.findBySessionKey(sessionKey,platId);
            redisClientProxy.setnx(RedisKeyBuilder.IM_INFO_KEY + sessionKey + key, FastJsonUtils.toJSONString(sfcImInfo), 7200);
            LoggerUtils.info(log, "[getSfcImInfo] 获取消息，db获取sfcImInfo：{}", FastJsonUtils.toJSONString(sfcImInfo));
        }
        return sfcImInfo;
    }

    private SfcImMsgRecord insertMsgRecord(GetMsgReqDTO req, DriverMsgPushReqDTO driverMsgPushReq, OrderDetailDTO orderDetail) {
        LogContextUtils.setCategory("insertMsgRecord");
        SfcImMsgRecord msgRecord = new SfcImMsgRecord();
        if (Objects.equals(MsgTypeEnum.READ_ACK.getMsgType(), driverMsgPushReq.getMsgType())) {
            return msgRecord;
        }
        if (StringUtils.isNotEmpty(driverMsgPushReq.getPlateNumber())){
            msgRecord.setPlateNumber(driverMsgPushReq.getPlateNumber());
        }else {
            msgRecord.setPlateNumber(orderDetail.getPlateNumber());
        }
        msgRecord.setSupplierCode(driverMsgPushReq.getSupplierCode());
        msgRecord.setUnionId(driverMsgPushReq.getUnionId());
        msgRecord.setMemberId(Long.parseLong(driverMsgPushReq.getMemberId()));
        msgRecord.setSubSupplierCode(driverMsgPushReq.getSupplierCode());
        msgRecord.setSessionKey(driverMsgPushReq.getSessionKey());
        msgRecord.setDriverNickName(orderDetail.getDriverName());
        msgRecord.setDriverId(driverMsgPushReq.getDriverId());
        msgRecord.setOrderId(orderDetail.getOrderId());
        msgRecord.setSupplierOrderId(driverMsgPushReq.getSupplierOrderId());
        msgRecord.setMsgId(driverMsgPushReq.getMsgId());
        msgRecord.setMsgContent(req.getContent());
        msgRecord.setMsgType(driverMsgPushReq.getMsgType());
        msgRecord.setMsgSender(MsgSenderEnum.DRIVER.getCode());
        msgRecord.setMsgStatus(MsgStatusEnum.UNREAD.getCode());
        msgRecord.setMsgSendStatus(MsgSendStatusEnum.SUCCESS.getCode());
        msgRecord.setMsgVersion(MSG_VERSION);
        msgRecord.setDriverGender(null);
        msgRecord.setUpdateTime(new Date());
        msgRecord.setOrderStatus(orderDetail.getStatus());
        sfcImMsgRecordDao.save(msgRecord);
        LoggerUtils.info(log, "保存im消息，司机发送乘客消息 SfcImMsgRecord：{}", FastJsonUtils.toJSONString(msgRecord));
        return msgRecord;
    }

    private void sendNotice(OrderDetailDTO orderDetail, DriverMsgPushReqDTO driverMsgPushReq) {
        String orderId = orderDetail.getOrderId();
        LogContextUtils.setCategory("sendNotice");
        LogContextUtils.setFilter1(orderId);
        if (!StringUtils.equalsIgnoreCase(OrderType.SFC.getOrderPrefix(), orderId.substring(0, 3))) {
            return;
        }

        String num = redisClientProxy.getString(RedisKeyBuilder.IM_SMS_NUM_KEY + orderId);
        Integer imSmsNum = StringUtils.isEmpty(num) ? 0 : Integer.valueOf(num);

        //匹配平台短信限制配置
        SmsRuleConfigDTO smsRuleConfigDTO = ImConfigCenter.matchIMSmsRuleConfigByPlatId(orderDetail.getPlatId());
        LoggerUtils.info(log, "短信限制配置：{}", FastJsonUtils.toJSONString(smsRuleConfigDTO));

        try {
            //车主首条消息通知
            if (Objects.isNull(imSmsNum) || imSmsNum < smsRuleConfigDTO.getSentLimitCount()) {
                PassengerUnreadSmsDTO passengerUnreadSmsDTO = new PassengerUnreadSmsDTO();
                BeanUtils.copyProperties(orderDetail, passengerUnreadSmsDTO);
                passengerUnreadSmsDTO.setMsgId(driverMsgPushReq.getMsgId());
                LoggerUtils.info(log, "获取消息第一条车主消息发MQ：{}", FastJsonUtils.toJSONString(passengerUnreadSmsDTO));
                defaultProducer.send(topic, ChatMsgTagEnum.im_passenger_unread_sms.getName(), passengerUnreadSmsDTO, TIME_OUT,
                        smsRuleConfigDTO.getDelayLevel(), new HashMap<>(), SerializeEnum.FASTJSON);
            }

            //app推送
            if (OrderChannelEnum.isApp(orderDetail.getChannelType()) && Objects.equals(ImConfigCenter.getAppPushConfig(), 1)) {
                PassengerUnreadSmsDTO passengerUnreadSmsDTO = new PassengerUnreadSmsDTO();
                BeanUtils.copyProperties(orderDetail, passengerUnreadSmsDTO);
                passengerUnreadSmsDTO.setMsgId(driverMsgPushReq.getMsgId());
                LoggerUtils.info(log, "车主消息推送发MQ：{}", FastJsonUtils.toJSONString(passengerUnreadSmsDTO));
                defaultProducer.send(topic, ChatMsgTagEnum.im_passenger_unread_app.getName(), passengerUnreadSmsDTO, TIME_OUT,
                        RocketDelayLevel.MINUTE_1, new HashMap<>(), SerializeEnum.FASTJSON);
            }
        } catch (Exception e) {
            LoggerUtils.error(log, "车主消息发送MQ异常: {}", orderId, e);
        }
    }

    private Integer driverMsgPush(DriverMsgPushReqDTO driverMsgPushReq,OrderDetailDTO orderDetail) throws ValidateException {
        LogContextUtils.setCategory("driverMsgPush");
        //已读消息处理
        if (Objects.equals(MsgTypeEnum.READ_ACK.getMsgType(), driverMsgPushReq.getMsgType())) {
            List<String> msgIdList = new ArrayList<>();
            //读取乘客未读消息
            ArrayList<String> sessionKeys = new ArrayList<>();
            sessionKeys.add(driverMsgPushReq.getSessionKey());
            String newSessionKey = SessionKeyUtil.getNewSessionKey(driverMsgPushReq.getSessionKey());
            if (StringUtils.isNotEmpty(newSessionKey)) {
                sessionKeys.add(newSessionKey);
            }
            List<SfcImMsgRecord> sfcImMsgRecordList =  sfcImMsgRecordDao.queryUnreadMsg(sessionKeys, MsgSenderEnum.PASSENGER.getCode());
            sfcImMsgRecordList.forEach(e -> msgIdList.add(e.getMsgId()));
            LoggerUtils.info(log, "获取消息消息id列表，msgIdList：{}", FastJsonUtils.toJSONString(msgIdList));
            driverMsgPushReq.setMsgIds(msgIdList);
        }

        // 根据会话信息推送司机消息
        Integer msgSendStatus = pushToMultipleSessions(driverMsgPushReq,orderDetail);

        //已读回执
        SfcImMsgRecord update = new SfcImMsgRecord();
        update.setUpdateTime(new Date());
        update.setMsgStatus(MsgStatusEnum.READ.getCode());
        sfcImMsgRecordDao.update(update, new LambdaQueryWrapper<SfcImMsgRecord>()
                .eq(SfcImMsgRecord::getDriverId, driverMsgPushReq.getDriverId())
                .eq(SfcImMsgRecord::getOrderId, driverMsgPushReq.getOrderId())
                .eq(SfcImMsgRecord::getMsgStatus, MsgStatusEnum.UNREAD.getCode())
                .eq(SfcImMsgRecord::getMsgSender, MsgSenderEnum.PASSENGER.getCode()));

        if (Objects.equals(msgSendStatus, MsgSendStatusEnum.FAIL.getCode())) {
            throw new ValidateException(BIZ_ERROR_FACTORY.msgReceiveHandleError(driverMsgPushReq.getMsgId()));
        }

        return msgSendStatus;
    }

    /**
     * 向多个会话推送消息
     */
    private Integer pushToMultipleSessions(DriverMsgPushReqDTO driverMsgPushReq,OrderDetailDTO orderDetail) {
        Integer msgSendStatus = MsgSendStatusEnum.FAIL.getCode();
        boolean hasSuccessPush = false;

            // 推送消息到当前会话
            try {
                Simple<?> currentSimple = pushApi.driverMsgPush(driverMsgPushReq);
                if (Objects.nonNull(currentSimple) && Objects.equals(currentSimple.getStatus(), Simple.OK.getStatus())) {
                    hasSuccessPush = true;
                    LoggerUtils.info(log, "[pushToMultipleSessions] 当前会话推送成功，sessionKey：{}，msgId：{}",
                            driverMsgPushReq.getSessionKey(), driverMsgPushReq.getMsgId());
                } else {
                    LoggerUtils.warn(log, "[pushToMultipleSessions] 当前会话推送失败，sessionKey：{}，msgId：{}，响应：{}",
                            driverMsgPushReq.getSessionKey(), driverMsgPushReq.getMsgId(), FastJsonUtils.toJSONString(currentSimple));
                }
            } catch (Exception e) {
                LoggerUtils.error(log, "[pushToMultipleSessions] 当前会话推送异常，sessionKey：{}，msgId：{}",
                        driverMsgPushReq.getSessionKey(), driverMsgPushReq.getMsgId(), e);
            }

        if(OrderChannelEnum.isXcx(orderDetail.getPlatId()) || OrderChannelEnum.isTCAppWithoutMada(orderDetail.getPlatId())){
            String otherSessionKey = null;
            String userId = null;
            String oldSessionKey = driverMsgPushReq.getSessionKey();
            String oldUserId = splitOldSessionKey4UserId(oldSessionKey);
            if(OrderChannelEnum.isTCAppWithoutMada(orderDetail.getPlatId())){
                userId = getUnionIdByMemberId(oldUserId);
            }else if(StringUtils.isNotBlank(oldUserId)){
                Long memberIdByUnionId = getMemberIdByUnionId(oldUserId);
                userId  = Objects.nonNull(memberIdByUnionId)? memberIdByUnionId.toString():null;
            }
            otherSessionKey = replaceSessionKey(oldSessionKey, userId);

            // 查询替代会话信息
            SfcImInfo otherSession = sfcImInfoDao.findBySessionKey(otherSessionKey, null);
            if (Objects.nonNull(otherSession)) {
                LoggerUtils.warn(log, "[pushToMultipleSessions] 找到替代会话信息，sessionKey：{}", driverMsgPushReq.getSessionKey());
                // 构建替代会话的推送请求
                DriverMsgPushReqDTO alternatePushReq = buildAlternateSessionPushRequest(driverMsgPushReq, otherSession,OrderChannelEnum.isTCAppWithoutMada(orderDetail.getPlatId()));

                Simple<?> alternateSimple = pushApi.driverMsgPush(alternatePushReq);
                if (Objects.nonNull(alternateSimple) && Objects.equals(alternateSimple.getStatus(), Simple.OK.getStatus())) {
                    hasSuccessPush = true;
                    LoggerUtils.info(log, "[pushToMultipleSessions] 替代会话推送成功，sessionKey：{}，msgId：{}",
                            otherSession.getSessionKey(), driverMsgPushReq.getMsgId());
                } else {
                    LoggerUtils.warn(log, "[pushToMultipleSessions] 替代会话推送失败，sessionKey：{}，msgId：{}，响应：{}",
                            otherSession.getSessionKey(), driverMsgPushReq.getMsgId(), FastJsonUtils.toJSONString(alternateSimple));
                }
            }
        }

        // 如果至少有一个会话推送成功，则认为是成功的
        if (hasSuccessPush) {
            msgSendStatus = MsgSendStatusEnum.SUCCESS.getCode();
        }

        return msgSendStatus;
    }

    private String getUnionIdByMemberId(String memberId) {

        ThirdPartyConfigDTO thirdPartyConfigDTO = ImConfigCenter.getThirdPartyConfig();
        ThirdPartyWechatBaseResponse response = null;
        if (StringUtils.isNotBlank(memberId)) {
            //如果是APP端，memberId是数字
            response = getMemberIdListByMemberId(memberId, thirdPartyConfigDTO);
        }
        if(Objects.nonNull(response)&& org.apache.commons.collections4.CollectionUtils.isNotEmpty(response.getData())){
            return response.getData().stream().filter(i -> Objects.equals(i.getMemberSystem(), 33))
                    .map(MemberInfoDTO::getUnionId).findFirst().orElse(null);
        }
        return null;
    }

    private String replaceSessionKey(String sessionKey, String userId) {
        if(StringUtils.isBlank(sessionKey)|| StringUtils.isBlank(userId)){
            return null;
        }
        // 会话标识（供应商Code/子供应商Code + 会员ID/unionId + 司机ID）  例子CaoCao_1234567890_1234567890
        // 将中间的换成userId
        String[] sessionKeyParts = sessionKey.split("_");
        if (sessionKeyParts.length < SESSION_KEY_LENGTH) {
            LoggerUtils.warn(log, "会话标识格式不正确，无法替换 sessionKey: {}", sessionKey);
            return null;
        }
        if(sessionKeyParts.length == SESSION_KEY_LENGTH){
            return sessionKeyParts[0] + "_" + userId + "_" + sessionKeyParts[2];
        }

        LoggerUtils.warn(log, "会话标识分隔后超过3位  sessionKey: {}", sessionKey);
        return sessionKeyParts[0] + "_" + userId + "_" + sessionKeyParts[SESSION_KEY_LENGTH - 1];

    }


    private Long getMemberIdByUnionId(String unionId) {
        ThirdPartyConfigDTO thirdPartyConfigDTO = ImConfigCenter.getThirdPartyConfig();
        ThirdPartyWechatBaseResponse response = null;
        if (StringUtils.isNotBlank(unionId)) {
            response = getMemberIdListByUnionId(unionId, thirdPartyConfigDTO);
        }
        if(Objects.nonNull(response)&& org.apache.commons.collections4.CollectionUtils.isNotEmpty(response.getData())){
            return response.getData().stream().filter(i -> Objects.equals(i.getMemberSystem(), 0))
                    .map(MemberInfoDTO::getMemberId).findFirst().orElse(null);
        }
        return null;
    }

    private static String splitOldSessionKey4UserId(String oldSessionKey) {
        String[] splits = oldSessionKey.split("_");
        if(splits.length == SESSION_KEY_LENGTH){
            return   splits[1];
        }else if(splits.length > SESSION_KEY_LENGTH){
            // XXXX_XXXX_XXXX_XXXX_XXX  中间的都认为是用户ID
            return String.join("_", Arrays.copyOfRange(splits, 1, splits.length - 1));
        }
        return null;
    }

    /**
     * 构建替代会话的推送请求
     */
    private DriverMsgPushReqDTO buildAlternateSessionPushRequest(DriverMsgPushReqDTO originalReq, SfcImInfo alternateSession,boolean isApp) {
        DriverMsgPushReqDTO alternatePushReq = new DriverMsgPushReqDTO();
        BeanUtils.copyProperties(originalReq, alternatePushReq);
        
        // 使用替代会话的信息
        alternatePushReq.setSessionKey(alternateSession.getSessionKey());
        alternatePushReq.setUnionId(alternateSession.getUnionId());
        alternatePushReq.setMemberId(String.valueOf(alternateSession.getMemberId()));
        alternatePushReq.setImKey(isApp?alternateSession.getUnionId():String.valueOf(alternateSession.getMemberId()));
        alternatePushReq.setPlateNumber(isApp?"App":"WX");
        return alternatePushReq;
    }

    private void desensitization(RiskQueryRspDTO riskQueryRsp, GetMsgReqDTO req, DriverMsgPushReqDTO driverMsgPushReq) {
        LogContextUtils.setCategory("desensitization");
        LoggerUtils.info(log, "敏感词脱敏请求参数：{}, 风控响应：{}", FastJsonUtils.toJSONString(req), FastJsonUtils.toJSONString(riskQueryRsp));
        try {
            //解析content
            String content = StringUtils.EMPTY;
            JSONObject jsonObject = JSONObject.parseObject(req.getContent());
            JSONObject data = null;
            if (!Objects.isNull(jsonObject) && !Objects.isNull(jsonObject.get("data"))) {
                data = JSONObject.parseObject(jsonObject.get("data").toString());
                Object contentObj = data.get("content");
                if (!Objects.isNull(contentObj)) {
                    content = String.valueOf(contentObj);
                }
            }

            Map<String, String> obj = riskQueryRsp.getData().getObj();
            if (CollectionUtils.isEmpty(obj)) {
                return;
            }

            for (String sensitiveWord : obj.values()) {
                String value = DesensitizationUtils.desensitization(content, sensitiveWord);
                if (!StringUtils.isEmpty(value)) {
                    content = value;
                }
            }

            LoggerUtils.info(log, "敏感词脱敏后：{}", content);

            //封装content
            if (!Objects.isNull(jsonObject) && !Objects.isNull(data)) {
                data.put("content", content);
                jsonObject.put("data", FastJsonUtils.toJSONString(data));
            }

            req.setContent(FastJsonUtils.toJSONString(jsonObject));
            driverMsgPushReq.getMsgContent().setContent(content);
            LoggerUtils.info(log, "敏感词脱敏后：{}, 司机消息：{}", FastJsonUtils.toJSONString(req), FastJsonUtils.toJSONString(driverMsgPushReq));
        } catch (Exception e) {
            LoggerUtils.error(log, "敏感词脱敏失败：{}", e.getMessage(), e);
        }
    }

    public boolean safeWarn(OrderRelationResponse orderRelation, ContentDTO content, DriverMsgPushReqDTO driverMsgPushReq, RiskQueryRspDTO riskQueryRsp) {
        LogContextUtils.setCategory("safeWarn");
        LogContextUtils.setSubCategory("driver");
        String orderId = orderRelation.getOrderSerialNo();
        if (Objects.isNull(riskQueryRsp)) {
            LoggerUtils.warn(log, "安全提醒请求风控异常|orderId：{}", orderId);
            return Boolean.FALSE;
        }

        RiskQueryDTO data = riskQueryRsp.getData();
        if (Objects.isNull(data)) {
            LoggerUtils.warn(log, "安全提醒无风控数据|data is null|orderId：{}", orderId);
            return Boolean.FALSE;
        }

        Map<String, String> obj = data.getObj();
        if (CollectionUtils.isEmpty(obj)) {
            LoggerUtils.warn(log, "安全提醒无风控规则|obj is null|orderId：{}", orderId);
            return Boolean.FALSE;
        }

        if (!obj.containsKey("aq006") && !obj.containsKey("aq007")) {
            log.warn("安全无风险|orderId:{}", orderId);
            return Boolean.FALSE;
        }

        SafeWarnMsgReqDTO req = buildSafeWarnMsgPushReq(orderRelation, content, driverMsgPushReq);

        try {
            // 调用接口，推送消息
            Simple<?> simple = pushApi.safeWarnMsgPush(req);
            if (Objects.nonNull(simple) && Objects.equals(simple.getStatus(), Simple.OK.getStatus())) {
                //保存系统信息
                SfcImMsgRecord sysMsg = buildSysMsg(req);
                sysMsg.setDriverNickName(content.getDriverNickname());
                sfcImMsgRecordDao.save(sysMsg);
            }
            return Boolean.TRUE;
        } catch (Exception e) {
            LoggerUtils.warn(log, "安全消息推送失败：{}",  e.getMessage(), e);
        }
        return Boolean.FALSE;
    }

    private SfcImMsgRecord buildSysMsg(SafeWarnMsgReqDTO req) {
        SfcImMsgRecord sfcImMsgRecord = new SfcImMsgRecord();
        sfcImMsgRecord.setSessionKey(ImUtils.buildSessionKey(req.getSupplierCode(), req.getImKey(), req.getDriverId()));
        sfcImMsgRecord.setMsgId(UUID.randomUUID().toString().replace("-", StringUtils.EMPTY));
        sfcImMsgRecord.setMsgSender(MsgSenderEnum.SYS.getCode());
        sfcImMsgRecord.setUnionId(req.getUnionId());
        sfcImMsgRecord.setMemberId(Long.valueOf(req.getMemberId()));
        sfcImMsgRecord.setPlateNumber(req.getPlateNumber());
        sfcImMsgRecord.setSupplierCode(req.getSupplierCode());
        sfcImMsgRecord.setSubSupplierCode(req.getSupplierCode());
        sfcImMsgRecord.setDriverId(req.getDriverId());
        sfcImMsgRecord.setOrderId(req.getOrderId());
        sfcImMsgRecord.setSupplierOrderId(req.getSupplierOrderId());
        sfcImMsgRecord.setMsgType(MsgTypeEnum.SAFE_WARN_TEXT.getMsgType());
        sfcImMsgRecord.setMsgStatus(MsgStatusEnum.READ.getCode());
        sfcImMsgRecord.setMsgContent(req.getMsgContent().getContent());
        sfcImMsgRecord.setMsgSendStatus(MsgSendStatusEnum.SUCCESS.getCode());
        sfcImMsgRecord.setUpdateTime(new Date());
        sfcImMsgRecord.setMsgVersion(MSG_VERSION);
        return sfcImMsgRecord;
    }

    private SafeWarnMsgReqDTO buildSafeWarnMsgPushReq(OrderRelationResponse orderRelation, ContentDTO contentDto, DriverMsgPushReqDTO driverMsgPushReq) {
        MsgContentDTO msgContentDTO = new MsgContentDTO();
        msgContentDTO.setMsgId(contentDto.getMsgId());
        msgContentDTO.setMsgType(MsgTypeEnum.SAFE_WARN_TEXT.getMsgType());
        msgContentDTO.setContent(MsgTipsConstant.SAFE_RISK_WARN);
        SafeWarnMsgReqDTO req = new SafeWarnMsgReqDTO();
        req.setMsgId(contentDto.getMsgId());
        req.setMsgContent(msgContentDTO);
        req.setPlateNumber(orderRelation.getPlateNumber());
        req.setSupplierCode(orderRelation.getSupplierCode());
        req.setDriverId(contentDto.getFromUserGuid());
        req.setOrderId(orderRelation.getOrderSerialNo());
        req.setSupplierOrderId(contentDto.getPassengerOrderGuid());
        req.setUnionId(driverMsgPushReq.getUnionId());
        req.setOpenId(driverMsgPushReq.getOpenId());
        req.setMemberId(StringUtils.isEmpty(driverMsgPushReq.getMemberId()) ? "0" : driverMsgPushReq.getMemberId());
        req.setImKey(req.filter1());
        req.setMsgType(MsgTypeEnum.SAFE_WARN_TEXT.getMsgType());
        req.setSessionKey(ImUtils.buildSessionKey(orderRelation.getSupplierCode(), req.filter1(), contentDto.getFromUserGuid()));
        return req;
    }

    @Override
    public CloseChatActionRspDTO closeChatAction(CloseChatActionReqDTO req) {
        LoggerUtils.info(log, "[closeChatAction] 关闭会话，req：{}", FastJsonUtils.toJSONString(req));

        try {
            SfcImInfo sfcImInfo = sfcImInfoDao.findByTcConversationId(req.getTcConversationId());
            if (Objects.isNull(sfcImInfo)) {
                throw new ValidateException(BIZ_ERROR_FACTORY.chatInfoNotFoundError(req.getTcConversationId()));
            }

            if (Objects.equals(sfcImInfo.getChatStatus(), 1)) {
                return CloseChatActionRspDTO.success(req.getTraceId());
            }

            sfcImInfo.setDriverId(req.getDriverId());
            sfcImInfo.setChatStatus(1);
            sfcImInfoDao.updateById(sfcImInfo);
            return CloseChatActionRspDTO.success(req.getTraceId());
        } catch (ValidateException validateException) {
            return CloseChatActionRspDTO.fail(req.getTraceId(), validateException.getErrorCode(), validateException.getError().getMessage());
        } catch (Exception e) {
            LoggerUtils.error(log, "[closeChatAction] 关闭会话异常 req：{}", FastJsonUtils.toJSONString(req), e);
            return CloseChatActionRspDTO.fail(req.getTraceId(), ERROR_CODE, "关闭会话异常");
        }
    }

    @Override
    public OpenChatActionRspDTO openChatAction(OpenChatActionReqDTO req) {
        LoggerUtils.info(log, "创建会话，req：{}", FastJsonUtils.toJSONString(req));
        long startTime = System.currentTimeMillis();

        try {
            OrderRelationResponse orderRelation = orderService.orderRelationInfo(Strings.EMPTY, req.getSupplierOrderId());
            if (Objects.isNull(orderRelation)) {
                throw new ValidateException(BIZ_ERROR_FACTORY.supOrderQueryTcOrderError(req.getSupplierOrderId()));
            }

            //订单详情
            OrderDetailDTO orderDetail = orderService.getOrderDetail(orderRelation.getOrderSerialNo());

            //订单检查
            validateOpenChatReq(orderDetail, req.getSupplierOrderId());

            String sessionKey = ImUtils.getSessionKey(orderDetail, orderRelation.getSupplierCode(), req.getDriverId());
            SfcImInfo sfcImInfo = sfcImInfoDao.findSfcImInfo(sessionKey, orderDetail.getOrderId());

            OpenChatActionDTO openChatAction = new OpenChatActionDTO();
            if (Objects.isNull(sfcImInfo)) {
                sfcImInfo = new SfcImInfo();
                String tcId = idGeneratorApi.getTcId() + Strings.EMPTY;
                sfcImInfo.setTcSessionId(tcId);
                sfcImInfo.setSupplierCode(orderRelation.getSupplierCode());
                sfcImInfo.setSupplierSessionId(req.getSupplierConversationId());
                sfcImInfo.setSupplierOrderId(req.getSupplierOrderId());
                sfcImInfo.setSessionKey(sessionKey);
                sfcImInfo.setChatStatus(0);
                sfcImInfo.setOrderId(orderRelation.getOrderSerialNo());
                sfcImInfo.setAllOrderIds(orderRelation.getOrderSerialNo());
                if (StringUtils.isNotEmpty(orderDetail.getDriverName())){
                    sfcImInfo.setDriverNickName(orderDetail.getDriverName());
                }
                sfcImInfo.setPlateNumber(orderDetail.getPlateNumber());
                sfcImInfo.setUnionId(orderDetail.getUnionId());
                sfcImInfo.setDriverId(req.getDriverId());
                sfcImInfo.setPlateId(orderDetail.getPlatId());
                sfcImInfo.setMemberId(Long.parseLong(orderDetail.getMemberId()));
                sfcImInfoDao.save(sfcImInfo);
                redisClientProxy.setnx(RedisKeyBuilder.IM_INFO_KEY + sessionKey, FastJsonUtils.toJSONString(sfcImInfo), 7200);
                openChatAction.setTcChatActionCode(tcId);
                LoggerUtils.info(log, "消息首次创建会话，rsp：{}，耗时：{}毫秒",
                        FastJsonUtils.toJSONString(openChatAction), System.currentTimeMillis() - startTime);
                return OpenChatActionRspDTO.success(req.getTraceId(), openChatAction);
            }

            extractedOrderId(orderRelation.getOrderSerialNo(), sfcImInfo);
            sfcImInfoDao.updateById(sfcImInfo);
            openChatAction.setTcChatActionCode(sfcImInfo.getTcSessionId());
            LoggerUtils.info(log, "消息创建会话，rsp：{}，耗时：{}毫秒",
                    FastJsonUtils.toJSONString(openChatAction), System.currentTimeMillis() - startTime);
            return OpenChatActionRspDTO.success(req.getTraceId(), openChatAction);
        } catch (ValidateException validateException) {
            return OpenChatActionRspDTO.fail(req.getTraceId(), validateException.getErrorCode(), validateException.getError().getMessage());
        } catch (Exception e) {
            LoggerUtils.error(log, "创建会话异常 req：{}", FastJsonUtils.toJSONString(req), e);
            return OpenChatActionRspDTO.fail(req.getTraceId(), ERROR_CODE, "创建会话异常");
        }
    }

    private void validateOpenChatReq(OrderDetailDTO orderDetail, String supplierOrderId) throws ValidateException {
        String orderId = orderDetail.getOrderId();
        if (Objects.isNull(orderDetail)) {
            throw new ValidateException(BIZ_ERROR_FACTORY.orderNotFoundError(orderId));
        }

        int status = orderDetail.getStatus();
        if (Objects.equals(status, OrderState.DISPATCHING.getCode())) {
            throw new ValidateException(BIZ_ERROR_FACTORY.orderDispatchError(orderId));
        }

        if (Objects.equals(status, OrderState.CANCELED.getCode())) {
            throw new ValidateException(BIZ_ERROR_FACTORY.orderCancelError(orderId));
        }

        String supOrderId = orderDetail.getSupplierOrderId();
        if (!StringUtils.equals(supOrderId, supplierOrderId)) {
            throw new ValidateException(BIZ_ERROR_FACTORY.orderReassignmentError(orderId));
        }

        String driverChatId = orderDetail.getDriverChatId();
        if (StringUtils.isEmpty(driverChatId) || Objects.equals(orderDetail.getDriverChatFlag(), 0)) {
            throw new ValidateException(BIZ_ERROR_FACTORY.driverNotChatError(driverChatId));
        }
    }
}
