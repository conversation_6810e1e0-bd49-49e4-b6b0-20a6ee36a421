<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:sof="http://schema.ly.com/schema/sof"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
         http://schema.ly.com/schema/sof http://schema.ly.com/schema/sof.xsd"
       default-autowire="byName">

    <!-- 供应链接入层 -->
    <sof:reference id="imFacade"
                   serviceName="im"
                   interface="com.ly.travel.shared.mobility.supply.integration.facade.carcommon.ImFacade"
                   gsName="${dsf.car.integration.gsName}"
                   version="${dsf.car.integration.version}"
                   timeout="1000" retries="0" serializer="FASTJSON">
        <sof:method name="sendMsg" path="sendMsg" paramType="bodyParam" timeout="10000" retries="0"/>
        <sof:method name="driverOnlineStatus" path="driverOnlineStatus" paramType="bodyParam" timeout="10000" retries="0"/>
        <sof:method name="uploadUserStatus" path="uploadUserStatus" paramType="bodyParam" timeout="10000" retries="0"/>
        <sof:method name="imCreate" path="imCreate" paramType="bodyParam" timeout="10000" retries="0"/>
        <sof:method name="imClose" path="imClose" paramType="bodyParam" timeout="10000" retries="0"/>
    </sof:reference>

    <!-- 交易层 -->
    <sof:reference id="carOrderFacade"
                   serviceName="order"
                   interface="com.ly.travel.car.orderservice.facade.CarOrderFacade"
                   gsName="${dsf.car.order.service.gsName}"
                   version="${dsf.car.order.service.version}"
                   timeout="1000" retries="0" serializer="FASTJSON">
        <sof:method name="psiDetail" path="psiDetail" paramType="bodyParam" timeout="10000" retries="0"/>
    </sof:reference>

    <!-- 供应链层 -->
    <sof:reference id="orderFacade"
                   serviceName="order"
                   interface="com.ly.travel.shared.mobility.supply.order.core.facade.OrderFacade"
                   gsName="${dsf.car.order.core.gsName}"
                   version="${dsf.car.order.core.version}"
                   timeout="1000" retries="0" serializer="FASTJSON">
        <sof:method name="orderRelationInfo" path="orderRelationInfo" paramType="bodyParam" timeout="10000" retries="0"/>
        <sof:method name="queryDriverInfo" path="queryDriverInfo" paramType="bodyParam" timeout="10000" retries="0"/>
        <sof:method name="queryDispatchList" path="queryDispatchList" paramType="bodyParam" timeout="10000" retries="0"/>
    </sof:reference>

    <sof:reference id="tradeFacade"
                   serviceName="trade"
                   interface="com.ly.travel.shared.mobility.supply.trade.core.facade.TradeFacade"
                   gsName="${dsf.car.supply.trade.core.gsName}"
                   version="${dsf.car.supply.trade.core.version}"
                   timeout="1000" retries="0" serializer="FASTJSON">
        <sof:method name="queryImCapacity" path="queryImCapacity" paramType="bodyParam" timeout="5000" retries="0"/>
    </sof:reference>

    <!-- 分销订单层 -->
    <sof:reference id="distributionIMFacade"
                   serviceName="im"
                   interface="com.ly.travel.car.distribution.facade.DistributionIMFacade"
                   gsName="${dsf.distribution.tcdsfGroup.gsName}"
                   version="${dsf.distribution.tcdsfGroup.version}"
                   timeout="1000" retries="0" serializer="FASTJSON">
        <sof:method name="imNotifyCallBack" path="imNotifyCallBack" paramType="bodyParam" timeout="2000" retries="0"/>
    </sof:reference>
</beans>
