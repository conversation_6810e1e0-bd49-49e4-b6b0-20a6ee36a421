<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:sof="http://schema.ly.com/schema/sof"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
         http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
         http://schema.ly.com/schema/sof http://schema.ly.com/schema/sof.xsd
         http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd"
       default-autowire="byName">

    <!-- HTTP -->
    <bean id="abstractBaseClient" class="com.ly.travel.car.im.integration.client.BaseClient">
        <property name="springRestTemplateClient" ref="springRestTemplateClient"/>
        <property name="restTemplate" ref="restTemplate"/>
    </bean>

    <bean id="springRestTemplateClient" class="com.ly.sof.api.client.SpringRestTemplateClient">
        <property name="restTemplate" ref="restTemplate"/>
    </bean>

    <bean id="restTemplate" class="org.springframework.web.client.RestTemplate">
        <constructor-arg ref="requestFactory"/>
    </bean>

    <!-- Http客户端配置 -->
    <bean id="requestFactory"
          class="org.springframework.http.client.SimpleClientHttpRequestFactory">
        <property name="readTimeout" value="${http_read_timeout}"/>
        <property name="connectTimeout" value="${connect_timeout}"/>
    </bean>
    <!-- HTTP end -->

    <bean id="cacheClientHA" class="com.ly.tcbase.cacheclient.CacheClientHA">
        <constructor-arg name="cacheName" value="${redis.groupNames}"/>
        <constructor-arg name="needThrowException" value="false"/>
    </bean>

    <bean id="redisClientProxy" class="com.ly.travel.car.im.integration.client.redis.impl.RedisClientProxy">
        <constructor-arg name="cacheClientHA" ref="cacheClientHA"/>
    </bean>
</beans>
