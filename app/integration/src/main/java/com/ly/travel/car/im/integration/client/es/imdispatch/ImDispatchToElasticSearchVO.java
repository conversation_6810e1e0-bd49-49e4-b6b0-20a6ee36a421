package com.ly.travel.car.im.integration.client.es.imdispatch;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;


@Data
public class ImDispatchToElasticSearchVO implements Serializable {

    private static final long serialVersionUID = 5418301932701725870L;

    /**
     * 订单编号
     */
    private String orderId;

    /**
     * 消息ID
     */
    private String msgId;

    /**
     * 供应商代码
     */
    private String supplierCode;

    /**
     * 供应商订单号
     */
    private String passengerOrderGuid;

    /**
     * 最新时间
     */
    private String latestTime;

    /**
     * 投递状态
     */
    private Integer status;

    private Date dispatchTime;

}
