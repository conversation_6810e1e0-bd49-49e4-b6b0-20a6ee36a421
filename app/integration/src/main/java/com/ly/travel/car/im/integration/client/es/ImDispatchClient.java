package com.ly.travel.car.im.integration.client.es;


import com.ly.travel.car.im.integration.client.es.common.ESBulkResponse;
import com.ly.travel.car.im.integration.client.es.common.ESSearchResponse;
import com.ly.travel.car.im.integration.client.es.imdispatch.ImDispatchSearchRequest;
import com.ly.travel.car.im.integration.client.es.imdispatch.ImDispatchToElasticSearchVO;

/**
 * <AUTHOR>
 * @version Id: SecretReportClient, v 0.1 2025/3/25 18:51 zhurun(1048699) Exp $
 */
public interface ImDispatchClient {

    ESBulkResponse bulk(ImDispatchToElasticSearchVO vo);
    
    /**
     * 根据消息ID查询消息状态
     * 
     * @param msgId 消息ID
     * @return 查询结果
     */
    ESSearchResponse<ImDispatchToElasticSearchVO> queryByMsgId(String msgId);

}