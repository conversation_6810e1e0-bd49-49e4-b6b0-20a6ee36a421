package com.ly.travel.car.im.integration.client.redis.impl;


import com.ly.tcbase.cacheclient.CacheClientHA;
import com.ly.tcbase.cacheclient.command.MultiRWEnum;
import com.ly.travel.car.im.integration.client.redis.RedisClient;
import com.ly.travel.car.im.integration.client.redis.ScanHandler;
import io.lettuce.core.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Map;

/**
 * Redis 客户端
 *
 * <AUTHOR>
 * @version Id: RedisClientProxy, v 0.1 2024/04/25 11:39 lichunbo Exp $
 */
public class RedisClientProxy implements RedisClient {

    /**
     * Redis多中心客户端
     */
    private final CacheClientHA cacheClientHA;

    /**
     * Instantiates a new Redis client proxy.
     *
     * @param cacheClientHA the cache client ha
     */
    public RedisClientProxy(CacheClientHA cacheClientHA) {
        this.cacheClientHA = cacheClientHA;
    }

    @Override
    public boolean save(MultiRWEnum multiRWEnum, String key, String value, long seconds) {
        if (StringUtils.isEmpty(key)) {
            return false;
        }

        if (seconds > 0) {
            return cacheClientHA.String().set(multiRWEnum, key, value, SetArgs.Builder.ex(seconds));
        }

        return cacheClientHA.String().set(multiRWEnum, key, value);
    }

    @Override
    public boolean save(String key, String value, long seconds) {
        if (StringUtils.isEmpty(key)) {
            return false;
        }

        if (seconds < 0) {
            return cacheClientHA.String().set(key, value);
        }

        return cacheClientHA.String().set(key, value, SetArgs.Builder.ex(seconds));
    }

    @Override
    public boolean save(MultiRWEnum multiRWEnum, String key, byte[] bytes, long seconds) {
        if (StringUtils.isEmpty(key)) {
            return false;
        }

        Boolean r = cacheClientHA.String().setBit(multiRWEnum, key, bytes);
        if (r != null && r && seconds > 0) {
            r = cacheClientHA.Key().expire(multiRWEnum, key, (int) seconds);
        }

        return r != null && r;
    }

    @Override
    public boolean hmset(MultiRWEnum multiRWEnum, String key, Map<String, String> map, long seconds) {
        if (StringUtils.isEmpty(key)) {
            return false;
        }

        Boolean r = cacheClientHA.Hash().hmset(multiRWEnum, key, map);
        if (r != null && r && seconds > 0) {
            r = cacheClientHA.Key().expire(multiRWEnum, key, (int) seconds);
        }

        return r;
    }

    @Override
    public boolean hset(MultiRWEnum multiRWEnum, String key, String fieldKey, String value, long seconds) {
        if (StringUtils.isEmpty(key)) {
            return false;
        }

        if (StringUtils.isEmpty(fieldKey)) {
            return false;
        }

        Boolean r = cacheClientHA.Hash().hset(multiRWEnum, key, fieldKey, value);
        if (r != null && r && seconds > 0) {
            r = cacheClientHA.Key().expire(multiRWEnum, key, (int) seconds);
        }

        return r;
    }

    @Override
    public boolean setnx(MultiRWEnum multiRwEnum, String key, String value, long seconds) {
        if (StringUtils.isEmpty(key)) {
            return false;
        }

        return cacheClientHA.String().setnx(multiRwEnum, key, seconds, value);
    }

    @Override
    public String getString(String key) {
        if (StringUtils.isEmpty(key)) {
            return null;
        }

        return cacheClientHA.String().get(key);
    }

    @Override
    public byte[] getBytes(String key) {
        if (StringUtils.isEmpty(key)) {
            return null;
        }

        return cacheClientHA.String().getBit(key);
    }

    @Override
    public boolean remove(String key) {
        if (StringUtils.isEmpty(key)) {
            return false;
        }

        return cacheClientHA.Key().del(key);
    }

    @Override
    public boolean exists(MultiRWEnum multiRWEnum, String key) {
        if (StringUtils.isEmpty(key)) {
            return false;
        }

        return cacheClientHA.Key().exists(multiRWEnum, key);
    }

    @Override
    public void scan(MultiRWEnum multiRwEnum, String pattern, long limit, ScanHandler handler) {
        Assert.isTrue(StringUtils.isNotEmpty(pattern), "无效pattern");
        Assert.isTrue(limit > 0, "无效limit");
        Assert.isTrue(handler != null, "无效handler");

        ScanArgs args = ScanArgs.Builder.limit(limit).match(pattern);
        KeyScanCursor<String> previous = null;
        do {
            if (previous != null) {
                previous = cacheClientHA.Key().scan(multiRwEnum, previous, args);
            } else {
                previous = cacheClientHA.Key().scan(multiRwEnum, ScanCursor.INITIAL, args);
            }

            // 需要注意的是，
            // 对元素的模式匹配工作是在命令从数据集中取出元素之后，
            // 向客户端返回元素之前的这段时间内进行的，
            // 所以如果被迭代的数据集中只有少量元素和模式相匹配，
            // 那么迭代命令或许会在多次执行中都不返回任何元素。
            handler.handle(previous.getKeys());

            // 判断是否还能继续迭代
        } while (!previous.isFinished());
    }

    @Override
    public List<KeyValue<String, String>> mget(String... keys) {
        if (keys == null || keys.length == 0) {
            return null;
        }

        return cacheClientHA.String().mgetNew(keys);
    }

    @Override
    public Map<String, String> hgetall(String key) {
        if (StringUtils.isEmpty(key)) {
            return null;
        }

        return cacheClientHA.Hash().hgetall(key);
    }

    @Override
    public String hget(String key, String field) {
        if (StringUtils.isEmpty(key) || StringUtils.isEmpty(field)) {
            return null;
        }

        return cacheClientHA.Hash().hget(key, field);
    }

    @Override
    public List<String> hmget(String key, String... fields) {
        if (StringUtils.isEmpty(key)) {
            return null;
        }

        if (fields == null || fields.length == 0) {
            return null;
        }

        return cacheClientHA.Hash().hmget(key, fields);
    }

    @Override
    public long ttl(String key) {
        if (StringUtils.isEmpty(key)) {
            return -2;
        }

        Long l = cacheClientHA.Key().ttl(key);
        if (l == null) {
            return -2;
        }

        if (l <= 0) {
            return 0;
        }

        return l;
    }

    /**
     * 原子加+过期时间
     *
     * @param key   String
     * @param value long
     * @return count
     */
    @Override
    public long incrby(MultiRWEnum multiRwEnum, String key, long value, long expireSeconds) {
        if (StringUtils.isEmpty(key)) {
            return -2;
        }

        Long latest = cacheClientHA.String().incrby(multiRwEnum, key, value);
        if (latest != null && expireSeconds > 0) {
            cacheClientHA.Key().expire(multiRwEnum, key, (int) expireSeconds);
        }

        return latest;
    }

    /**
     * hash小key的原子加
     *
     * @param key    大key
     * @param field  小key
     * @param amount 增加数量
     * @return 增加后的值
     */
    @Override
    public long hincrby(MultiRWEnum multiRwEnum, String key, String field, long amount) {
        if (StringUtils.isEmpty(key)) {
            return -2;
        }

        if (StringUtils.isEmpty(field)) {
            return -2;
        }

        return cacheClientHA.Hash().hincrby(multiRwEnum, key, field, amount);
    }

    /**
     * hash小key的原子加
     *
     * @param key    大key
     * @param field  小key
     * @param amount 增加数量
     * @return 增加后的值
     */
    @Override
    public long hincrby(MultiRWEnum multiRwEnum, String key, String field, long amount, long expireSeconds) {
        long latest = hincrby(multiRwEnum, key, field, amount);

        if (latest > 0 && expireSeconds > 0) {
            cacheClientHA.Key().expire(multiRwEnum, key, (int) expireSeconds);
        }

        return latest;
    }

    /**
     * 设置过期时间
     *
     * @param key           key
     * @param expireSeconds expireSeconds
     * @return boolean
     */
    @Override
    public boolean expire(String key, long expireSeconds) {
        return cacheClientHA.Key().expire(key, (int) expireSeconds);
    }

    /**
     * 删除hash中的key
     *
     * @param key    the key
     * @param fields the fields
     * @return
     */
    @Override
    public Long hdel(String key, String... fields) {
        if (StringUtils.isEmpty(key)) {
            return null;
        }

        if (fields == null || fields.length == 0) {
            return null;
        }

        return cacheClientHA.Hash().hdel(key, fields);
    }

    public long decr(String key) {
        if (StringUtils.isEmpty(key)) {
            return -2;
        }
        return cacheClientHA.String().decr(key);
    }
}