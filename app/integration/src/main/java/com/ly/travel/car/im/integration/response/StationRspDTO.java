package com.ly.travel.car.im.integration.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class StationRspDTO implements Serializable {

    private static final long serialVersionUID = 8823172889637413414L;

    /**
     * code : 200
     * title : 欢迎来到龙洞堡站
     * welcome_text : 我们将在发单后引导您前往最近的推荐上车点与司机碰面。火车站人员流量较大，请戴好口罩，安全出行。
     * cancel_text : 不在此火车站上车
     * data_list : [{"displayname":"龙洞堡站","name":"T2航站楼","city_name":"贵阳","city_id":114,"address":"南明区机场1号路贵阳龙洞堡国际机场2号航站楼","address_all":"南明区机场1号路贵阳龙洞堡国际机场2号航站楼龙洞堡站","lat":"26.54416","lng":"106.79963","polygon":[{"lng":"106.79931","lat":"26.54673"},{"lng":"106.80857","lat":"26.54535"},{"lng":"106.80765","lat":"26.5406"},{"lng":"106.79838","lat":"26.54214"},{"lng":"106.79935","lat":"26.54663"}],"scale":"15.6","poi_list":[{"function_area":"出发层","poi_name":"龙洞堡机场-网约车通道-滴滴车站","address":"龙洞堡机场-网约车通道-滴滴车站","address_all":"T2-龙洞堡机场-网约车通道-滴滴车站龙洞堡机场-网约车通道-滴滴车站","city_name":"贵阳","city_id":114,"lat":"26.54251","lng":"106.79905","tips":"机场区域仅支持小绿点上车","scale":15.6},{"function_area":"到达层","poi_name":"龙洞堡机场-网约车通道-滴滴车站","address":"龙洞堡机场-网约车通道-滴滴车站","address_all":"T2-龙洞堡机场-网约车通道-滴滴车站龙洞堡机场-网约车通道-滴滴车站","city_name":"贵阳","city_id":114,"lat":"26.54251","lng":"106.79905","tips":"机场区域仅支持小绿点上车","scale":15.6}]},{"displayname":"龙洞堡站","name":"T3航站楼","city_name":"贵阳","city_id":114,"address":"南明区机场1号路贵阳龙洞堡国际机场2号航站楼","address_all":"南明区机场1号路贵阳龙洞堡国际机场2号航站楼龙洞堡站","lat":"26.54416","lng":"106.79963","polygon":[{"lng":"106.79979","lat":"26.54907"},{"lng":"106.80468","lat":"26.54832"},{"lng":"106.80427","lat":"26.54605"},{"lng":"106.80053","lat":"26.5466"},{"lng":"106.80013","lat":"26.54667"},{"lng":"106.79936","lat":"26.54678"},{"lng":"106.79981","lat":"26.54905"}],"scale":"15.6","poi_list":[{"function_area":"出发层","poi_name":"T3航站楼-出发层-上车点","address":"T3航站楼-出发层-上车点","address_all":"T3航站楼-出发层-上车点T3航站楼-出发层-上车点","city_name":"贵阳","city_id":114,"lat":"26.54775","lng":"106.80058","tips":"机场区域仅支持小绿点上车","scale":15.6}]}]
     * data_info : []
     * areaIndex : 0
     * poiIndex :
     * tag : train
     * is_recommend : true
     * poi_id : 339
     */

    private int code;

    private String title;

    private String welcome_text;

    private String cancel_text;

    private int areaIndex;

    private String poiIndex;

    private String tag;

    private boolean is_recommend;

    private String poi_id;

    private List<DataListBean> data_list;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getWelcome_text() {
        return welcome_text;
    }

    public void setWelcome_text(String welcome_text) {
        this.welcome_text = welcome_text;
    }

    public String getCancel_text() {
        return cancel_text;
    }

    public void setCancel_text(String cancel_text) {
        this.cancel_text = cancel_text;
    }

    public int getAreaIndex() {
        return areaIndex;
    }

    public void setAreaIndex(int areaIndex) {
        this.areaIndex = areaIndex;
    }

    public String getPoiIndex() {
        return poiIndex;
    }

    public void setPoiIndex(String poiIndex) {
        this.poiIndex = poiIndex;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public boolean isIs_recommend() {
        return is_recommend;
    }

    public void setIs_recommend(boolean is_recommend) {
        this.is_recommend = is_recommend;
    }

    public String getPoi_id() {
        return poi_id;
    }

    public void setPoi_id(String poi_id) {
        this.poi_id = poi_id;
    }

    public List<DataListBean> getData_list() {
        return data_list;
    }

    public void setData_list(List<DataListBean> data_list) {
        this.data_list = data_list;
    }

    public static class DataListBean implements Serializable {

        private static final long serialVersionUID = 3758625347686621519L;

        /**
         * displayname : 龙洞堡站
         * name : T2航站楼
         * city_name : 贵阳
         * city_id : 114
         * address : 南明区机场1号路贵阳龙洞堡国际机场2号航站楼
         * address_all : 南明区机场1号路贵阳龙洞堡国际机场2号航站楼龙洞堡站
         * lat : 26.54416
         * lng : 106.79963
         * polygon : [{"lng":"106.79931","lat":"26.54673"},{"lng":"106.80857","lat":"26.54535"},{"lng":"106.80765","lat":"26.5406"},{"lng":"106.79838","lat":"26.54214"},{"lng":"106.79935","lat":"26.54663"}]
         * scale : 15.6
         * poi_list : [{"function_area":"出发层","poi_name":"龙洞堡机场-网约车通道-滴滴车站","address":"龙洞堡机场-网约车通道-滴滴车站","address_all":"T2-龙洞堡机场-网约车通道-滴滴车站龙洞堡机场-网约车通道-滴滴车站","city_name":"贵阳","city_id":114,"lat":"26.54251","lng":"106.79905","tips":"机场区域仅支持小绿点上车","scale":15.6},{"function_area":"到达层","poi_name":"龙洞堡机场-网约车通道-滴滴车站","address":"龙洞堡机场-网约车通道-滴滴车站","address_all":"T2-龙洞堡机场-网约车通道-滴滴车站龙洞堡机场-网约车通道-滴滴车站","city_name":"贵阳","city_id":114,"lat":"26.54251","lng":"106.79905","tips":"机场区域仅支持小绿点上车","scale":15.6}]
         */

        private String displayname;
        private String name;
        private String city_name;
        private int city_id;
        private String address;
        private String address_all;
        private String lat;
        private String lng;
        private String scale;
        private List<PolygonBean> polygon;
        private List<PoiListBean> poi_list;

        public String getDisplayname() {
            return displayname;
        }

        public void setDisplayname(String displayname) {
            this.displayname = displayname;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getCity_name() {
            return city_name;
        }

        public void setCity_name(String city_name) {
            this.city_name = city_name;
        }

        public int getCity_id() {
            return city_id;
        }

        public void setCity_id(int city_id) {
            this.city_id = city_id;
        }

        public String getAddress() {
            return address;
        }

        public void setAddress(String address) {
            this.address = address;
        }

        public String getAddress_all() {
            return address_all;
        }

        public void setAddress_all(String address_all) {
            this.address_all = address_all;
        }

        public String getLat() {
            return lat;
        }

        public void setLat(String lat) {
            this.lat = lat;
        }

        public String getLng() {
            return lng;
        }

        public void setLng(String lng) {
            this.lng = lng;
        }

        public String getScale() {
            return scale;
        }

        public void setScale(String scale) {
            this.scale = scale;
        }

        public List<PolygonBean> getPolygon() {
            return polygon;
        }

        public void setPolygon(List<PolygonBean> polygon) {
            this.polygon = polygon;
        }

        public List<PoiListBean> getPoi_list() {
            return poi_list;
        }

        public void setPoi_list(List<PoiListBean> poi_list) {
            this.poi_list = poi_list;
        }

        public static class PolygonBean {
            /**
             * lng : 106.79931
             * lat : 26.54673
             */

            private String lng;
            private String lat;

            public String getLng() {
                return lng;
            }

            public void setLng(String lng) {
                this.lng = lng;
            }

            public String getLat() {
                return lat;
            }

            public void setLat(String lat) {
                this.lat = lat;
            }
        }

        public static class PoiListBean {
            /**
             * function_area : 出发层
             * poi_name : 龙洞堡机场-网约车通道-滴滴车站
             * address : 龙洞堡机场-网约车通道-滴滴车站
             * address_all : T2-龙洞堡机场-网约车通道-滴滴车站龙洞堡机场-网约车通道-滴滴车站
             * city_name : 贵阳
             * city_id : 114
             * lat : 26.54251
             * lng : 106.79905
             * tips : 机场区域仅支持小绿点上车
             * scale : 15.6
             */

            private String function_area;
            private String poi_name;
            private String address;
            private String address_all;
            private String city_name;
            private int city_id;
            private String lat;
            private String lng;
            private String tips;
            private double scale;

            public String getFunction_area() {
                return function_area;
            }

            public void setFunction_area(String function_area) {
                this.function_area = function_area;
            }

            public String getPoi_name() {
                return poi_name;
            }

            public void setPoi_name(String poi_name) {
                this.poi_name = poi_name;
            }

            public String getAddress() {
                return address;
            }

            public void setAddress(String address) {
                this.address = address;
            }

            public String getAddress_all() {
                return address_all;
            }

            public void setAddress_all(String address_all) {
                this.address_all = address_all;
            }

            public String getCity_name() {
                return city_name;
            }

            public void setCity_name(String city_name) {
                this.city_name = city_name;
            }

            public int getCity_id() {
                return city_id;
            }

            public void setCity_id(int city_id) {
                this.city_id = city_id;
            }

            public String getLat() {
                return lat;
            }

            public void setLat(String lat) {
                this.lat = lat;
            }

            public String getLng() {
                return lng;
            }

            public void setLng(String lng) {
                this.lng = lng;
            }

            public String getTips() {
                return tips;
            }

            public void setTips(String tips) {
                this.tips = tips;
            }

            public double getScale() {
                return scale;
            }

            public void setScale(double scale) {
                this.scale = scale;
            }
        }
    }
}
