package com.ly.travel.car.im.integration.client.api;

import com.alibaba.fastjson.JSONObject;
import com.ly.car.utils.HttpUtils;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.im.integration.request.AppPushReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.message.BasicHeader;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class AppPushApi {

    @Value("${config.push.appPushUrl}")
    private String appPushUrl;

    @Value("${config.push.appPushToken}")
    private String appPushToken;

    public JSONObject appPush(AppPushReqDTO req) {
        String url = appPushUrl + "/pushRest";
        String responseBody = null;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("Account", "car.java.dsf.im.chat");
        jsonObject.put("Password", "car.java.dsf.im.chat");
        jsonObject.put("NodeId", req.getNodeId());
        String memberId = req.getMemberId();
        jsonObject.put("MemberId", memberId);
        jsonObject.put("PushUrl", req.getPushUrl());

        try {
            LoggerUtils.info(log, "[appPush][{}] appPush结果：{}", memberId, jsonObject.toJSONString());
            responseBody = HttpUtils.post(url, jsonObject.toJSONString(), new BasicHeader("Generalplatform-Token", appPushToken));
            LoggerUtils.info(log, "[appPush][{}] appPush结果：{}", memberId, responseBody);
            return FastJsonUtils.fromJSONString(responseBody, JSONObject.class);
        } catch (Exception e) {
            LoggerUtils.error(log, "[appPush][{}]  app推送异常：{}", memberId, responseBody, e);
        }
        return null;
    }
}
