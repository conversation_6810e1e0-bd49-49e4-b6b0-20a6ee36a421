package com.ly.travel.car.im.integration.client;

import com.alibaba.fastjson2.TypeReference;
import com.ly.mom.netty.util.internal.ThrowableUtil;
import com.ly.sof.spi.context.SOFContext;
import com.ly.travel.car.im.integration.client.es.common.ESBulkResponse;
import com.ly.travel.car.im.integration.client.es.common.ESSearchResponse;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version Id: EsClient, v 0.1 2025/3/18 18:43 zhurun(1048699) Exp $
 */
@Slf4j
public class BaseEsHttpClient extends BaseHttpClient {

    private static final String   ES_URL     = "http://es.dss.17usoft.com";
    private static final String   BULK_URL   = "/index/%s/type/info/bulk/sync";
    private static final String   SEARCH_URL = "/index/%s/template/%s/%s/search";

    protected static final Logger LOGGER     = LoggerFactory.getLogger(BaseEsHttpClient.class);
    @Resource
    protected SOFContext          sofContext;

    protected String env() {
        return sofContext.getAppConfiguration().getPropertyValue("sof-env", "qa");
    }

    public <T> ESBulkResponse bulk(List<T> body, String esName, Map<String, String> headerMap) {
        String url = ES_URL + String.format(BULK_URL, esName);
        TypeReference<ESBulkResponse> typeReference = new TypeReference<ESBulkResponse>() {
        };
        ESBulkResponse response = null;
        try {
            response = post(url, body, headerMap, typeReference, 300);
        } catch (IOException e) {
            log.error("[][EsClient][bulk][][] url:{} 请求体:{} 返回体:{},写入es异常:{}", url, body, response, ThrowableUtil.stackTraceToString(e));
        } finally {
            log.info("[][EsClient][bulk][][] url:{} 请求体:{} 返回体:{}", url, body, response);
        }
        return response;
    }

    public <T, REQUEST> ESSearchResponse<T> search(REQUEST body, TypeReference<ESSearchResponse<T>> responseType, String elasticSearchName,
                                                   String templateName, String version, Map<String, String> headerMap) {

        ESSearchResponse<T> response = null;
        String url = ES_URL + String.format(SEARCH_URL, elasticSearchName, templateName, version);
        try {
            response = post(url, body, headerMap, responseType, 3000);
        } catch (IOException e) {
            LOGGER.error("[][EsClient][search][][] url:{} 请求体:{} 返回体:{},查询es异常:{}", url, body, response,
                ThrowableUtil.stackTraceToString(e));
        } finally {
            log.info("[][EsClient][search][][] url:{} 请求体:{} 返回体:{}", url, body, response);
        }
        return response;
    }
}
