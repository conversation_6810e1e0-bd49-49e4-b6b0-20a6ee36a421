package com.ly.travel.car.im.integration.client.api;

import com.ly.car.utils.HttpUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.im.integration.response.IdRspDTO;
import com.ly.travel.car.im.integration.throwable.error.IdGeneratorException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class IdGeneratorApi {
    private static final String ID_URL = "http://tccomponent.17usoft.com/uniquekey/idgen";

    public Long getTcId() {
        String result = HttpUtils.get(ID_URL);
        IdRspDTO idRspDTO = FastJsonUtils.fromJSONString(result, IdRspDTO.class);
        if (!idRspDTO.isSuccess()) {
            throw new IdGeneratorException(result);
        }
        return idRspDTO.getId().get(0);
    }
}
