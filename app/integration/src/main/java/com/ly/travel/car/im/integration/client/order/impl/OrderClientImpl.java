package com.ly.travel.car.im.integration.client.order.impl;

import com.ly.travel.car.im.integration.client.BaseClient;
import com.ly.travel.car.im.integration.client.order.OrderClient;
import com.ly.travel.car.im.integration.throwable.exception.IntegrationException;
import com.ly.travel.car.orderservice.facade.CarOrderFacade;
import com.ly.travel.car.orderservice.facade.request.order.OrderDetailRequest;
import com.ly.travel.car.orderservice.facade.response.order.OrderDetailResponse;
import com.ly.travel.shared.mobility.supply.order.core.facade.OrderFacade;
import com.ly.travel.shared.mobility.supply.order.core.facade.order.request.OrderRelationRequest;
import com.ly.travel.shared.mobility.supply.order.core.facade.order.request.QueryDispatchListRequest;
import com.ly.travel.shared.mobility.supply.order.core.facade.order.request.QueryDriverInfoRequest;
import com.ly.travel.shared.mobility.supply.order.core.facade.order.response.OrderRelationResponse;
import com.ly.travel.shared.mobility.supply.order.core.facade.order.response.QueryDispatchListResponse;
import com.ly.travel.shared.mobility.supply.order.core.facade.order.response.QueryDriverInfoResponse;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 订单相关服务
 *
 * <AUTHOR>
 * @version v0.1 2024年05月25日 11:23 lichunbo Exp $
 */
@Service
public class OrderClientImpl extends BaseClient implements OrderClient {
    @Resource
    private CarOrderFacade carOrderFacade;
    @Resource
    private OrderFacade orderFacade;

    @Override
    public OrderDetailResponse psiDetail(OrderDetailRequest var1) throws IntegrationException {
        OrderDetailResponse response = call(carOrderFacade::psiDetail, var1, OrderDetailResponse::isSuccess, OrderDetailResponse::getErrorMessage);
        return response;
    }

    @Override
    public OrderRelationResponse orderRelationInfo(OrderRelationRequest var1) throws IntegrationException {
        OrderRelationResponse response = call(orderFacade::orderRelationInfo, var1, OrderRelationResponse::isSuccess, OrderRelationResponse::getErrorMessage);
        return response;
    }

    @Override
    public QueryDriverInfoResponse queryDriverInfo(QueryDriverInfoRequest var1) throws IntegrationException {
        QueryDriverInfoResponse response = call(orderFacade::queryDriverInfo, var1, QueryDriverInfoResponse::isSuccess, QueryDriverInfoResponse::getErrorMessage);
        return response;
    }

    @Override
    public QueryDispatchListResponse queryDispatchList(QueryDispatchListRequest var1) throws IntegrationException {
        QueryDispatchListResponse response = call(orderFacade::queryDispatchList, var1, QueryDispatchListResponse::isSuccess, QueryDispatchListResponse::getErrorMessage);
        return response;
    }
}
