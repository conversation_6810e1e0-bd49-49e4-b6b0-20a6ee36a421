package com.ly.travel.car.im.integration.client.distribute.impl;

import com.ly.travel.car.distribution.facade.DistributionIMFacade;
import com.ly.travel.car.distribution.facade.request.IMNotifyCallBackReq;
import com.ly.travel.car.distribution.facade.response.IMNotifyCallBackRsp;
import com.ly.travel.car.im.integration.client.BaseClient;
import com.ly.travel.car.im.integration.client.distribute.DistributeClient;
import com.ly.travel.car.im.integration.throwable.exception.IntegrationException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class DistributeClientImpl extends BaseClient implements  DistributeClient {

    @Resource
    private DistributionIMFacade distributeFacade;


    @Override
    public IMNotifyCallBackRsp imNotifyCallBack(IMNotifyCallBackReq var1) throws IntegrationException {
        return call(distributeFacade::imNotifyCallBack, var1, IMNotifyCallBackRsp::isSuccess, IMNotifyCallBackRsp::getErrorMessage);
    }
}
