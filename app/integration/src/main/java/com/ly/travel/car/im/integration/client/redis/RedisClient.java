package com.ly.travel.car.im.integration.client.redis;

import com.ly.tcbase.cacheclient.command.MultiRWEnum;
import io.lettuce.core.KeyValue;

import java.util.List;
import java.util.Map;

/**
 * Redis 客户端
 *
 * <AUTHOR>
 * @version Id: RedisClient, v 0.1 2024/04/25 11:39 lichunbo Exp $
 */
public interface RedisClient {

    /**
     * 保存数据 不过期的
     *
     * @param key   the key
     * @param value the value
     * @return boolean boolean
     */
    default boolean save(String key, String value) {
        return this.save(key, value, -1);
    }

    /**
     * Save String.
     *
     * @param key     the key
     * @param value   the value
     * @param seconds 数据有效期,单位秒,如果值小于0表示数据不过期.
     * @return the boolean
     */
    default boolean save(String key, String value, long seconds) {
        return this.save(MultiRWEnum.DEFAULT_CLUSTER_TYPE, key, value, seconds);
    }

    /**
     * Save String.
     *
     * @param multiRWEnum the multi rw enum
     * @param key         the key
     * @param value       the value
     * @param seconds     the seconds
     * @return the boolean
     */
    boolean save(MultiRWEnum multiRWEnum, String key, String value, long seconds);

    /**
     * Save bytes.
     *
     * @param key   the key
     * @param bytes the bytes
     * @return the boolean
     */
    default boolean save(String key, byte[] bytes) {
        return save(key, bytes, -1);
    }

    /**
     * Save bytes with expire.
     *
     * @param key     the key
     * @param bytes   the bytes
     * @param seconds the expire in second
     * @return boolean boolean
     */
    default boolean save(String key, byte[] bytes, long seconds) {
        return this.save(MultiRWEnum.DEFAULT_CLUSTER_TYPE, key, bytes, seconds);
    }

    /**
     * Save boolean.
     *
     * @param multiRWEnum the multi rw enum
     * @param key         the key
     * @param bytes       the bytes
     * @param seconds     the seconds
     * @return the boolean
     */
    boolean save(MultiRWEnum multiRWEnum, String key, byte[] bytes, long seconds);

    /**
     * [Hash] method
     * 批量插入
     *
     * @param key     the key
     * @param map     the map
     * @param seconds the seconds
     * @return the boolean
     */
    default boolean hmset(String key, Map<String, String> map, long seconds) {
        return this.hmset(MultiRWEnum.DEFAULT_CLUSTER_TYPE, key, map, seconds);
    }

    /**
     * [Hash] method
     * 批量插入,可指定写策略
     *
     * @param multiRWEnum the multi rw enum
     * @param key         redis 大KEY
     * @param map         the map
     * @param seconds     the expire in millisecond
     * @return boolean boolean
     */
    boolean hmset(MultiRWEnum multiRWEnum, String key, Map<String, String> map, long seconds);

    /**
     * hash 单条插入
     *
     * @param key      the key
     * @param fieldKey the field key
     * @param value    the value
     * @param seconds  the expire in millisecond
     * @return boolean boolean
     */
    default boolean hset(String key, String fieldKey, String value, long seconds) {
        return this.hset(MultiRWEnum.DEFAULT_CLUSTER_TYPE, key, fieldKey, value, seconds);
    }

    /**
     * hash 单条插入,可指定写策略
     *
     * @param multiRWEnum the multi rw enum
     * @param key         the key
     * @param fieldKey    the field key
     * @param value       the value
     * @param seconds     the seconds
     * @return the boolean
     */
    boolean hset(MultiRWEnum multiRWEnum, String key, String fieldKey, String value, long seconds);

    /**
     * SETNX
     * SETNX key value
     * 将 key 的值设为 value ，当且仅当 key 不存在。
     * 若给定的 key 已经存在，则 SETNX 不做任何动作。
     * SETNX 是『SET if Not eXists』(如果不存在，则 SET)的简写。
     *
     * @param key     the key
     * @param data    the data
     * @param seconds the expire in second
     * @return
     */
    default boolean setnx(String key, String data, long seconds) {
        return setnx(MultiRWEnum.DEFAULT_CLUSTER_TYPE, key, data, seconds);
    }

    /**
     * SETNX
     * SETNX key value
     * 将 key 的值设为 value ，当且仅当 key 不存在。
     * 若给定的 key 已经存在，则 SETNX 不做任何动作。
     * SETNX 是『SET if Not eXists』(如果不存在，则 SET)的简写。
     *
     * @param multiRwEnum the multi rw enum
     * @param key         the key
     * @param data        the data
     * @param seconds     the expire in second
     * @return
     */
    boolean setnx(MultiRWEnum multiRwEnum, String key, String data, long seconds);

    /**
     * 探测某个KEY的剩余过期时间,单位秒
     *
     * @param key the key
     * @return 如果KEY不存在, 模拟redis返回-2
     */
    long ttl(String key);

    /**
     * 获取字符串
     *
     * @param key the key
     * @return str str
     */
    String getString(String key);

    /**
     * 返回字节数组
     *
     * @param key the key
     * @return the byte [ ]
     */
    byte[] getBytes(String key);

    /**
     * 删除key
     * 固定MultiRWEnum.ALL_SUCCESS策略模式
     *
     * @param key the key
     * @return boolean boolean
     */
    boolean remove(String key);

    /**
     * 查询是否存在某个KEY
     *
     * @param key the key
     * @return boolean boolean
     */
    default boolean exists(String key) {
        return this.exists(MultiRWEnum.DEFAULT_CLUSTER_TYPE, key);
    }

    /**
     * 查询是否存在某个KEY
     * 可指定策略模式.
     *
     * @param multiRWEnum the multi rw enum
     * @param key         the key
     * @return the boolean
     */
    boolean exists(MultiRWEnum multiRWEnum, String key);

    /**
     * [推荐使用] 自动迭代并处理
     *
     * @param pattern KEY匹配模式
     * @param limit   期望每次scan的条数
     * @param handler 迭代到的数据处理器
     */
    default void scan(String pattern, long limit, ScanHandler handler) {
        // 因为scan游标读操作有上下文的依赖关系,所以使用 ONLY_MAIN_CLUSTER_TYPE 更佳.
        scan(MultiRWEnum.ONLY_MAIN_CLUSTER_TYPE, pattern, limit, handler);
    }

    /**
     * 自动迭代并处理
     *
     * @param multiRwEnum 读策略
     * @param pattern     KEY匹配模式
     * @param limit       期望每次scan的条数
     * @param handler     迭代到的数据处理器
     */
    void scan(MultiRWEnum multiRwEnum, String pattern, long limit, ScanHandler handler);

    /**
     * Mget list.
     *
     * @param keys the keys
     * @return the list
     */
    default List<KeyValue<String, String>> mget(List<String> keys) {
        if (keys == null || keys.size() == 0) {
            return null;
        }

        return this.mget(keys.toArray(new String[0]));
    }

    /**
     * [String] method
     * multiple get
     * 支持一次交互同时获取多个KEY对应的值
     * 是{@link RedisClient#getString(String)} 的增强版
     *
     * @param keys the keys
     * @return the list
     */
    List<KeyValue<String, String>> mget(String... keys);

    /**
     * [Hash] method
     * hgetall
     * 返回K,V对应的map
     *
     * @param key the key
     * @return map map
     */
    Map<String, String> hgetall(String key);

    /**
     * [Hash] method
     * 通过指定的大KEY获取指定field对应的值
     *
     * @param key   the key
     * @param field the field
     * @return string string
     */
    String hget(String key, String field);

    /**
     * [Hash] method
     * hash multiple get
     * 批量删除指定大KEY缓存中的多个field的值
     *
     * @param key    the key
     * @param fields the fields
     * @return Long
     */
    Long hdel(String key, String... fields);

    /**
     * [Hash] method
     * hash multiple get
     * 批量获取指定大KEY缓存中的多个field的值
     *
     * @param key    the key
     * @param fields the fields
     * @return list list
     */
    List<String> hmget(String key, String... fields);

    /**
     * 原子加
     *
     * @param key   String
     * @param value long
     * @return count long
     */
    default long incrby(String key, long value) {
        return this.incrby(key, value, -1);
    }

    /**
     * 原子加 + 过期时间
     *
     * @param key           String
     * @param value         long
     * @param expireSeconds the expire seconds
     * @return count long
     */
    default long incrby(String key, long value, long expireSeconds) {
        return this.incrby(MultiRWEnum.DEFAULT_CLUSTER_TYPE, key, value, expireSeconds);
    }

    long incrby(MultiRWEnum multiRWEnum, String key, long value, long expireSeconds);

    /**
     * hash小key的原子加
     *
     * @param key    大key
     * @param field  小key
     * @param amount 增加数量
     * @return 增加后的值 long
     */
    default long hincrby(String key, String field, long amount) {
        return this.hincrby(MultiRWEnum.DEFAULT_CLUSTER_TYPE, key, field, amount);
    }

    /**
     * hash小key的原子加
     *
     * @param key           大key
     * @param field         小key
     * @param amount        增加数量
     * @param expireSeconds 过期时间
     * @return 增加后的值 long
     */
    default long hincrby(String key, String field, long amount, long expireSeconds) {
        return this.hincrby(MultiRWEnum.DEFAULT_CLUSTER_TYPE, key, field, amount, expireSeconds);
    }

    /**
     * hash小key的原子加
     *
     * @param multiRWEnum 写策略模式
     * @param key         大key
     * @param field       小key
     * @param amount      增加数量
     * @return 增加后的值 long
     */
    long hincrby(MultiRWEnum multiRWEnum, String key, String field, long amount);

    /**
     * hash小key的原子加
     *
     * @param multiRWEnum   写策略模式
     * @param key           大key
     * @param field         小key
     * @param amount        增加数量
     * @param expireSeconds 过期时间
     * @return 增加后的值 long
     */
    long hincrby(MultiRWEnum multiRWEnum, String key, String field, long amount, long expireSeconds);

    /**
     * 设置过期时间
     *
     * @param key
     * @param expireSeconds
     * @return
     */
    default boolean expire(String key, long expireSeconds) {
        return false;
    }
}