package com.ly.travel.car.im.integration.client.supply;

import com.ly.travel.car.im.integration.throwable.exception.IntegrationException;
import com.ly.travel.shared.mobility.supply.trade.core.facade.trade.request.SupplierImCapacityRequest;
import com.ly.travel.shared.mobility.supply.trade.core.facade.trade.response.SupplierImCapacityResponse;

public interface SupplierClient {

    SupplierImCapacityResponse queryImCapacity(SupplierImCapacityRequest var1) throws IntegrationException;
}
