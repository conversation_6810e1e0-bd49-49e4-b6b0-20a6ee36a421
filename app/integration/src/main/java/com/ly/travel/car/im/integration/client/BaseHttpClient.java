package com.ly.travel.car.im.integration.client;

import com.alibaba.fastjson2.TypeReference;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.sof.utils.mapping.JacksonUtils;
import com.ly.travel.car.im.integration.utils.HttpUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version Id: BaseHttpClient, v 0.1 2025/3/18 18:25 zhurun(1048699) Exp $
 */
@Slf4j
public class BaseHttpClient {

    /**
     * 默认超时时间，单位秒
     */
    private static final int DEFAULT_TIMEOUT         = 10;

    /**
     * 默认重试次数
     */
    private static final int DEFAULT_RETRY_TIMES     = 1;

    /**
     * 默认最大重试次数
     */
    private static final int DEFAULT_RETRY_TIMES_MAX = 5;

    /**
     * 默认重试间隔时间，单位毫秒
     */
    private static final int DEFAULT_RETRY_INTERVAL  = 10;

    /**
     * Post t.
     *
     * @param <T>      the type parameter
     * @param url      the url
     * @param request  the request
     * @param clazz    the clazz
     * @param timeout  the timeout
     * @return the t
     * @throws IOException the io exception
     */
    protected <T> T post(String url, Object request, Class<T> clazz, int timeout) throws IOException {
        return post(url, request, clazz, timeout, TimeUnit.MILLISECONDS);
    }

    /**
     * Post t.
     *
     * @param <T>      the type parameter
     * @param url      the url
     * @param request  the request
     * @param clazz    the clazz
     * @param timeout  the timeout
     * @param timeUnit the time unit
     * @return the t
     * @throws IOException the io exception
     */
    protected <T> T post(String url, Object request, Class<T> clazz, int timeout, TimeUnit timeUnit) throws IOException {
        long start = System.currentTimeMillis();
        String ret = "";
        try {
            ret = HttpUtils.postJson(url, request, timeout, timeUnit);
            if (clazz == String.class) {
                return (T) ret;
            }
            return FastJsonUtils.fromJSONString(ret, clazz);
        } finally {
            LoggerUtils.info(log, "[HTTP]http post times:{}ms ，\r\nurl:{} ，\r\nrequest:{} ，\r\nresponse:{}，",
                System.currentTimeMillis() - start, url, JacksonUtils.toJSONString(request), ret);
        }
    }

    /**
     * Post if fail retry t.
     *
     * @param <T>     the type parameter
     * @param url     the url
     * @param request the request
     * @param clazz   the clazz
     * @param timeout the timeout
     * @return the t
     * @throws IOException the io exception
     */
    protected <T> T postIfFailRetry(String url, Object request, Class<T> clazz, int timeout) throws IOException {
        return postIfFailRetry(url, request, clazz, timeout, DEFAULT_RETRY_TIMES);
    }

    /**
     * Post if fail retry t.
     *
     * @param <T>        the type parameter
     * @param url        the url
     * @param request    the request
     * @param clazz      the clazz
     * @param timeout    the timeout
     * @param retryTimes the retry times
     * @return the t
     * @throws IOException the io exception
     */
    protected <T> T postIfFailRetry(String url, Object request, Class<T> clazz, int timeout, int retryTimes) throws IOException {
        if (retryTimes > DEFAULT_RETRY_TIMES_MAX) {
            retryTimes = DEFAULT_RETRY_TIMES;
        }
        try {
            return post(url, request, clazz, timeout);
        } catch (IOException e) {
            if (retryTimes > 0) {
                try {
                    Thread.sleep(DEFAULT_RETRY_INTERVAL);
                } catch (InterruptedException ignored) {
                }
                retryTimes--;
                return postIfFailRetry(url, request, clazz, timeout, retryTimes);
            } else {
                throw e;
            }
        }
    }

    /**
     * Post t.
     *
     * @param <T>      the type parameter
     * @param url      the url
     * @param request  the request
     * @param headerMap the header
     * @param clazz    the clazz
     * @param timeout  the timeout
     * @return the t
     * @throws IOException the io exception
     */
    protected <T> T post(String url, Object request, Map<String, String> headerMap, TypeReference<T> clazz,
                         int timeout) throws IOException {
        return post(url, request, headerMap, clazz, timeout, TimeUnit.MILLISECONDS);
    }

    /**
     * Post t.
     *
     * @param <T>      the type parameter
     * @param url      the url
     * @param request  the request
     * @param headerMap the header
     * @param typeReference    the typeReference
     * @param timeout  the timeout
     * @param timeUnit the time unit
     * @return the t
     * @throws IOException the io exception
     */
    protected <T> T post(String url, Object request, Map<String, String> headerMap, TypeReference<T> typeReference, int timeout,
                         TimeUnit timeUnit) throws IOException {
        long start = System.currentTimeMillis();
        String ret = "";
        try {
            ret = HttpUtils.postJson(url, request, headerMap, timeout, timeUnit);
            if (typeReference.getRawType() == String.class) {
                return (T) ret;
            }
            return FastJsonUtils.fromJSONString(ret, typeReference);
        } finally {
            LoggerUtils.info(log, "[HTTP]http post times:{}ms ，\r\nurl:{} ，\r\nrequest:{} ，\r\nresponse:{}，",
                System.currentTimeMillis() - start, url, JacksonUtils.toJSONString(request), ret);
        }
    }
}
