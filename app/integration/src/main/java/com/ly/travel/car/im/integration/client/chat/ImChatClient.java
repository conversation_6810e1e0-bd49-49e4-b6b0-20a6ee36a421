package com.ly.travel.car.im.integration.client.chat;

import com.ly.travel.car.im.integration.throwable.exception.IntegrationException;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.driverOnlineStatus.DriverOnlineStatusRequest;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.driverOnlineStatus.DriverOnlineStatusResponse;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.imClose.ImCloseRequest;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.imClose.ImCloseResponse;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.imCreate.ImCreateRequest;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.imCreate.ImCreateResponse;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.sendMsg.SendMsgRequest;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.sendMsg.SendMsgResponse;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.uploadUserStatus.UploadUserStatusRequest;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.uploadUserStatus.UploadUserStatusResponse;

/**
 * 聊天相关服务
 *
 * <AUTHOR>
 * @version v0.1 2024年05月25日 11:23 lichunbo Exp $
 */
public interface ImChatClient {

    /**
     * 乘客消息发送司机
     *
     * @param var1
     * @return
     * @throws IntegrationException
     */
    SendMsgResponse sendMsg(SendMsgRequest var1) throws IntegrationException;

    /**
     * 获取司机在离线状态
     *
     * @param var1
     * @return
     * @throws IntegrationException
     */
    DriverOnlineStatusResponse getDriverOnlineStatus(DriverOnlineStatusRequest var1) throws IntegrationException;

    /**
     * 上传用户状态
     *
     * @param var1
     * @return
     * @throws IntegrationException
     */
    UploadUserStatusResponse uploadUserStatus(UploadUserStatusRequest var1) throws IntegrationException;

    /**
     * 创建会话
     *
     * @param var1
     * @return
     * @throws IntegrationException
     */
    ImCreateResponse imCreate(ImCreateRequest var1) throws IntegrationException;

    /**
     * 关闭会话
     *
     * @param var1
     * @return
     * @throws IntegrationException
     */
    ImCloseResponse imClose(ImCloseRequest var1) throws IntegrationException;
}
