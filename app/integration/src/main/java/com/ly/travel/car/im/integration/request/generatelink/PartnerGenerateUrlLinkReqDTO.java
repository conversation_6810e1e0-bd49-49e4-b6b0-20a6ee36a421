package com.ly.travel.car.im.integration.request.generatelink;

import lombok.Data;

import java.io.Serializable;

@Data
public class PartnerGenerateUrlLinkReqDTO implements Serializable {

    private static final long serialVersionUID = 4167948477820561578L;

    /**
     * 通过 URL Link 进入的小程序页面路径，必须是已经发布的小程序存在的页面，不可携带 query 。path 为空时会跳转小程序主页
     */
    private String path;

    /**
     * 通过 URL Link 进入小程序时的query，最大1024个字符，只支持数字，大小写英文以及部分特殊字符：!#$&'()*+,/:;=?@-._~%
     */
    private String query;

    /**
     * 到期失效的URL Link的失效间隔天数。生成的到期失效URL Link在该间隔时间到达前有效。最长间隔天数为30天。expire_type 为 1 必填
     */
    private Integer expire_interval;

    /**
     * 乘车呗提供的固定token
     */
    private String partnerId;
}
