package com.ly.travel.car.im.integration.client;

import com.google.common.base.Function;
import com.ly.sof.api.client.SpringRestTemplateClient;
import com.ly.sof.api.client.converter.StringHttpCharsetMessageConverter;
import com.ly.sof.api.exception.LYException;
import com.ly.sof.spi.context.SOFContext;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.im.integration.throwable.error.IntegrationErrorFactory;
import com.ly.travel.car.im.integration.throwable.exception.IntegrationException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.Serializable;
import java.lang.invoke.SerializedLambda;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;

/**
 * 基础客户端
 *
 * <AUTHOR>
 * @version v0.1 2024年04月23日 16:24 lichunbo Exp $
 */
@Slf4j
@Service("baseClient")
public class BaseClient implements InitializingBean {

    /**
     * 错误工厂
     */
    protected final IntegrationErrorFactory errorFactory = IntegrationErrorFactory.getInstance();
    @Resource
    protected SOFContext sofContext;
    @Resource
    protected SpringRestTemplateClient springRestTemplateClient;
    @Resource
    protected RestTemplate restTemplate;

    @Override
    public void afterPropertiesSet() throws Exception {
        restTemplate.getMessageConverters().add(1, new StringHttpCharsetMessageConverter(StandardCharsets.UTF_8));
    }

    public void setSpringRestTemplateClient(SpringRestTemplateClient springRestTemplateClient) {
        this.springRestTemplateClient = springRestTemplateClient;
    }

    public void setRestTemplate(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    /**
     * 调用dsf接口通用模板方法。
     *
     * @param callFunction dsf的方法。
     * @param request      请求参数
     * @param success      判断是否成功的方法
     * @param message      获取异常message的方法
     * @param <Req>        请求参数类型
     * @param <Resp>       返回参数类型。
     * @return dsf的返回结果
     * @throws IntegrationException 返回值为空或success为false抛异常，或者调用dsf抛异常。
     */
    public <Req, Resp> Resp call(CallFunction<Req, Resp> callFunction, Req request, Function<Resp, Boolean> success, Function<Resp, String> message) throws IntegrationException {
        String name = getMethodName(callFunction);
        try {
            LoggerUtils.info(log, "{}请求参数，{}", name, FastJsonUtils.toJSONString(request));
            final Resp call = callFunction.call(request);
            LoggerUtils.info(log, "{}返回结果，{}", name, FastJsonUtils.toJSONString(call));
            if (call == null) {
                throw new IntegrationException(errorFactory.queryError(name));
            }
            if (!success.apply(call)) {
                throw new IntegrationException(errorFactory.interfaceCallError(message.apply(call)));
            }
            return call;
        } catch (IntegrationException e) {
            throw e;
        } catch (LYException e) {
            throw new IntegrationException(e.getError(), e);
        } catch (Exception e) {
            LoggerUtils.error(log, "{}异常", e, name);
            throw new IntegrationException(e);
        }
    }

    @FunctionalInterface
    public interface CallFunction<Req, Resp> extends Serializable {
        /**
         * 定义一个函数声明，参考：java.util.function.Function;
         *
         * @param source
         * @return
         * @throws Exception
         */
        Resp call(Req source) throws Exception;
    }

    /**
     * 获取lambda表达式的入口调用类和方法名称构造日志。
     *
     * @param serializable lambda表达式。
     * @return 类名[方法名]
     */
    public String getMethodName(Serializable serializable) {
        String result = serializable.getClass().getName();
        Method writeReplaceMethod = null;
        try {
            writeReplaceMethod = serializable.getClass().getDeclaredMethod("writeReplace");
            writeReplaceMethod.setAccessible(true);
            SerializedLambda serializedLambda = (SerializedLambda) writeReplaceMethod.invoke(serializable);
            result = StringUtils.substringAfterLast(serializedLambda.getCapturingClass(), "/") + "[" + serializedLambda.getImplMethodName() + "]";
        } catch (Exception e) {
            LoggerUtils.error(log, "获取lambda的方法名异常", e);
        }
        return result;
    }

}
