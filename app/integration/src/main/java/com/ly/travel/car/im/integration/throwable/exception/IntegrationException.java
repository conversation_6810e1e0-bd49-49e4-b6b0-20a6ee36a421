package com.ly.travel.car.im.integration.throwable.exception;

import com.ly.sof.api.error.LYError;
import com.ly.sof.api.exception.LYException;


/**
 * Integration层错误Exception
 *
 * <AUTHOR>
 * @version v0.1 2024年04月25日 10:55 lichunbo Exp $
 */
public class IntegrationException extends LYException {
    /**
     * 构造方法
     *
     * @param error 错误实例
     */
    public IntegrationException(LYError error) {
        super(error);
    }

    /**
     * 构造方法
     *
     * @param error 错误实例
     * @param cause 异常
     */
    public IntegrationException(LYError error, Throwable cause) {
        super(error, cause);
    }

    /**
     * 构造方法
     *
     * @param cause 异常
     */
    public IntegrationException(Throwable cause) {
        super(cause);
    }
}
