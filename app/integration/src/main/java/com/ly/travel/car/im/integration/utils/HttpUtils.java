package com.ly.travel.car.im.integration.utils;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultHttpRequestRetryHandler;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicHeader;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLContext;
import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.nio.Buffer;
import java.nio.charset.Charset;
import java.security.*;
import java.security.cert.CertificateException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class HttpUtils {
    private static Logger logger = LoggerFactory.getLogger(HttpUtils.class);

    HttpUtils() {
    }

    // 设置请求和传输超时时间
    private static final RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(5000)
            .setConnectTimeout(3000).setConnectionRequestTimeout(3000).build();

    private static PoolingHttpClientConnectionManager phccm = new PoolingHttpClientConnectionManager();

    public static final String CONTENT_TYPE_TEXT = "text";

    // HTTP请求器
    private static CloseableHttpClient closeableHttpClient;

    /**
     * HTTP GET
     *
     * @param url
     * @return
     */
    public static String get(String url) {
        return get(url, null, null);
    }

    /**
     * HTTP GET请求
     *
     * @param url
     * @param headers
     * @param charset
     * @return
     */
    public static String get(String url, Map<String, String> headers, String charset) {
        if (charset == null || charset.trim().isEmpty()) {
            charset = "UTF-8";
        }

        CloseableHttpResponse response = null;
        HttpEntity entity = null;
        // 连接池最大并发连接数
        phccm.setMaxTotal(800);
        // 单路由最大并发数
        phccm.setDefaultMaxPerRoute(400);

        CloseableHttpClient httpclient = HttpClients.custom().setDefaultRequestConfig(requestConfig)
                .setConnectionManager(phccm).setRetryHandler(new DefaultHttpRequestRetryHandler()).build();

        HttpGet httpGet = new HttpGet(url);

        // 设置HTTP头信息
        if (headers != null && !headers.isEmpty()) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                Header header = new BasicHeader(entry.getKey(), entry.getValue());
                httpGet.addHeader(header);
            }
        } else {
            Header header = new BasicHeader("User-Agent",
                    "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/43.0.2357.134 Safari/537.36");
            httpGet.addHeader(header);
        }

        try {
            response = httpclient.execute(httpGet);
            entity = response.getEntity();

            return EntityUtils.toString(entity, charset);
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
        } finally {
            if (entity != null) {
                try {
                    EntityUtils.consume(entity);
                } catch (IOException e) {
                    logger.error(e.getMessage(), e);
                }
            }

            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                    logger.error("", e);
                }
            }

            httpGet.releaseConnection();
        }

        return null;
    }

    public static String getOptimize(String url, Map<String, String> headers, String charset) {
        if (charset == null || charset.trim().isEmpty()) {
            charset = "UTF-8";
        }

        CloseableHttpResponse response = null;
        HttpEntity entity = null;
        // 连接池最大并发连接数
        phccm.setMaxTotal(800);
        // 单路由最大并发数
        phccm.setDefaultMaxPerRoute(400);

        CloseableHttpClient httpclient = HttpClients.custom().setDefaultRequestConfig(requestConfig)
                .setConnectionManager(phccm).setRetryHandler(new DefaultHttpRequestRetryHandler()).build();

        HttpGet httpGet = new HttpGet(url);

        // 设置HTTP头信息
        if (headers != null && !headers.isEmpty()) {
            Header headerBase = new BasicHeader("User-Agent",
                    "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/43.0.2357.134 Safari/537.36");
            httpGet.addHeader(headerBase);
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                Header header = new BasicHeader(entry.getKey(), entry.getValue());
                httpGet.addHeader(header);
            }
        } else {
            Header header = new BasicHeader("User-Agent",
                    "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/43.0.2357.134 Safari/537.36");
            httpGet.addHeader(header);
        }

        try {
            response = httpclient.execute(httpGet);
            entity = response.getEntity();

            return EntityUtils.toString(entity, charset);
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
        } finally {
            if (entity != null) {
                try {
                    EntityUtils.consume(entity);
                } catch (IOException e) {
                    logger.error(e.getMessage(), e);
                }
            }

            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                    logger.error("", e);
                }
            }

            httpGet.releaseConnection();
        }

        return null;
    }


    /**
     * HTTP POST 请求
     *
     * @param url
     * @param formData
     * @return
     */
    public static String post(String url, Map<String, String> formData) {
        return post(url, null, formData, null);
    }

    /**
     * HTTP POST 请求
     *
     * @param url
     * @param reqBodyStr
     * @return
     */
    public static String post(String url, String reqBodyStr) {
        return post(url, null, reqBodyStr, null, StringUtils.EMPTY);
    }

    /**
     * @param url
     * @param reqBodyStr
     * @param contentType
     * @return
     */
    public static String post(String url, String reqBodyStr, String contentType) {
        return post(url, null, reqBodyStr, null, contentType);
    }

    /**
     * HTTP POST请求
     *
     * @param url
     * @param headers
     * @param formData
     * @param charset
     * @return
     */
    public static String post(String url, Map<String, String> headers, Map<String, String> formData, String charset) {
        if (charset == null || charset.trim().isEmpty()) {
            charset = "UTF-8";
        }

        CloseableHttpResponse response = null;
        HttpEntity entity = null;
        // 连接池最大并发连接数
        phccm.setMaxTotal(800);
        // 单路由最大并发数
        phccm.setDefaultMaxPerRoute(400);

        CloseableHttpClient httpclient = HttpClients.custom().setDefaultRequestConfig(requestConfig)
                .setConnectionManager(phccm).setRetryHandler(new DefaultHttpRequestRetryHandler()).build();

        HttpPost httpPost = new HttpPost(url);

        // 设置HTTP头信息
        if (headers != null && !headers.isEmpty()) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                Header header = new BasicHeader(entry.getKey(), entry.getValue());
                httpPost.addHeader(header);
            }
        } else {
            Header header = new BasicHeader("User-Agent",
                    "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/43.0.2357.134 Safari/537.36");
            httpPost.addHeader(header);
        }

        // 设置参数
        if (formData != null && !formData.isEmpty()) {
            List<BasicNameValuePair> params = new ArrayList<>();
            for (Map.Entry<String, String> entry : formData.entrySet()) {
                params.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
            }

            try {
                httpPost.setEntity(new UrlEncodedFormEntity(params));
            } catch (UnsupportedEncodingException e) {
                logger.error(e.getMessage(), e);
            }
        }

        try {
            response = httpclient.execute(httpPost);
            entity = response.getEntity();

            return EntityUtils.toString(entity, charset);
        } catch (IOException e) {
            logger.error("", e);
        } finally {
            if (entity != null) {
                try {
                    EntityUtils.consume(entity);
                } catch (IOException e) {
                    logger.error("", e);
                }
            }

            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                    logger.error("", e);
                }
            }

            httpPost.releaseConnection();
        }

        return null;

    }

    /**
     * HTTP POST请求
     *
     * @param url
     * @param headers
     * @param reqBodyStr
     * @param charset
     * @param contentType
     * @return
     */
    public static String post(String url, Map<String, String> headers, String reqBodyStr, String charset, String contentType) {
        if (charset == null || charset.trim().isEmpty()) {
            charset = "UTF-8";
        }

        CloseableHttpResponse response = null;
        HttpEntity entity = null;

        if (StringUtils.isEmpty(contentType)) {
            contentType = "application/json";
        }
        // 连接池最大并发连接数
        phccm.setMaxTotal(800);
        // 单路由最大并发数
        phccm.setDefaultMaxPerRoute(400);

        CloseableHttpClient httpclient = HttpClients.custom().setDefaultRequestConfig(requestConfig)
                .setConnectionManager(phccm).setRetryHandler(new DefaultHttpRequestRetryHandler()).build();

        HttpPost httpPost = new HttpPost(url);

        // 设置HTTP头信息
        if (headers != null && !headers.isEmpty()) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                Header header = new BasicHeader(entry.getKey(), entry.getValue());
                httpPost.addHeader(header);
            }
        } else {
            Header header = new BasicHeader("User-Agent",
                    "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/43.0.2357.134 Safari/537.36");
            httpPost.addHeader(header);
        }

        StringEntity reqBodyParams = new StringEntity(reqBodyStr, charset);
        reqBodyParams.setContentType(contentType);
        httpPost.setEntity(reqBodyParams);

        try {
            response = httpclient.execute(httpPost);
            entity = response.getEntity();
            return EntityUtils.toString(entity, charset);
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
        } finally {
            if (entity != null) {
                try {
                    EntityUtils.consume(entity);
                } catch (IOException e) {
                    logger.error(e.getMessage(), e);
                }
            }

            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                    logger.error(e.getMessage(), e);
                }
            }

            httpPost.releaseConnection();
        }

        return null;
    }

    /**
     * 向指定 URL 发送POST方法的请求
     *
     * @param url   发送请求的 URL
     * @param param 请求参数，请求参数应该是 name1=value1&name2=value2 的形式。
     * @return 所代表远程资源的响应结果
     */
    public static String sendPost(String url, String param) {
        String result = "";
        try {
            URL realUrl = new URL(url);
            // 打开和URL之间的连接
            URLConnection conn = realUrl.openConnection();
            // 设置通用的请求属性
            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);
            // 获取URLConnection对象对应的输出流
            try (PrintWriter out = new PrintWriter(conn.getOutputStream());) {
                // 发送请求参数
                out.print(param);
                // flush输出流的缓冲
                out.flush();
            }
            // 定义BufferedReader输入流来读取URL的响应
            try (BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream(), "GBK"))) {
                StringBuilder resultSb = new StringBuilder();
                String line;
                while ((line = in.readLine()) != null) {
                    resultSb.append(line);
                }
                result = resultSb.toString();
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * 通过Https往API post xml数据
     *
     * @param url         API地址
     * @param xmlObj      要提交的XML数据对象
     * @param mchId       证书密码，默认为商户ID
     * @param inputStream 文件流
     * @return
     */
    public static String postData(String url, String xmlObj, String mchId, InputStream inputStream) {
        // 加载证书
        String result = null;
        try {
            initCert(mchId, inputStream);
        } catch (Exception e) {
            logger.error("initCert error：", e);
            return result;
        }
        HttpPost httpPost = new HttpPost(url);
        // 得指明使用UTF-8编码，否则到API服务器XML的中文不能被成功识别
        StringEntity postEntity = new StringEntity(xmlObj, "UTF-8");
        httpPost.addHeader("Content-Type", "text/xml");
        httpPost.setEntity(postEntity);
        // 设置请求器的配置
        httpPost.setConfig(requestConfig);
        HttpResponse response = null;
        try {
            response = closeableHttpClient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            result = EntityUtils.toString(entity, "UTF-8");
        } catch (IOException e) {
            logger.error("http execute error：", e);
        } finally {
            httpPost.abort();
        }
        return result;
    }

    /**
     * 加载证书
     *
     * @param mchId       商户号
     * @param inputStream 文件流
     * @throws IOException
     * @throws CertificateException
     * @throws NoSuchAlgorithmException
     * @throws KeyStoreException
     * @throws UnrecoverableKeyException
     * @throws KeyManagementException
     */
    private static void initCert(String mchId, InputStream inputStream) throws NoSuchAlgorithmException, CertificateException, IOException, KeyStoreException, KeyManagementException, UnrecoverableKeyException {
        // 证书密码，默认为商户ID
        String key = mchId;

        // 指定读取证书格式为PKCS12
        KeyStore keyStore = KeyStore.getInstance("PKCS12");

        try {
            // 指定PKCS12的密码(商户ID)
            keyStore.load(inputStream, key.toCharArray());
        } finally {
            inputStream.close();
        }
        SSLContext sslcontext = SSLContexts.custom().loadKeyMaterial(keyStore, key.toCharArray()).build();
        // 指定TLS版本
        SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslcontext, new String[]{"TLSv1"}, null,
                SSLConnectionSocketFactory.getDefaultHostnameVerifier());
        // 设置httpclient的SSLSocketFactory
        closeableHttpClient = HttpClients.custom().setSSLSocketFactory(sslsf).build();
    }


    /* 发送 post请求 用HTTPclient 发送请求*/
    public static byte[] wxpost(String URL, String json) {
        String obj = null;
        InputStream inputStream = null;
        Buffer reader = null;
        byte[] data = null;
        // 创建默认的httpClient实例.
        CloseableHttpClient httpclient = HttpClients.createDefault();
        // 创建httppost
        HttpPost httppost = new HttpPost(URL);
        httppost.addHeader("Content-type", "application/json; charset=utf-8");
        try {
            StringEntity s = new StringEntity(json, Charset.forName("UTF-8"));
            s.setContentEncoding("UTF-8");
            s.setContentType("image/png");
            httppost.setEntity(s);
            CloseableHttpResponse response = httpclient.execute(httppost);
            try {
                // 获取相应实体
                HttpEntity entity = response.getEntity();
                if (entity != null) {
                    inputStream = entity.getContent();
                    data = readInputStream(inputStream);
                }
                return data;
            } finally {
                response.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 关闭连接,释放资源
            try {
                httpclient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return data;
    }

    /**
     * 将流 保存为数据数组
     *
     * @param inStream
     * @return
     * @throws Exception
     */
    public static byte[] readInputStream(InputStream inStream) throws Exception {
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        // 创建一个Buffer字符串
        byte[] buffer = new byte[1024];
        // 每次读取的字符串长度，如果为-1，代表全部读取完毕
        int len = 0;
        // 使用一个输入流从buffer里把数据读取出来
        while ((len = inStream.read(buffer)) != -1) {
            // 用输出流往buffer里写入数据，中间参数代表从哪个位置开始读，len代表读取的长度
            outStream.write(buffer, 0, len);
            outStream.flush();
        }
        // 关闭输入流
        inStream.close();
        // 把outStream里的数据写入内存
        return outStream.toByteArray();
    }

    public static String postJson(String url, Object request, int timeout, TimeUnit timeUnit) {
        // 使用空的headers调用带有headers参数的方法
        return postJson(url, request, null, timeout, timeUnit);
    }

    /**
     * 发送JSON请求并设置自定义请求头
     * 
     * @param url 请求地址
     * @param request 请求对象，将被转换为JSON
     * @param headers 自定义请求头
     * @param timeout 超时时间
     * @param timeUnit 时间单位
     * @return 响应字符串
     */
    public static String postJson(String url, Object request, Map<String, String> headers, int timeout, TimeUnit timeUnit) {
        if (url == null || url.trim().isEmpty()) {
            logger.error("URL不能为空");
            return null;
        }

        CloseableHttpResponse response = null;
        HttpEntity entity = null;
        String charset = "UTF-8";
        String contentType = "application/json";

        // 设置自定义超时时间
        RequestConfig customRequestConfig = RequestConfig.custom()
                .setSocketTimeout((int) timeUnit.toMillis(timeout))
                .setConnectTimeout((int) timeUnit.toMillis(timeout))
                .setConnectionRequestTimeout((int) timeUnit.toMillis(timeout))
                .build();

        // 连接池最大并发连接数
        phccm.setMaxTotal(800);
        // 单路由最大并发数
        phccm.setDefaultMaxPerRoute(400);

        CloseableHttpClient httpclient = HttpClients.custom()
                .setDefaultRequestConfig(customRequestConfig)
                .setConnectionManager(phccm)
                .setRetryHandler(new DefaultHttpRequestRetryHandler())
                .build();

        HttpPost httpPost = new HttpPost(url);

        // 设置HTTP头信息
        if (headers != null && !headers.isEmpty()) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                Header header = new BasicHeader(entry.getKey(), entry.getValue());
                httpPost.addHeader(header);
            }
        } else {
            // 设置默认的HTTP头信息
            Header header = new BasicHeader("User-Agent",
                    "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/43.0.2357.134 Safari/537.36");
            httpPost.addHeader(header);
        }
        
        // 确保Content-Type头被设置
        boolean hasContentType = false;
        if (headers != null) {
            for (String key : headers.keySet()) {
                if ("Content-Type".equalsIgnoreCase(key)) {
                    hasContentType = true;
                    break;
                }
            }
        }
        
        if (!hasContentType) {
            httpPost.addHeader("Content-Type", contentType + "; charset=" + charset);
        }

        try {
            // 将对象转换为JSON字符串
            String jsonString = com.ly.sof.utils.mapping.FastJsonUtils.toJSONString(request);
            logger.info("POST JSON请求内容: {}", jsonString);
            
            // 设置请求体，不在StringEntity中设置contentType，避免与HTTP头冲突
            StringEntity reqBodyParams = new StringEntity(jsonString, charset);
            httpPost.setEntity(reqBodyParams);

            // 执行请求
            response = httpclient.execute(httpPost);
            entity = response.getEntity();
            
            // 获取响应状态码
            int statusCode = response.getStatusLine().getStatusCode();
            String responseBody = EntityUtils.toString(entity, charset);
            
            // 记录响应信息
            logger.info("POST JSON响应状态码: {}, 响应内容: {}", statusCode, responseBody);
            
            // 如果状态码不是2xx，则记录警告
            if (statusCode < 200 || statusCode >= 300) {
                logger.warn("POST JSON请求响应状态码异常: {}, URL: {}", statusCode, url);
            }
            
            return responseBody;
        } catch (Exception e) {
            logger.error("POST JSON请求异常, URL: " + url, e);
        } finally {
            // 释放资源
            if (entity != null) {
                try {
                    EntityUtils.consume(entity);
                } catch (IOException e) {
                    logger.error("释放HttpEntity资源异常", e);
                }
            }

            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                    logger.error("关闭HttpResponse异常", e);
                }
            }

            httpPost.releaseConnection();
        }

        return null;
    }
    
    /**
     * 使用自定义请求头发送JSON请求，使用默认超时时间
     * 
     * @param url 请求地址
     * @param request 请求对象，将被转换为JSON
     * @param headers 自定义请求头
     * @return 响应字符串
     */
    public static String postJson(String url, Object request, Map<String, String> headers) {
        // 默认使用5秒超时
        return postJson(url, request, headers, 5, TimeUnit.SECONDS);
    }

    /**
     * 使用默认超时时间发送JSON请求
     * 
     * @param url 请求地址
     * @param request 请求对象，将被转换为JSON
     * @return 响应字符串
     */
    public static String postJson(String url, Object request) {
        // 默认使用5秒超时
        return postJson(url, request, 5, TimeUnit.SECONDS);
    }
}
