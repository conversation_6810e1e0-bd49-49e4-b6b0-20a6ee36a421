package com.ly.travel.car.im.integration.client.api;

import com.ly.car.http.config.HttpClientProperties;
import com.ly.car.utils.HttpUtils;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.im.integration.request.RiskQueryReqDTO;
import com.ly.travel.car.im.integration.response.RiskQueryRspDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Component
public class RiskApi {

    @Value("${config.risk.url}")
    private String riskUrl;

    private static CloseableHttpClient httpClient = getComponentsConfig();

    public static CloseableHttpClient getComponentsConfig() {
        HttpClientProperties.ComponentsConfig componentsConfig = new HttpClientProperties.ComponentsConfig();
        componentsConfig.setConnectTimeout(500);
        componentsConfig.setSocketTimeout(500);
        componentsConfig.setConnectionRequestTimeout(500);
        return HttpUtils.createHttpClient(componentsConfig);
    }

    /**
     * 是否被风控判断
     *
     * @param rsp
     * @return
     */
    public Boolean riskJudge(RiskQueryRspDTO rsp) {
        if (Objects.nonNull(rsp) && rsp.getSuccess() && Objects.nonNull(rsp.getData())
                && Objects.nonNull(rsp.getData().getCode()) && !rsp.getData().getCode().equals(0)) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 风控查询
     *
     * @param req
     * @return
     */
    public RiskQueryRspDTO riskQuery(RiskQueryReqDTO req) {
        String orderId = req.getOrderId();
        try {
            String url = riskUrl + "/risk/query";
            String requestBody = FastJsonUtils.toJSONString(req);
            LoggerUtils.info(log, "[riskQuery][{}] 风控查询， url：{}  req：{}", orderId, url, requestBody);
            String responseBody = HttpUtils.post(httpClient, url, requestBody);
            LoggerUtils.info(log, "[riskQuery][{}] 风控查询，rsp：{}", orderId, responseBody);
            return FastJsonUtils.fromJSONString(responseBody, RiskQueryRspDTO.class);
        } catch (Exception e) {
            LoggerUtils.error(log, "[riskQuery][{}]  风控查询异常", orderId, e);
            return null;
        }
    }
}
