package com.ly.travel.car.im.integration.throwable.error;

import com.ly.sof.api.error.AbstractErrorFactory;
import com.ly.sof.api.error.LYError;
import org.apache.commons.lang3.StringUtils;

/**
 * Integration 层错误工厂
 *
 * <AUTHOR>
 * @version v0.1 2024年04月25日 10:55 lichunbo Exp $
 */
public class IntegrationErrorFactory extends AbstractErrorFactory {

    /**
     * 获取IntegrationErrorFactory单例
     *
     * @return IntegrationErrorFactory单例
     */
    public static IntegrationErrorFactory getInstance() {
        return IntegrationErrorFactoryHolder.INSTANCE;
    }

    /**
     * 获取错误消息资源文件名称，资源文件名称是不包含[error-messages]后缀，和文件扩展类型
     * 通过提供的错误消息资源文件名称，消息工厂计算出资源文件完整路径
     * <p>
     * 资源文件路径：
     * <code>META-INF/messages/bundleName-error-messages.properties</code>
     *
     * @return 错误消息资源文件名称
     */
    @Override
    protected String provideErrorBundleName() {
        return "integration";
    }

    public LYError responseError(String message) {
        return createError("LY0521801044", message);
    }

    /**
     * 单例实现
     *
     * <AUTHOR>
     * @version $Id: IntegrationErrorFactory.java, v 0.1 2016下午9:06:22 llf32758 Exp $
     */
    private static final class IntegrationErrorFactoryHolder {
        /**
         * instance
         */
        private static final IntegrationErrorFactory INSTANCE = new IntegrationErrorFactory();
    }

    /**
     * {0}不能为空
     *
     * @return
     */
    public LYError paramNull(Object obj) {
        return createError("LY0521801001", obj);
    }

    /**
     * 查询{}接口失败
     *
     * @param message the message
     * @return ly error
     */
    public LYError queryError(String message) {
        return createError("LY0521801002", message);
    }

    /**
     * LY0521801003=调用接口失败,接口返回错误码:{},异常信息:{}
     *
     * @param code
     * @param errorMsg
     * @return
     */
    public LYError invokeReturnError(String code, String errorMsg) {
        return createError("LY0521801005", code, errorMsg);
    }

    /**
     * LY0521801004=调用接口失败,异常信息:{0}.
     *
     * @param errorMsg 接口返回的错误信息.
     * @return ly error.
     */
    public LYError interfaceCallError(String errorMsg) {
        if (StringUtils.isBlank(errorMsg)) {
            return createError("LY0521801003");
        }
        return createError("LY0521801004", errorMsg);
    }

    /**
     * 接口调用返回空 LY0521801006
     *
     * @return {@link LYError}
     */
    public LYError invokeReturnNull(String methodName) {
        return createError("LY0521801006", methodName);
    }
}
