package com.ly.travel.car.im.integration.client.api;

import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.spat.dsf.client.v2.DSFProxy;
import com.ly.spat.dsf.client.v2.HttpService;
import com.ly.spat.dsf.utils.StringUtils;
import com.ly.travel.car.im.integration.client.redis.impl.RedisClientProxy;
import com.ly.travel.car.im.integration.request.SmsReqDTO;
import com.ly.travel.car.im.integration.response.SmsRspDTO;
import com.ly.travel.car.im.integration.utils.RedisKeyBuilder;
import com.ly.travel.car.im.model.dto.OrderDetailDTO;
import com.ly.travel.car.im.model.enums.GenerateLinkTypeEnum;
import com.ly.travel.car.im.model.enums.OrderChannelEnum;
import com.ly.travel.car.im.model.utils.DateToolsUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class SmsApi {
    @Resource
    private GenerateLinkApi generateLinkApi;
    @Resource
    private RedisClientProxy redisClientProxy;

    @Value("${config.sms-link.wxLink}")
    private String wxLink;

    @Value("${config.company-link.wxLink}")
    private String companyWxLink;

    @Value("${config.push.appJumpUrl}")
    private String appJumpUrl;

    @Value("${config.msg.send.account}")
    private String account;

    @Value("${config.msg.send.password}")
    private String password;

    public SmsRspDTO send(SmsReqDTO smsReqDTO) {
        Map<String, Object> params = this.makeParams(smsReqDTO);
        LoggerUtils.info(log, "[send] 乘客未读发送短信req：{}", FastJsonUtils.toJSONString(params));
        HttpService service = DSFProxy.newInstance().getService("dsf.smsTemplateService", "smsService", "latest");
        String result = service.action("send", String.class, params, null);
        LoggerUtils.info(log, "[send] 乘客未读发送短信rsp：{}", result);
        return FastJsonUtils.fromJSONString(result, SmsRspDTO.class);
    }

    public Map<String, Object> makeParams(SmsReqDTO smsReqDTO) {
        Map<String, Object> params = new HashMap<>();
        params.put("Action", "CreateAndSend");
        params.put("IsMass", "0");
        params.put("Mobile", smsReqDTO.getMobile());
        params.put("IsGlobal", smsReqDTO.getIsGlobal());
        params.put("NodeId", smsReqDTO.getNodeId());
        Map<String, String> inputParameters = smsReqDTO.getInputParameters();
        if (!smsReqDTO.getIsHidePrefix()) {
            inputParameters.put("@SmsOrderSource", "2");
        }
        params.put("InputParameters", inputParameters);
        params.put("GlobalNumber", smsReqDTO.getGlobalNumber());
        params.put("Account", account);
        params.put("Password", password);
        params.put("PlatformId", StringUtils.isEmpty(smsReqDTO.getPlatformId()) ? "4" : smsReqDTO.getPlatformId());
        return params;
    }

    /**
     * @param order
     * @return -1:订单渠道不支持发短信；
     * ""空字符串，发短信但没有链接
     */
    public String generateLinkApi(OrderDetailDTO order, int type) {
        if (Objects.isNull(order)) {
            return "-1";
        }

        String orderId = order.getOrderId();
        List<String> orderTags = order.getOrderTags();
        boolean distribution = CollectionUtils.isNotEmpty(orderTags) && orderTags.contains("DISTRIBUTION_ORDER");
        if (distribution) {
            return "-1";
        }

        LoggerUtils.info(log, "[generateLinkApi][{}] distributionInfo: {}", orderId, FastJsonUtils.toJSONString(order));

        //小程序
        if (OrderChannelEnum.isXcx(order.getChannelType())) {
            //todo:前提生成的wxLink是固定的，若是动态的请修改
            String cacheKey = RedisKeyBuilder.IM_DIALOGUE_LIST_KEY + type;
            String wxUrlLink = redisClientProxy.getString(cacheKey);
            LoggerUtils.info(log, "[generateLinkApi][{}] wxUrlLink: {}", orderId, wxUrlLink);
            if (StringUtils.isEmpty(wxUrlLink)) {
                String wxLinkUrl = getWxLinkUrl(type);
                LoggerUtils.info(log, "[generateLinkApi][{}] wxLinkUrl: {}", orderId, wxLinkUrl);
                wxUrlLink = generateLinkApi.wxUrlLink(orderId, wxLinkUrl);
                if (StringUtils.isNotEmpty(wxUrlLink)) {
                    redisClientProxy.save(cacheKey, wxUrlLink, DateToolsUtils.getLeftTimeOfToday());
                } else {
                    return Strings.EMPTY;
                }
            }

            String shortUrl = generateLinkApi.shortLink(wxUrlLink, orderId);
            if (Objects.isNull(shortUrl)) {
                return Strings.EMPTY;
            }
            return shortUrl;
        }

        //APP
        if (OrderChannelEnum.isApp(order.getChannelType())) {
            String appLinkUrl = getAppLinkUrl(type);
            if (StringUtils.isEmpty(appLinkUrl)) {
                return "-1";
            }

            String shortUrl = generateLinkApi.shortLink(appLinkUrl, orderId);
            if (Objects.isNull(shortUrl)) {
                return "-1";
            }
            return shortUrl;
        }
        return "-1";
    }

    /**
     * 获取wx渠道refid
     */
    private String getWxLinkUrl(int type) {
        if (GenerateLinkTypeEnum.SMS.getType() == type) {
            return wxLink;
        } else if (GenerateLinkTypeEnum.COMPANY_WX.getType() == type) {
            return companyWxLink;
        }
        return Strings.EMPTY;
    }

    /**
     * 获取wx渠道link,refid不一样
     */
    private String getAppLinkUrl(int type) {
        try {
            if (GenerateLinkTypeEnum.SMS.getType() == type) {
                return URLEncoder.encode(appJumpUrl, "UTF-8");
            } else if (GenerateLinkTypeEnum.COMPANY_WX.getType() == type) {
                return Strings.EMPTY;
            }
        } catch (Exception e) {
            LoggerUtils.warn(log, "[getAppLinkUrl][][][] url编码失败");
        }
        return Strings.EMPTY;
    }
}
