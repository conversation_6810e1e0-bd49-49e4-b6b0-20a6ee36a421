package com.ly.travel.car.im.integration.client.supply.impl;

import com.ly.travel.car.im.integration.client.BaseClient;
import com.ly.travel.car.im.integration.client.supply.SupplierClient;
import com.ly.travel.car.im.integration.throwable.exception.IntegrationException;
import com.ly.travel.shared.mobility.supply.trade.core.facade.TradeFacade;
import com.ly.travel.shared.mobility.supply.trade.core.facade.trade.request.SupplierImCapacityRequest;
import com.ly.travel.shared.mobility.supply.trade.core.facade.trade.response.SupplierImCapacityResponse;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 供应链相关服务
 *
 * <AUTHOR>
 * @version v0.1 2024年05月25日 11:23 lichunbo Exp $
 */
@Service
public class SupplierClientImpl extends BaseClient implements SupplierClient {
    @Resource
    private TradeFacade tradeFacade;


    @Override
    public SupplierImCapacityResponse queryImCapacity(SupplierImCapacityRequest var1) throws IntegrationException {
        SupplierImCapacityResponse response = call(tradeFacade::queryImCapacity, var1, SupplierImCapacityResponse::isSuccess, SupplierImCapacityResponse::getErrorMessage);
        return response;
    }
}
