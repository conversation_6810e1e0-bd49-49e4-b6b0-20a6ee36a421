package com.ly.travel.car.im.integration.client.es.impl;

import com.alibaba.fastjson2.TypeReference;
import com.ly.travel.car.im.integration.client.AbstractEsClient;
import com.ly.travel.car.im.integration.client.es.ImDispatchClient;
import com.ly.travel.car.im.integration.client.es.common.ESBulkResponse;
import com.ly.travel.car.im.integration.client.es.common.ESSearchResponse;
import com.ly.travel.car.im.integration.client.es.imdispatch.ImDispatchSearchRequest;
import com.ly.travel.car.im.integration.client.es.imdispatch.ImDispatchToElasticSearchVO;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;


@Component
public class ImDispatchClientImpl extends AbstractEsClient implements ImDispatchClient {

    protected static final Map<String, String> headerMap = new HashMap<>();

    private static final String   ES_URL     = "http://es.dss.17usoft.com";
    private static final String   SEARCH_URL = "/index/%s/template/%s/%s/search";

    @Override
    protected String esPrefix() {
        return "im-delivery";
    }

    @Override
    protected Map<String, String> token() {
        if (MapUtils.isEmpty(headerMap)) {
            headerMap.put("Authentication", "f0110dc1-5ccb-4833-b8f4-31d55562871e");
        }
        return headerMap;
    }

    @Override
    protected String version() {
        return "1.0.1";
    }

    @Override
    public ESBulkResponse bulk(ImDispatchToElasticSearchVO vo) {
        return super.bulk(vo);
    }
    
    @Override
    public ESSearchResponse<ImDispatchToElasticSearchVO> queryByMsgId(String msgId) {
        // 创建查询请求
        ImDispatchSearchRequest request = new ImDispatchSearchRequest();
        request.setFrom(0);
        request.setSize(10);
        
        // 创建自定义查询条件
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("msgId", msgId);
        
        // 设置ES模板名称和版本
        String esName = String.join("-", esPrefix(), env());
        String templateName = "query"; // ES中定义的查询模板名称
        String version = version();
        
        // 执行查询
        TypeReference<ESSearchResponse<ImDispatchToElasticSearchVO>> typeReference = 
                new TypeReference<ESSearchResponse<ImDispatchToElasticSearchVO>>() {};
        
        return search(queryParams, typeReference, esName, templateName, version, token());
    }
}
