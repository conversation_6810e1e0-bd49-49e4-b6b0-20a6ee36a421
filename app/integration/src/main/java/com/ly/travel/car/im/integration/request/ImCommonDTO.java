package com.ly.travel.car.im.integration.request;

import com.ly.travel.car.im.facade.dto.BaseDTO;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

@Data
public class ImCommonDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -9221858126282032449L;

    /**
     * imKey
     */
    private String imKey;

    /**
     * 会话标识（供应商Code/子供应商Code + 会员ID/unionId + 司机ID）
     */
    private String sessionKey;

    /**
     * 司机车牌号
     */
    private String plateNumber;

    /**
     * 供应商Code
     */
    private String supplierCode;

    /**
     * 司机ID
     */
    private String driverId;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 供应商订单号
     */
    private String supplierOrderId;

    public String filter1() {
        return StringUtils.defaultIfBlank(super.getUnionId(), super.getMemberId());
    }
}
