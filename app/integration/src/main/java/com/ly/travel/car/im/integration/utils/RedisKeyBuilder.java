package com.ly.travel.car.im.integration.utils;

import com.ly.travel.car.im.model.utils.DateToolsUtils;
import org.slf4j.helpers.MessageFormatter;

import java.util.Date;

/**
 * <AUTHOR>
 * @version Id: RedisKeyBuilder, v 0.1 2024/06/05 14:01 lichunbo Exp $
 */
public class RedisKeyBuilder {

    /**
     * 消息接收锁
     */
    private static final String IM_RECEIVE_LOCK = "im:receive:lock:{}";

    /**
     * 订单锁
     */
    private static final String IM_ORDER_LOCK = "im:order:lock:{}";

    /**
     * 唯一标识锁
     */
    public static final String IM_SESSION_KEY_LOCK = "im:sessionKey:lock:{}";

    /**
     * 消息会话缓存key
     */
    public static final String IM_INFO_KEY = "im:info:key:";

    /**
     * 消息数量锁
     */
    public static final String IM_SMS_NUM_KEY = "im:sms:num:key:";

    public static final String IM_DISTRIBUTION_MSG_KEY = "im:distribution:msg:key:";

    /**
     * 订阅订单锁
     */
    public static final String IM_SUB_ORDER_LOCK = "im:sub:order:lock:{}";

    /**
     * 订单推送锁
     */
    public static final String IM_ORDER_PUSH_LOCK = "im:order:push:lock:{}";

    /**
     * 对话列表页缓存key
     */
    public static final String IM_DIALOGUE_LIST_KEY = "im:dialogue:list:key:";

    /**
     * 司机消息推送企微缓存key
     */
    public static final String IM_DRIVER_MSG_PUSH_KEY = "im:driver:msg:push:key:";

    /**
     * 司机消息订单ID缓存key
     */
    public static final String IM_DRIVER_MSG_ORDER_ID_KEY = "im:driver:msg:order:id:key:";

    /**
     * im_passenger_unread_push_tag
     */
    public static final String IM_PASSENGER_UNREAD_PUSH_TAG = "im_passenger_unread_push_tag_{0}_{1}";

    /**
     * 消息接收锁
     *
     * @param orderSerialNo
     * @return
     */
    public static String receiveLock(String orderSerialNo) {
        return MessageFormatter.format(IM_RECEIVE_LOCK, orderSerialNo).getMessage();
    }

    /**
     * 订单锁
     *
     * @param orderSerialNo
     * @return
     */
    public static String orderLock(String orderSerialNo) {
        return MessageFormatter.format(IM_ORDER_LOCK, orderSerialNo).getMessage();
    }

    /**
     * 唯一标识锁
     *
     * @param sessionKey
     * @return
     */
    public static String sessionKeyLock(String sessionKey) {
        return MessageFormatter.format(IM_SESSION_KEY_LOCK, sessionKey).getMessage();
    }

    /**
     * 订阅订单锁
     *
     * @param orderSerialNo
     * @return
     */
    public static String subOrderLock(String orderSerialNo) {
        return MessageFormatter.format(IM_SUB_ORDER_LOCK, orderSerialNo).getMessage();
    }

    /**
     * 订单推送锁
     *
     * @param orderSerialNo
     * @return
     */
    public static String orderPushLock(String orderSerialNo) {
        return MessageFormatter.format(IM_ORDER_PUSH_LOCK, orderSerialNo).getMessage();
    }

    /**
     * 乘客未读推送
     *
     * @param orderId
     * @return
     */
    public static String passengerUnreadPushTag(String orderId) {
        return MessageFormatter.format(IM_PASSENGER_UNREAD_PUSH_TAG, orderId, DateToolsUtils.toString(new Date(), "yyyyMMdd")).getMessage();
    }
}
