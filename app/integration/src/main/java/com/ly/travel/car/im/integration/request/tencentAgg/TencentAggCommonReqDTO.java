package com.ly.travel.car.im.integration.request.tencentAgg;

import lombok.Data;

import java.io.Serializable;

@Data
public class TencentAggCommonReqDTO implements Serializable {

    private static final long serialVersionUID = 4651692360052065399L;

    /**
     * 腾讯出行服务分配给服务商调用 accessKey
     */
    private String accessKey;

    /**
     * 腾讯出行服务分配给服务商的 id
     */
    private String spId;

    /**
     * 请求流水号,调用方自动生成一个随机 ID，
     * 建议使用 uuid
     */
    private String seqId;

    /**
     * 请求发送时的时间戳(unix 时间戳) 秒
     */
    private Long timestamp;

    /**
     * 10 位随机字符串
     */
    private String nonce;

    /**
     * 验证签名参数
     */
    private String sign;

    /**
     * 事件类型
     */
    private Integer eventType;

    /**
     * 腾讯订单号
     */
    private String orderId;

    /**
     * 服务商订单号
     */
    private String spOrderId;

    /**
     * 订单状态
     */
    private Integer status;

    /**
     * 车主订单号
     */
    private String driverOrderId;

    /**
     * 车主id
     */
    private String driverId;
}
