package com.ly.travel.car.im.integration.client.api;

import com.ly.car.bean.Simple;
import com.ly.sof.utils.log.LogContextUtils;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.im.integration.request.*;
import com.ly.travel.car.im.integration.utils.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PushApi {

    @Value("${config.car.robin.url}")
    private String carRobinUrl;

    /**
     * 推送司机消息
     */
    public Simple<?> driverMsgPush(DriverMsgPushReqDTO req) {
        String reqStr = FastJsonUtils.toJSONString(req);
        String imKey = req.getImKey();
        LoggerUtils.info(log, "[driverMsgPush][{}] 推送司机消息req：{}", imKey, reqStr);
        String rspStr = HttpUtils.post(carRobinUrl + "/chat/driverMsgPush", reqStr);
        LoggerUtils.info(log, "[driverMsgPush][{}] 推送司机消息rsp：{}", imKey, rspStr);
        return FastJsonUtils.fromJSONString(rspStr, Simple.class);
    }

    /**
     * 推送改派系统消息
     */
    public Simple<?> reassignmentMsgPush(ReassignmentMsgReqDTO req) {
        String reqStr = FastJsonUtils.toJSONString(req);
        String imKey = req.getImKey();
        LoggerUtils.info(log, "[reassignmentMsgPush][{}] 推送改派系统消息req：{}", imKey, reqStr);
        String rspStr = HttpUtils.post(carRobinUrl + "/chat/reassignmentMsgPush", reqStr);
        LoggerUtils.info(log, "[reassignmentMsgPush][{}] 推送改派系统响应rsp：{}", imKey, rspStr);
        return FastJsonUtils.fromJSONString(rspStr, Simple.class);
    }

    /**
     * 推送安全提醒系统消息
     */
    public Simple<?> safeWarnMsgPush(SafeWarnMsgReqDTO req) {
        LogContextUtils.setCategory("safeWarnMsgPush");
        LogContextUtils.setFilter1(req.getImKey());
        String reqStr = FastJsonUtils.toJSONString(req);
        LoggerUtils.info(log, "[推送安全提醒系统消息req：{}", reqStr);
        String rspStr = HttpUtils.post(carRobinUrl + "/chat/safeWarnMsgPush", reqStr);
        LoggerUtils.info(log, "推送安全提醒系统消息rsp：{}", rspStr);
        return FastJsonUtils.fromJSONString(rspStr, Simple.class);
    }

    /**
     * 推送夜间订单提醒系统消息
     */
    public Simple<?> safeNightMsgPush(SafeNightWarnMsgReqDTO req) {
        LogContextUtils.setCategory("safeNightMsgPush");
        LogContextUtils.setFilter1(req.getImKey());
        String reqStr = FastJsonUtils.toJSONString(req);
        String imKey = req.getImKey();
        LoggerUtils.info(log, "推送夜间订单提醒系统消息req：{}", reqStr);
        String rspStr = HttpUtils.post(carRobinUrl + "/chat/nightWarnMsgPush", reqStr);
        LoggerUtils.info(log, "推送夜间订单提醒系统消息rsp：{}", rspStr);
        return FastJsonUtils.fromJSONString(rspStr, Simple.class);
    }

    /**
     * 推送敏感词提醒系统消息
     */
    public Simple<?> sensitiveWordsMsgPush(SensitiveWordsMsgReqDTO req) {
        String reqStr = FastJsonUtils.toJSONString(req);
        String imKey = req.getImKey();
        LoggerUtils.info(log, "[sensitiveWordsMsgPush][{}] 推送敏感词提醒系统消息req：{}", imKey, reqStr);
        String rspStr = HttpUtils.post(carRobinUrl + "/chat/sensitiveWordsMsgPush", reqStr);
        LoggerUtils.info(log, "[sensitiveWordsMsgPush][{}] 推送敏感词提醒系统消息rsp：{}", imKey, rspStr);
        return FastJsonUtils.fromJSONString(rspStr, Simple.class);
    }

    /**
     * 推送提示公告系统消息
     */
    public Simple<?> tipsMsgPush(TipsMsgReqDTO req) {
        LogContextUtils.setCategory("tipsMsgPush");
        LogContextUtils.setFilter1(req.getImKey());
        String reqStr = FastJsonUtils.toJSONString(req);
        String imKey = req.getImKey();
        LoggerUtils.info(log, "推送提示公告系统消息req：{}", reqStr);
        String rspStr = HttpUtils.post(carRobinUrl + "/chat/tipsMsgPush", reqStr);
        LoggerUtils.info(log, "推送提示公告系统消息rsp：{}", rspStr);
        return FastJsonUtils.fromJSONString(rspStr, Simple.class);
    }
}
