package com.ly.travel.car.im.integration.request;

import com.ly.travel.car.im.facade.dto.MsgContentDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class DriverMsgPushReqDTO extends ImCommonDTO implements Serializable {

    private static final long serialVersionUID = 8131661514682511434L;

    /**
     * 消息唯一ID
     */
    private String msgId;

    /**
     * 消息类型 0-快捷消息 1-自定义文本 2-定位 3-语音 4-图片 5-卡片消息 6-系统消息 7-提示公告 8-已读
     */
    private Integer msgType;

    /**
     * 消息内容
     */
    private MsgContentDTO msgContent;

    /**
     * 已读消息ID集合
     */
    private List<String> msgIds;
}
