package com.ly.travel.car.im.integration.client.order;

import com.ly.travel.car.im.integration.throwable.exception.IntegrationException;
import com.ly.travel.car.orderservice.facade.request.order.OrderDetailRequest;
import com.ly.travel.car.orderservice.facade.response.order.OrderDetailResponse;
import com.ly.travel.shared.mobility.supply.order.core.facade.order.request.OrderRelationRequest;
import com.ly.travel.shared.mobility.supply.order.core.facade.order.request.QueryDispatchListRequest;
import com.ly.travel.shared.mobility.supply.order.core.facade.order.request.QueryDriverInfoRequest;
import com.ly.travel.shared.mobility.supply.order.core.facade.order.response.OrderRelationResponse;
import com.ly.travel.shared.mobility.supply.order.core.facade.order.response.QueryDispatchListResponse;
import com.ly.travel.shared.mobility.supply.order.core.facade.order.response.QueryDriverInfoResponse;

/**
 * 订单相关服务
 *
 * <AUTHOR>
 * @version v0.1 2024年05月25日 11:23 lichunbo Exp $
 */
public interface OrderClient {

    /**
     * 获取订单详情  -- 交易服务
     *
     * @param var1
     * @return
     * @throws IntegrationException
     */
    OrderDetailResponse psiDetail(OrderDetailRequest var1) throws IntegrationException;

    /**
     * 根据供应商订单号查询同程订单号  -- 供应链服务
     *
     * @param var1
     * @return
     * @throws IntegrationException
     */
    OrderRelationResponse orderRelationInfo(OrderRelationRequest var1) throws IntegrationException;

    /**
     * 获取订单司机车辆信息  -- 供应链服务
     *
     * @param var1
     * @return
     * @throws IntegrationException
     */
    QueryDriverInfoResponse queryDriverInfo(QueryDriverInfoRequest var1) throws IntegrationException;

    /**
     * 获取订单车辆调度信息  -- 供应链服务
     * @param request
     * @return
     */
    QueryDispatchListResponse queryDispatchList(QueryDispatchListRequest request) throws IntegrationException;


}
