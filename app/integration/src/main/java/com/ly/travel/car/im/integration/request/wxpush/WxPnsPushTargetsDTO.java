package com.ly.travel.car.im.integration.request.wxpush;

import lombok.Data;

@Data
public class WxPnsPushTargetsDTO {

    /**
     * 公众号openID
     * 发送以下消息时建议填写 gzh,gzhTpl,kefu,wxfw
     */
    private String gzhOpenId;

    /**
     * 微信订单号
     * 发送以下消息时必填 wxfw
     */
    private String transactionId;

    /**
     * 用户id
     */
    private String unionId;

    /**
     * 小程序openID
     * 发送以下消息时建议填写 xcx,xcxTpl,xcxSub
     */
    private String xcxOpenId;

    /**
     * 用户手机号
     * 发送企业微信消息时专用，与 unionid 二者至少传一个，都传时优先使用 unionId
     */
    private String mobile;
}
