package com.ly.travel.car.im.integration.client.es.common;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ESSearchResponse<T> implements Serializable {
    private static final long serialVersionUID = -3090823694654438465L;

    private Integer           code;
    private String            message;
    private Result<T>         result;

    @Data
    public static class Result<T> implements Serializable {

        private static final long serialVersionUID = -6157743535148279293L;

        private Integer           took;
        private Integer           count;
        private List<T>           list;
    }

}