package com.ly.travel.car.im.integration.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class BumblebeeRspDTO implements Serializable {

    private static final long serialVersionUID = 6697051303332412541L;

    /**
     * status : 200
     * message : 请求成功
     * body : {"trainlist":[{"projectName":"火车票","projectTag":"TRAIN","startCityId":"6324","startCityName":"姑苏区","startLatitude":"31.330798","startLongtitude":"120.610444","startAddress":"苏州站","startAddressDetail":"苏州站","startTerminal":null,"endCityId":"630","endCityName":"静安区","endLatitude":"31.249574","endLongtitude":"121.455708","endAddress":"上海站","endAddressDetail":"上海站","endTerminal":null,"startTime":"2023-05-03 05:50","endTime":"2023-05-03 08:01","orderId":"MT2023041918201219089213"}],"airlist":[{"projectName":"火车票","projectTag":"TRAIN","startCityId":"6324","startCityName":"姑苏区","startLatitude":"31.330798","startLongtitude":"120.610444","startAddress":"苏州站","startAddressDetail":"苏州站","startTerminal":null,"endCityId":"630","endCityName":"静安区","endLatitude":"31.249574","endLongtitude":"121.455708","endAddress":"上海站","endAddressDetail":"上海站","endTerminal":null,"startTime":"2023-05-03 05:50","endTime":"2023-05-03 08:01","orderId":"MT2023041918201219089213"}],"hotellist":[{"projectName":"火车票","projectTag":"TRAIN","startCityId":"6324","startCityName":"姑苏区","startLatitude":"31.330798","startLongtitude":"120.610444","startAddress":"苏州站","startAddressDetail":"苏州站","startTerminal":null,"endCityId":"630","endCityName":"静安区","endLatitude":"31.249574","endLongtitude":"121.455708","endAddress":"上海站","endAddressDetail":"上海站","endTerminal":null,"startTime":"2023-05-03 05:50","endTime":"2023-05-03 08:01","orderId":"MT2023041918201219089213"}]}
     */

    private int status;

    private String message;

    private BodyBean body;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public BodyBean getBody() {
        return body;
    }

    public void setBody(BodyBean body) {
        this.body = body;
    }

    public static class BodyBean {
        private List<TravelAssistantInfoDto> trainlist;
        private List<TravelAssistantInfoDto> airlist;
        private List<TravelAssistantInfoDto> hotellist;

        public List<TravelAssistantInfoDto> getTrainlist() {
            return trainlist;
        }

        public void setTrainlist(List<TravelAssistantInfoDto> trainlist) {
            this.trainlist = trainlist;
        }

        public List<TravelAssistantInfoDto> getAirlist() {
            return airlist;
        }

        public void setAirlist(List<TravelAssistantInfoDto> airlist) {
            this.airlist = airlist;
        }

        public List<TravelAssistantInfoDto> getHotellist() {
            return hotellist;
        }

        public void setHotellist(List<TravelAssistantInfoDto> hotellist) {
            this.hotellist = hotellist;
        }

        public static class TravelAssistantInfoDto {
            /**
             * projectName : 火车票
             * projectTag : TRAIN
             * startCityId : 6324
             * startCityName : 姑苏区
             * startLatitude : 31.330798
             * startLongtitude : 120.610444
             * startAddress : 苏州站
             * startAddressDetail : 苏州站
             * startTerminal : null
             * endCityId : 630
             * endCityName : 静安区
             * endLatitude : 31.249574
             * endLongtitude : 121.455708
             * endAddress : 上海站
             * endAddressDetail : 上海站
             * endTerminal : null
             * startTime : 2023-05-03 05:50
             * endTime : 2023-05-03 08:01
             * orderId : MT2023041918201219089213
             */

            private String projectName;
            private String projectTag;
            private String startCityId;
            private String startCityName;
            private String startLatitude;
            private String startLongtitude;
            private String startAddress;
            private String startAddressDetail;
            private Object startTerminal;
            private String endCityId;
            private String endCityName;
            private String endLatitude;
            private String endLongtitude;
            private String endAddress;
            private String endAddressDetail;
            private Object endTerminal;
            private String startTime;
            private String endTime;
            private String orderId;
            private String trainNo;
            private String flightNo;

            public TravelAssistantInfoDto() {
            }

            public String getProjectName() {
                return projectName;
            }

            public void setProjectName(String projectName) {
                this.projectName = projectName;
            }

            public String getProjectTag() {
                return projectTag;
            }

            public void setProjectTag(String projectTag) {
                this.projectTag = projectTag;
            }

            public String getStartCityId() {
                return startCityId;
            }

            public void setStartCityId(String startCityId) {
                this.startCityId = startCityId;
            }

            public String getStartCityName() {
                return startCityName;
            }

            public void setStartCityName(String startCityName) {
                this.startCityName = startCityName;
            }

            public String getStartLatitude() {
                return startLatitude;
            }

            public void setStartLatitude(String startLatitude) {
                this.startLatitude = startLatitude;
            }

            public String getStartLongtitude() {
                return startLongtitude;
            }

            public void setStartLongtitude(String startLongtitude) {
                this.startLongtitude = startLongtitude;
            }

            public String getStartAddress() {
                return startAddress;
            }

            public void setStartAddress(String startAddress) {
                this.startAddress = startAddress;
            }

            public String getStartAddressDetail() {
                return startAddressDetail;
            }

            public void setStartAddressDetail(String startAddressDetail) {
                this.startAddressDetail = startAddressDetail;
            }

            public Object getStartTerminal() {
                return startTerminal;
            }

            public void setStartTerminal(Object startTerminal) {
                this.startTerminal = startTerminal;
            }

            public String getEndCityId() {
                return endCityId;
            }

            public void setEndCityId(String endCityId) {
                this.endCityId = endCityId;
            }

            public String getEndCityName() {
                return endCityName;
            }

            public void setEndCityName(String endCityName) {
                this.endCityName = endCityName;
            }

            public String getEndLatitude() {
                return endLatitude;
            }

            public void setEndLatitude(String endLatitude) {
                this.endLatitude = endLatitude;
            }

            public String getEndLongtitude() {
                return endLongtitude;
            }

            public void setEndLongtitude(String endLongtitude) {
                this.endLongtitude = endLongtitude;
            }

            public String getEndAddress() {
                return endAddress;
            }

            public void setEndAddress(String endAddress) {
                this.endAddress = endAddress;
            }

            public String getEndAddressDetail() {
                return endAddressDetail;
            }

            public void setEndAddressDetail(String endAddressDetail) {
                this.endAddressDetail = endAddressDetail;
            }

            public Object getEndTerminal() {
                return endTerminal;
            }

            public void setEndTerminal(Object endTerminal) {
                this.endTerminal = endTerminal;
            }

            public String getStartTime() {
                return startTime;
            }

            public void setStartTime(String startTime) {
                this.startTime = startTime;
            }

            public String getEndTime() {
                return endTime;
            }

            public void setEndTime(String endTime) {
                this.endTime = endTime;
            }

            public String getOrderId() {
                return orderId;
            }

            public void setOrderId(String orderId) {
                this.orderId = orderId;
            }

            public String getTrainNo() {
                return trainNo;
            }

            public void setTrainNo(String trainNo) {
                this.trainNo = trainNo;
            }

            public String getFlightNo() {
                return flightNo;
            }

            public void setFlightNo(String flightNo) {
                this.flightNo = flightNo;
            }
        }
    }
}
