package com.ly.travel.car.im.integration.client.api;

import com.google.common.base.Strings;

import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.im.integration.request.BumblebeeReqDTO;
import com.ly.travel.car.im.integration.request.StationReqDTO;
import com.ly.travel.car.im.integration.response.BumblebeeRspDTO;
import com.ly.travel.car.im.integration.response.StationRspDTO;
import com.ly.travel.car.im.integration.utils.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.Objects;

@Slf4j
@Component
public class BumblebeeApi {

    @Value("${config.url.bumblebee}")
    private String bumblebeeUrl;

    @Value("${config.url.station}")
    private String stationUrl;

    /**
     * 查询机火酒待出行订单
     */
    public BumblebeeRspDTO getUndoOrderList(BumblebeeReqDTO req, String orderId) {
        try {
            String url = bumblebeeUrl + "/order/getUndoOrderList";
            String requestBody = FastJsonUtils.toJSONString(req);
            LoggerUtils.info(log, "[getUndoOrderList][{}] 查询机火酒待出行订单，请求参数：url：{} ，requestBody：{}", orderId, url, FastJsonUtils.toJSONString(req));
            String responseBody = HttpUtils.post(url, requestBody);
            LoggerUtils.info(log, "[getUndoOrderList][{}] 查询机火酒待出行订单，返回参数：{}", orderId, responseBody);
            if (!Strings.isNullOrEmpty(responseBody)) {
                BumblebeeRspDTO bumblebeeRspDTO = FastJsonUtils.fromJSONString(responseBody, BumblebeeRspDTO.class);
                if (Objects.nonNull(bumblebeeRspDTO) && Objects.equals(bumblebeeRspDTO.getStatus(), HttpStatus.OK.value())
                        && Objects.nonNull(bumblebeeRspDTO.getBody())
                        && (!ObjectUtils.isEmpty(bumblebeeRspDTO.getBody().getAirlist()) || !ObjectUtils.isEmpty(bumblebeeRspDTO.getBody().getTrainlist()))) {
                    return bumblebeeRspDTO;
                }
            }
        } catch (Exception e) {
            LoggerUtils.error(log, "[getUndoOrderList][{}] 查询机火酒待出行订单异常", orderId, e);
        }
        return null;
    }

    /**
     * 判断出发地/目的地是否为场站
     */
    public StationRspDTO airForceStation(StationReqDTO req, String orderId) {
        try {
            String url = stationUrl;
            String requestBody = FastJsonUtils.toJSONString(req);
            LoggerUtils.info(log, "[airForceStation][{}] 判断出发地/目的地是否为场站，请求参数: url：{}， requestBody：{}", orderId, url, FastJsonUtils.toJSONString(req));
            String responseBody = HttpUtils.post(url, requestBody);
            LoggerUtils.info(log, "[airForceStation][{}] 判断出发地/目的地是否为场站，请求参数：{}，返回参数：{}", orderId, requestBody, responseBody);
            if (!Strings.isNullOrEmpty(responseBody)) {
                StationRspDTO stationRspDTO = FastJsonUtils.fromJSONString(responseBody, StationRspDTO.class);
                if (Objects.nonNull(stationRspDTO) && Objects.equals(stationRspDTO.getCode(), HttpStatus.OK.value()) && !ObjectUtils.isEmpty(stationRspDTO.getData_list())) {
                    return stationRspDTO;
                }
            }
        } catch (Exception e) {
            LoggerUtils.error(log, "[airForceStation][{}] 查判断出发地/目的地是否为场站异常：{}", orderId, e);
        }
        return null;
    }
}
