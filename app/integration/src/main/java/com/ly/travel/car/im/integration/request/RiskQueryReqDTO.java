package com.ly.travel.car.im.integration.request;

import lombok.Data;

import java.io.Serializable;

@Data
public class RiskQueryReqDTO implements Serializable {

    private static final long serialVersionUID = -4211620478310690355L;

    /**
     * 会员id(用户风控必填)
     */
    private String memberId;

    /**
     * unionId
     */
    private String unionId;

    /**
     * 车牌号
     */
    private String driverCardNo;

    /**
     * 订单号（后置流程风控使用）
     */
    private String orderId;

    /**
     * 风险主场景（详见风控接口文档：http://wiki.17usoft.com/pages/viewpage.action?pageId=95576946）
     */
    private Integer mainScene;

    /**
     * 风险子场景（同上）
     */
    private Integer childScene;

    private String text;
}
