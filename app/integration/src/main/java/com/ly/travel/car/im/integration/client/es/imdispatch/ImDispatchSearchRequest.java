package com.ly.travel.car.im.integration.client.es.imdispatch;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 话单查询
 * <AUTHOR>
 * @version Id: SecretReportSearchRequest, v 0.1 2025/3/18 19:10 zhurun(1048699) Exp $
 */
@Data
public class ImDispatchSearchRequest implements Serializable {

    private static final long serialVersionUID = 2571298452106996923L;

    /**
     * 获取查询起始条目编号 offset
     */
    private int               from;
    /**
     * 页大小
     */
    private int               size;
    /**
     * 项目
     */
    private Long              projectId;

    /**
     * 唯一标识一通通话记录的ID
     */
    private String            callId;

    /**
     * 外部业务合作id（一般业务传的都是订单号）
     */
    private String            orderSerialNo;

    /**
     * 被叫响铃时间
     */
    private Date              startRingTime;

    /**
     * 被叫响铃时间
     */
    private Date              endRingTime;
    /**
     * AXB中的A号码
     */
    private String            phoneA;
    /**
     * AXB中的B号码或者N号码
     */
    private String            phoneB;
    /**
     * AXB中的X号码
     */
    private String            phoneX;
    /**
     * 存在绑定关系
     */
    private String            hasBindRelation;

    private String            customerNumber;
}
