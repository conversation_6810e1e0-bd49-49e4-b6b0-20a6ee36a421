package com.ly.travel.car.im.integration.client.redis.impl;

import com.ly.sof.spi.configuration.AppConfiguration;
import com.ly.sof.spi.context.SOFContext;
import com.ly.sof.spi.context.SOFContextAware;
import com.ly.sof.spi.context.ShutdownCallback;
import com.ly.sof.spi.context.StartupCallback;
import com.ly.tcbase.cacheclient.CacheClientHA;
import com.ly.travel.car.im.integration.client.redis.RedisClient;
import com.ly.travel.car.im.integration.client.redis.RedisClientFactory;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version Id: DefaultRedisClientFactory, v 0.1 2024/04/25 13:43 lichunbo Exp $
 */
public class DefaultRedisClientFactory implements SOFContextAware, StartupCallback, ShutdownCallback, RedisClientFactory {

    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultRedisClientFactory.class);

    private static final String REDIS_GROUP_KEYS = "redis.groupNames";

    private List<String> groupNames = new ArrayList<>();

    private Map<String, RedisClient> clientMap = new HashMap<>();

    private AppConfiguration appConfiguration;

    /**
     * 获取指定客户端
     *
     * @param redisGroupName redisGroupName
     * @return RedisClient redis client
     */
    @Override
    public RedisClient getClient(String redisGroupName) {
        return clientMap.get(redisGroupName);
    }

    /**
     * 注入SOF上下文
     *
     * @param context SOF上下文
     */
    @Override
    public void setSOFContext(SOFContext context) {
        this.appConfiguration = context.getAppConfiguration();

        String redisGroupNames = this.appConfiguration.getPropertyValue(REDIS_GROUP_KEYS);
        String[] keys = StringUtils.split(redisGroupNames, ",");
        if (keys == null || keys.length == 0) {
            throw new IllegalStateException("No redis group names, please check config: " + REDIS_GROUP_KEYS);
        }

        for (String redisGroupName : keys) {
            if (StringUtils.isEmpty(redisGroupName)) {
                LOGGER.error("[RedisClientFactory] Redis Group Not Found: " + redisGroupName);
                throw new IllegalStateException("Redis Group Not Found: " + redisGroupName);
            }
            groupNames.add(redisGroupName);
            LOGGER.info("[RedisClientFactory] Redis Group Found: " + redisGroupName);
        }
    }

    /**
     * 执行容器启动后的后处理操作
     *
     * @param context SOF上下文
     */
    @Override
    public void startup(SOFContext context) {
        LOGGER.info("[RedisClientFactory] RedisClients init begin...");
        long startMillis = System.currentTimeMillis();
        for (String redisGroupName : groupNames) {
            RedisClientProxy redisClientProxy = new RedisClientProxy(new CacheClientHA(redisGroupName, true));
            LOGGER.info("[RedisClientFactory] RedisClient " + redisGroupName + " initialized...");
            clientMap.put(redisGroupName, redisClientProxy);
        }
        LOGGER.info("[RedisClientFactory] RedisClients init end in " + (System.currentTimeMillis() - startMillis) + " ms");
    }

    /**
     * 在容器销毁前执行前处理操作
     *
     * @param context SOF上下文
     */
    @Override
    public void shutdown(SOFContext context) {
        //CacheClientHA未提供资源销毁方法
    }

    /**
     * 获取顺序
     *
     * @return 顺序
     */
    @Override
    public int getOrder() {
        return -1;
    }
}