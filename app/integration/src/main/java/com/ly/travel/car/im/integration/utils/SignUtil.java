package com.ly.travel.car.im.integration.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ly.car.utils.JsonUtils;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;

public class SignUtil {
    private static ObjectMapper objectMapper = new ObjectMapper();
    private static final String MD5 = "MD5";

    /**
     * 腾讯出行签名方法
     * 鉴权说明文档 https://open.go.qq.com/api/
     *
     * @param obj
     * @param apiSecret
     * @return
     */
    public static String sign(Object obj, String apiSecret) {
        Map<String, Object> params = objectMapper.convertValue(obj, Map.class);
        params.remove("sign");

        // Step 1：按参数名称排序，拼接参数
        TreeMap<String, Object> sortedParams = new TreeMap<>(params);
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, Object> entry : sortedParams.entrySet()) {
            if (entry.getValue() != null) {
                if (entry.getValue() instanceof List) {
                    sb.append("&").append(entry.getKey()).append("=").append(JsonUtils.json(entry.getValue()));
                } else {
                    sb.append("&").append(entry.getKey()).append("=").append(entry.getValue());
                }
            }
        }
        if (sb.length() > 0) {
            sb.deleteCharAt(0);
        }
        sb.append(apiSecret);

        // Step 2：计算 MD5 值
        return DigestUtils.md5Hex(sb.toString()).toUpperCase();
    }

    /**
     * 腾讯出行签名方法,不排序
     *
     * @param obj
     * @param apiSecret
     * @return
     */
    public static String signNotSorted(Object obj, String apiSecret) {
        Map<String, Object> params = objectMapper.convertValue(obj, Map.class);
        params.remove("sign");

        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            if (entry.getValue() != null) {
                if (entry.getValue() instanceof List) {
                    sb.append("&").append(entry.getKey()).append("=").append(JsonUtils.json(entry.getValue()));
                } else {
                    sb.append("&").append(entry.getKey()).append("=").append(entry.getValue());
                }
            }
        }
        if (sb.length() > 0) {
            sb.deleteCharAt(0);
        }
        sb.append(apiSecret);

        // Step 2：计算 MD5 值
        return DigestUtils.md5Hex(sb.toString()).toUpperCase();
    }

    /**
     * 生成10位随机字符串
     *
     * @return
     */
    public static String getRandomString() {
        StringBuffer sb = new StringBuffer();
        Random random = new Random();
        for (int i = 0; i < 10; i++) {
            int number = random.nextInt(3);
            long result;
            switch (number) {
                case 0:
                    result = Math.round(Math.random() * 25 + 65);
                    sb.append((char) result);
                    break;
                case 1:
                    result = Math.round(Math.random() * 25 + 97);
                    sb.append((char) result);
                    break;
                case 2:
                    sb.append(new Random().nextInt(10));
                    break;
            }
        }
        return sb.toString();
    }


    /**
     * 腾讯聚合的签名
     *
     * @param params
     * @param secret
     * @return
     * @throws Exception
     */
    public static String signTencent(Map<String, String> params, String secret) {
        // 第一步：参数经排序
        String[] keys = params.keySet().toArray(new String[0]);
        Arrays.sort(keys);
        // 第二步：把所有参数名和参数值串在一起
        StringBuilder sb = new StringBuilder();
        for (String key : keys) {
            if (StringUtils.isNotBlank(key)) {
                String value = params.get(key);
                if (null != value) {
                    sb.append(key.concat("="));
                    sb.append(value.concat("&"));
                }
            }
        }
        sb.append("apiSercret=" + secret);
        return toHex(getMd5(sb.toString()));
    }

    private static String toHex(byte[] byteArray) {
        if (null == byteArray) {
            return null;
        }
        StringBuilder md5Str = new StringBuilder();
        for (byte b : byteArray) {
            md5Str.append(String.format("%02x", b));
        }
        return md5Str.toString().toUpperCase();
    }

    private static byte[] getMd5(String str) {
        MessageDigest messageDigest;
        try {
            messageDigest = MessageDigest.getInstance(MD5);
            messageDigest.reset();
            messageDigest.update(str.getBytes(StandardCharsets.UTF_8));
        } catch (NoSuchAlgorithmException e) {
            return new byte[0];
        }
        return messageDigest.digest();
    }
}
