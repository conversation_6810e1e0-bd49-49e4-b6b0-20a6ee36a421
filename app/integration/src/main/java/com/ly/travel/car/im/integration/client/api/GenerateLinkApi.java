package com.ly.travel.car.im.integration.client.api;

import com.google.gson.Gson;
import com.ly.car.bean.NameValue;
import com.ly.car.utils.HttpUtils;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.im.integration.response.generatelink.ShortLinkRspDTO;
import com.ly.travel.car.im.integration.request.generatelink.WxLinkReqDTO;
import com.ly.travel.car.im.integration.response.generatelink.WxLinkRspDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class GenerateLinkApi {

    @Value("${config.generate-link.wxUrlLink}")
    private String wxUrlLink;

    @Value("${config.generate-link.wxAccount}")
    private String wxAccount;

    @Value("${config.generate-link.wxSign}")
    private String wxSign;

    @Value("${config.generate-link.shortUrl}")
    private String shortUrl;

    public String wxUrlLink(String orderId, String path) {
        try {
            WxLinkReqDTO req = new WxLinkReqDTO();
            req.setAccount(wxAccount);
            req.setSign(wxSign);
            WxLinkReqDTO.Content content = new WxLinkReqDTO.Content();
            content.setPath(path);
            content.setExpireTime(LocalDateTime.now().plusMonths(6).toEpochSecond(ZoneOffset.ofHours(8)));
            content.setName("顺风车");
            req.setContent(content);
            LoggerUtils.info(log, "[wxUrlLink][req][{}] 动态生成微信链接接口，请求参数： req: {}", orderId, FastJsonUtils.toJSONString(req));
            String result = HttpUtils.post(wxUrlLink, FastJsonUtils.toJSONString(req));
            LoggerUtils.info(log, "[wxUrlLink][rsp][{}] 动态生成微信链接接口，返回信息： rsp：{}", orderId, result);
            Gson gson = new Gson();
            WxLinkRspDTO rsp = gson.fromJson(result, WxLinkRspDTO.class);
            WxLinkRspDTO.Content ct = rsp.getContent();
            if (rsp.isSucceed() && Objects.nonNull(ct)) {
                return ct.getOpenLink();
            } else {
                LoggerUtils.error(log, "[wxUrlLink][rsp][{}] 动态生成微信链接接口，失败：code：{}，resc：{}", orderId, rsp.getRetCode(), rsp.getRetMsg());
            }
        } catch (Exception e) {
            LoggerUtils.error(log, "[wxUrlLink][rsp][{}] 动态生成微信链接接口，异常", orderId, e);
        }
        return null;
    }

    public String shortLink(String longUrls, String orderId) {
        try {
            LoggerUtils.info(log, "[shortLink][req][{}] 生成短链接口，请求参数： req: {}", orderId, FastJsonUtils.toJSONString(longUrls));
            NameValue nameValue = new NameValue();
            nameValue.setName("longUrls");
            nameValue.setValue(longUrls);
            String result = HttpUtils.post(shortUrl, nameValue);
            LoggerUtils.info(log, "[shortLink][rsp][{}] 生成短链接口，返回信息：rsp: {}", orderId, result);
            ShortLinkRspDTO rsp = FastJsonUtils.fromJSONString(result, ShortLinkRspDTO.class);
            List<ShortLinkRspDTO.Link> linkList = rsp.getDatas();
            String status = rsp.getStatus();
            if ("ok".equals(status) && CollectionUtils.isNotEmpty(linkList)) {
                return linkList.get(0).getShortUrl();
            } else {
                LoggerUtils.error(log, "[shortLink][rsp][{}] 生成短链接口，失败：code：{}，rsp：{}", orderId, status, linkList.get(0).getLongUrl());
            }
        } catch (Exception e) {
            LoggerUtils.error(log, "[shortLink][rsp][{}] 生成短链接口，异常", orderId, e);
        }
        return Strings.EMPTY;
    }
}
