package com.ly.travel.car.im.integration.client.es.imdispatch;

import com.ly.travel.car.im.integration.client.es.common.ESSearchResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version Id: SecretReportSearchResponse, v 0.1 2025/3/18 19:55 zhurun(1048699) Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ImDispatchSearchResponse extends ESSearchResponse<ImDispatchToElasticSearchVO> implements Serializable {

    private static final long serialVersionUID = -8951924356912575296L;

}
