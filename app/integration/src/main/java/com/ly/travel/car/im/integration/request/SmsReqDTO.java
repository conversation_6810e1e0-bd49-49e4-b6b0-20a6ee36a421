package com.ly.travel.car.im.integration.request;

import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

@Data
public class SmsReqDTO implements Serializable {

    private static final long serialVersionUID = -2298655778361362474L;

    private int nodeId;

    private Integer isGlobal = 0;

    private String globalNumber;

    private String mobile;

    private String platformId;

    private Map<String, String> inputParameters = new HashMap<>();

    /**
     * 是否替换【同程旅行】前缀
     */
    private Boolean isHidePrefix = false;
}
