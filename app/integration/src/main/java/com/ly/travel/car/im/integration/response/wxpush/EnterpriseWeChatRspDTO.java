package com.ly.travel.car.im.integration.response.wxpush;

import lombok.Data;

import java.io.Serializable;

/**
 * 企微推送消息响应
 */
@Data
public class EnterpriseWeChatRspDTO implements Serializable {

    private static final long serialVersionUID = 392429597042027923L;

    /**
     * 1000时消息发送成功,非1000查看code码枚举
     */
    private Integer code;

    /**
     * 消息
     */
    private String message;

    /**
     * logId
     */
    private String logId;

    /**
     * 响应data
     */
    private EnterpriseWeChatResponseData data;

    /**
     * 响应data
     */
    @Data
    public static class EnterpriseWeChatResponseData implements Serializable {

        /**
         * 发送消息用的手机号
         */
        private String mobile;

        /**
         * 客户的会话id
         */
        private String conversationRemoteId;

        /**
         * 企微主体标识
         */
        private String corpId;
    }
}
