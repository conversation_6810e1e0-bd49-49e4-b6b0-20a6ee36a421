package com.ly.travel.car.im.integration.client;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.ly.travel.car.im.integration.client.es.common.ESBulkResponse;
import com.ly.travel.car.im.integration.client.es.common.ESSearchResponse;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.Map;

/**
 * <AUTHOR>
 * @version Id: AbstractEsClient, v 0.1 2025/3/19 13:04 zhurun(1048699) Exp $
 */
@Slf4j
public abstract class AbstractEsClient extends BaseEsHttpClient {

    protected static final String DEFAULT_TEMPLATE = "query";

    protected abstract String esPrefix();

    protected abstract Map<String, String> token();

    protected String templateName() {
        return DEFAULT_TEMPLATE;
    }

    protected abstract String version();

    public <T> ESBulkResponse bulk(T body) {
        ESBulkResponse response = null;
        String esName = String.join("-", esPrefix(), env());

        response = bulk(Collections.singletonList(body), esName, token());

        if (response == null) {
            log.error("es更新失败，返回response为空， request : {}", JSON.toJSONString(body));
            return response;
        }
        if (response.getCode() != 0) {
            log.error("es更新失败，response：{} , request：{}", JSON.toJSONString(response), JSON.toJSONString(body));
        }
        return response;
    }

}
