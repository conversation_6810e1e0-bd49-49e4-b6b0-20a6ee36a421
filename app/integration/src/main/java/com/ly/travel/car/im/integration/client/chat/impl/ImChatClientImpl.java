package com.ly.travel.car.im.integration.client.chat.impl;

import com.ly.travel.car.im.integration.client.BaseClient;
import com.ly.travel.car.im.integration.client.chat.ImChatClient;
import com.ly.travel.car.im.integration.throwable.exception.IntegrationException;
import com.ly.travel.shared.mobility.supply.integration.facade.carcommon.ImFacade;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.driverOnlineStatus.DriverOnlineStatusRequest;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.driverOnlineStatus.DriverOnlineStatusResponse;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.imClose.ImCloseRequest;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.imClose.ImCloseResponse;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.imCreate.ImCreateRequest;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.imCreate.ImCreateResponse;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.sendMsg.SendMsgRequest;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.sendMsg.SendMsgResponse;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.uploadUserStatus.UploadUserStatusRequest;
import com.ly.travel.shared.mobility.supply.integration.facade.model.im.uploadUserStatus.UploadUserStatusResponse;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 聊天相关服务
 *
 * <AUTHOR>
 * @version v0.1 2024年05月25日 11:23 lichunbo Exp $
 */
@Service
public class ImChatClientImpl extends BaseClient implements ImChatClient {
    @Resource
    private ImFacade imFacade;

    @Override
    public SendMsgResponse sendMsg(SendMsgRequest var1) throws IntegrationException {
        SendMsgResponse response = call(imFacade::sendMsg, var1, SendMsgResponse::isSuccess, SendMsgResponse::getErrorMessage);
        return response;
    }

    @Override
    public DriverOnlineStatusResponse getDriverOnlineStatus(DriverOnlineStatusRequest var1) throws IntegrationException {
        DriverOnlineStatusResponse response = call(imFacade::driverOnlineStatus, var1, DriverOnlineStatusResponse::isSuccess, DriverOnlineStatusResponse::getErrorMessage);
        return response;
    }

    @Override
    public UploadUserStatusResponse uploadUserStatus(UploadUserStatusRequest var1) throws IntegrationException {
        UploadUserStatusResponse response = call(imFacade::uploadUserStatus, var1, UploadUserStatusResponse::isSuccess, UploadUserStatusResponse::getErrorMessage);
        return response;
    }

    @Override
    public ImCreateResponse imCreate(ImCreateRequest var1) throws IntegrationException {
        ImCreateResponse response = call(imFacade::imCreate, var1, ImCreateResponse::isSuccess, ImCreateResponse::getErrorMessage);
        return response;
    }

    @Override
    public ImCloseResponse imClose(ImCloseRequest var1) throws IntegrationException {
        ImCloseResponse response = call(imFacade::imClose, var1, ImCloseResponse::isSuccess, ImCloseResponse::getErrorMessage);
        return response;
    }
}
