package com.ly.travel.car.im.integration.utils;


import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public class AMapUtils {
    public static Map<String, String> objectToString(Map<String, Object> map) {
        Map<String, String> result = new HashMap<>();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            String stringValue = (Objects.isNull(value)) ? null : value.toString();
            result.put(key, stringValue);
        }
        return result;
    }
}
