package com.ly.travel.car.im.integration.response.wxpush;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class WxPnsPushRspDTO implements Serializable {

    private static final long serialVersionUID = -8634522961022850695L;

    private List<WxPnsPushResultDTO> results;

    /**
     * 0	成功
     * 101	推送次数超限
     * 102	openid/unionid缺失
     * 103	查询不到openid
     * 104	transactionid缺失
     * 106	公众号Payload缺失
     * 107	小程序Payload缺失
     * 108	微信通知Payload缺失
     * 109/110	运行时错误
     * 111	微信服务推送错误
     * 112	微信服务账户缺失
     * 113	微信服务url缺失
     * 114	openid查询异常
     * 120	客服Payload缺失
     * 131	公众号模板ID缺失
     * 132	小程序模板ID缺失
     * 133	微信通知模板ID缺失
     * 134	订阅推送模板ID缺失
     * 135
     * 小程序订阅Payload缺失
     * <p>
     * 136
     * 该账号没有可用推送次数
     * <p>
     * 137	模板订阅（新）推送Payload缺失
     * 138	该账号没有可用推送次数
     * 999	该推送方式已不再支持
     */
    private Integer resultCode;

    private String resultMsg;
}
