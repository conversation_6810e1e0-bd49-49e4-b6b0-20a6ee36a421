<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <groupId>com.ly.travel.car</groupId>
        <artifactId>shared-mobility-im-chat-service-parent</artifactId>
        <version>*******-RELEASE</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>shared-mobility-im-chat-service-integration</artifactId>
    <packaging>jar</packaging>

    <name>LY shared-mobility-im-chat-service-integration</name>
    <description>LY shared-mobility-im-chat-service-integration</description>

    <dependencies>
        <dependency>
            <groupId>com.ly.travel.car</groupId>
            <artifactId>shared-mobility-im-chat-service-model</artifactId>
        </dependency>
        <!--region log dependecies -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
        </dependency>
        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>

        <!-- log4j2 -->
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>jcl-over-slf4j</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-web</artifactId>
            <scope>runtime</scope>
        </dependency>
        <!--endregion -->
        <!-- Test dependecies -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-all</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.easymock</groupId>
            <artifactId>easymock</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-easymock</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.ly.flight.toolkit</groupId>
            <artifactId>object-diff</artifactId>
        </dependency>
        <!-- 热部署组件依赖redis 依赖tcbase cache后开启 -->
        <!--		<dependency>-->
        <!--			<groupId>com.ly.flight.toolkit</groupId>-->
        <!--			<artifactId>deploy-static-resource</artifactId>-->
        <!--		</dependency>-->
        <dependency>
            <groupId>com.ly.sof</groupId>
            <artifactId>sof-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ly.tcbase</groupId>
            <artifactId>cache</artifactId>
        </dependency>
    </dependencies>
</project>
