package com.ly.travel.car.im.dal.entity;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@Accessors(chain = true)
public class SfcImInfo extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 6860601924058253930L;

    /**
     * 会话标识（供应商Code/子供应商Code + 会员ID/unionId + 司机ID）
     */
    private String sessionKey;

    /**
     * 用户unionId
     */
    private String unionId;

    /**
     * 会员ID
     */
    private Long memberId;

    /**
     * 司机ID
     */
    private String driverId;

    /**
     * 司机车牌号
     */
    private String plateNumber;

    /**
     * 供应商Code
     */
    private String supplierCode;

    /**
     * 子供应商Code
     */
    private String subSupplierCode;

    /**
     * 司机昵称
     */
    private String driverNickName;

    /**
     * 司机性别 0-未知 1-男 2-女
     */
    private Integer driverGender;

    /**
     * 未读消息数量
     */
    private Integer unreadMsgNum;

    /**
     * 最新消息记录ID
     */
    private Long latestMsgRecordId;

    /**
     * 最新消息内容简介
     */
    private String latestMsgDesc;

    /**
     * 最新消息类型 0-快捷消息 1-自定义文本 2-定位 3-语音 4-图片 5-卡片消息 6-系统消息 7-提示公告
     */
    private Integer latestMsgType;

    /**
     * 最新消息时间
     */
    private Date latestMsgTime;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 供应商订单号
     */
    private String supplierOrderId;

    /**
     * 同程会话id
     */
    private String tcSessionId;

    /**
     * 供应商会话id
     */
    private String supplierSessionId;

    /**
     * 聊天状态 0：在线  1：离开
     */
    private Integer chatStatus;

    /**
     * 既往订单号
     */
    private String allOrderIds;

    /**
     * 渠道id
     */
    private Integer plateId;

    /**
     * 替代memberId
     */
    private Long alternateMemberId;
}
