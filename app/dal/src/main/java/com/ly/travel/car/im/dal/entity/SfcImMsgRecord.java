package com.ly.travel.car.im.dal.entity;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@Accessors(chain = true)
public class SfcImMsgRecord extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 2636060479092461785L;

    /**
     * 会话标识（供应商Code/子供应商Code + 会员ID/unionId + 司机ID）
     */
    private String sessionKey;

    /**
     * 用户unionId
     */
    private String unionId;

    /**
     * 会员ID
     */
    private Long memberId;

    /**
     * 司机车牌号
     */
    private String plateNumber;

    /**
     * 供应商Code
     */
    private String supplierCode;

    /**
     * 子供应商Code
     */
    private String subSupplierCode;

    /**
     * 司机昵称
     */
    private String driverNickName;

    /**
     * 司机性别 0-未知 1-男 2-女
     */
    private Integer driverGender;

    /**
     * 司机ID
     */
    private String driverId;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 供应商订单号
     */
    private String supplierOrderId;

    /**
     * 消息唯一ID
     */
    private String msgId;

    /**
     * 消息内容
     */
    private String msgContent;

    /**
     * 消息类型 0-快捷消息 1-自定义文本 2-定位 3-语音 4-图片 5-卡片消息 6-系统消息 7-提示公告
     */
    private Integer msgType;

    /**
     * 消息发送者 1-乘客 2-司机
     */
    private Integer msgSender;

    /**
     * 场景类型（同顺风车订单状态枚举）
     */
    private Integer sceneType;

    /**
     * 消息状态 0-未读 1-已读
     */
    private Integer msgStatus;

    /**
     * 消息发送状态 0-失败 1-成功
     */
    private Integer msgSendStatus;

    /**
     * 订单状态（过程值）
     */
    private Integer orderStatus;

    /**
     * 消息发送时间
     */
    private Date msgSendTime;

    /**
     * 消息版本，用于区分不同迭代消息格式的兼容
     */
    private Integer msgVersion;

    /**
     * 乘客已读页面来源
     */
    private String pageSource;

    /**
     * 乘客已读页面来源refId
     */
    private String readSourceRefid;

    /**
     * 乘客消息发送渠道 wx app xcx
     */
    private String platform;
}
