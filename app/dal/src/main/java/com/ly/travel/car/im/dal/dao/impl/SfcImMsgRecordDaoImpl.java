package com.ly.travel.car.im.dal.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ly.dal.util.DateUtil;
import com.ly.travel.car.im.dal.dao.SfcImMsgRecordDao;
import com.ly.travel.car.im.dal.entity.SfcImMsgRecord;
import com.ly.travel.car.im.dal.mapper.SfcImMsgRecordMapper;
import com.ly.travel.car.im.facade.enums.MsgSenderEnum;
import com.ly.travel.car.im.facade.enums.MsgTypeEnum;
import com.ly.travel.car.im.facade.request.QueryMsgRecordReqDTO;
import com.ly.travel.car.im.model.enums.MsgSendStatusEnum;
import com.ly.travel.car.im.model.enums.MsgStatusEnum;
import com.ly.travel.car.im.model.utils.MsgTypeConverter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
public class SfcImMsgRecordDaoImpl extends ServiceImpl<SfcImMsgRecordMapper, SfcImMsgRecord> implements SfcImMsgRecordDao {
    @Override
    public SfcImMsgRecord findByMsgId(String msgId) {
        return baseMapper.selectList(new LambdaQueryWrapper<SfcImMsgRecord>()
                        .eq(SfcImMsgRecord::getMsgId, msgId)
                        .last("limit 1")
                        .orderByDesc(SfcImMsgRecord::getId))
                .stream()
                .findFirst()
                .orElse(null);
    }

    @Override
    public Long selectCount(String sessionKey, Integer msgSender, String orderId, Integer msgStatus) {
        LambdaQueryWrapper<SfcImMsgRecord> lambdaQueryWrapper = new LambdaQueryWrapper<>();

        if (StringUtils.isNotBlank(sessionKey)) {
            lambdaQueryWrapper.eq(SfcImMsgRecord::getSessionKey, sessionKey);
        }

        if (Objects.nonNull(msgSender)) {
            lambdaQueryWrapper.eq(SfcImMsgRecord::getMsgSender, msgSender);
        }

        if (StringUtils.isNotBlank(orderId)) {
            lambdaQueryWrapper.eq(SfcImMsgRecord::getOrderId, orderId);
        }

        if (Objects.nonNull(msgStatus)) {
            lambdaQueryWrapper.eq(SfcImMsgRecord::getMsgStatus, msgStatus);
        }

        return baseMapper.selectCount(lambdaQueryWrapper);
    }

    @Override
    public SfcImMsgRecord findByMsgRecord(String sessionKey, String orderId, Integer msgType) {
        return baseMapper.selectList(new LambdaQueryWrapper<SfcImMsgRecord>()
                        .eq(SfcImMsgRecord::getSessionKey, sessionKey)
                        .eq(SfcImMsgRecord::getOrderId, orderId)
                        .in(SfcImMsgRecord::getMsgType, msgType)
                        .last("limit 1")
                        .orderByDesc(SfcImMsgRecord::getId))
                .stream()
                .findFirst()
                .orElse(null);
    }

    @Override
    public Page<SfcImMsgRecord> selectRecordPage(Page<SfcImMsgRecord> page, String sessionKey, String orderId) {
        return baseMapper.selectPage(page, new LambdaQueryWrapper<SfcImMsgRecord>()
                .eq(SfcImMsgRecord::getSessionKey, sessionKey)
                .eq(SfcImMsgRecord::getOrderId, orderId)
                .eq(SfcImMsgRecord::getMsgSendStatus, MsgSendStatusEnum.SUCCESS.getCode())
                .in(SfcImMsgRecord::getMsgSender, MsgSenderEnum.PASSENGER.getCode(), MsgSenderEnum.DRIVER.getCode())
                .orderByDesc(SfcImMsgRecord::getId));
    }

    @Override
    public SfcImMsgRecord queryRepeatMsg(String sessionKey, String msgId, Integer msgSender, Integer msgSendStatus) {
        return baseMapper.selectList(new LambdaQueryWrapper<SfcImMsgRecord>()
                        .eq(SfcImMsgRecord::getSessionKey, sessionKey)
                        .eq(SfcImMsgRecord::getMsgId, msgId)
                        .eq(SfcImMsgRecord::getMsgSender, msgSender)
                        .eq(SfcImMsgRecord::getMsgSendStatus, msgSendStatus)
                        .orderByDesc(SfcImMsgRecord::getId))
                .stream()
                .findFirst()
                .orElse(null);
    }

    @Override
    public List<SfcImMsgRecord> queryMsgRecord(List<String> sessionKeys, String createTime, String msgSendTime, Integer queryDirection, Integer queryNum) {
        LambdaQueryWrapper<SfcImMsgRecord> lambdaQueryWrapper = new LambdaQueryWrapper<SfcImMsgRecord>()
                .in(SfcImMsgRecord::getSessionKey, sessionKeys)
                .gt(SfcImMsgRecord::getCreateTime, createTime);
        if (StringUtils.isNotBlank(msgSendTime)) {
            lambdaQueryWrapper = Objects.equals(queryDirection, 1) ? lambdaQueryWrapper.lt(SfcImMsgRecord::getMsgSendTime, msgSendTime) : lambdaQueryWrapper.gt(SfcImMsgRecord::getMsgSendTime, msgSendTime);
        }
        lambdaQueryWrapper.orderByDesc(SfcImMsgRecord::getId).last(" limit " + queryNum);
        return baseMapper.selectList(lambdaQueryWrapper);
    }

    @Override
    public List<SfcImMsgRecord> queryUnreadMsg(List<String> sessionKeys, Integer msgSender) {
        return Optional.ofNullable(baseMapper.selectList(new LambdaQueryWrapper<SfcImMsgRecord>()
                        .in(SfcImMsgRecord::getSessionKey, sessionKeys)
                        .eq(SfcImMsgRecord::getMsgSender, msgSender)
                        .eq(SfcImMsgRecord::getMsgStatus, MsgStatusEnum.UNREAD.getCode())
                        .orderByDesc(SfcImMsgRecord::getId)))
                .orElse(Collections.emptyList());
    }

    @Override
    public List<SfcImMsgRecord> queryUnreadMsgByOrderId(String orderId, Integer msgSender) {
        return Optional.ofNullable(baseMapper.selectList(new LambdaQueryWrapper<SfcImMsgRecord>()
                        .eq(SfcImMsgRecord::getOrderId, orderId)
                        .eq(SfcImMsgRecord::getMsgSender, msgSender)
                        .eq(SfcImMsgRecord::getMsgStatus, MsgStatusEnum.UNREAD.getCode())
                        .orderByDesc(SfcImMsgRecord::getId)))
                .orElse(Collections.emptyList());
    }

    @Override
    public List<SfcImMsgRecord> queryMsgRecord(QueryMsgRecordReqDTO req) {
        LambdaQueryWrapper<SfcImMsgRecord> lambdaQueryWrapper = new LambdaQueryWrapper<SfcImMsgRecord>();
        
        // 如果有订单ID，按订单ID过滤
        if (StringUtils.isNotBlank(req.getOrderId())) {
            lambdaQueryWrapper.eq(SfcImMsgRecord::getOrderId, req.getOrderId());
        }
        
        // 如果有会话标识，按会话标识过滤
        if (StringUtils.isNotBlank(req.getSessionKey())) {
            lambdaQueryWrapper.eq(SfcImMsgRecord::getSessionKey, req.getSessionKey());
        }
        
        if (StringUtils.isNotBlank(req.getStartTime())) {
            try {
                Date startDate = DateUtil.string2Date(req.getStartTime());
                lambdaQueryWrapper.ge(SfcImMsgRecord::getMsgSendTime, startDate);
            } catch (Exception e) {
                // 日期格式不正确，忽略该条件
            }
        }
        
        if (StringUtils.isNotBlank(req.getEndTime())) {
            try {
                Date endDate = DateUtil.string2Date(req.getEndTime());
                lambdaQueryWrapper.le(SfcImMsgRecord::getMsgSendTime, endDate);
            } catch (Exception e) {
                // 日期格式不正确，忽略该条件
            }
        }
        
        // 如果有用户ID，按用户ID或unionId过滤
        if (StringUtils.isNotBlank(req.getUserId())) {
            try {
                Long memberId = Long.parseLong(req.getUserId());
                lambdaQueryWrapper.eq(SfcImMsgRecord::getMemberId, memberId);
            } catch (NumberFormatException e) {
                lambdaQueryWrapper.eq(SfcImMsgRecord::getUnionId, req.getUserId());
            }
        }
        
        if (StringUtils.isNotBlank(req.getMessageType())) {
            MsgTypeEnum msgTypeEnum = MsgTypeConverter.convertMessageType(req.getMessageType());
            if (msgTypeEnum != null) {
                lambdaQueryWrapper.eq(SfcImMsgRecord::getMsgType, msgTypeEnum.getMsgType());
            }
        }

        if (StringUtils.isNotBlank(req.getPlateNumber())) {
            lambdaQueryWrapper.eq(SfcImMsgRecord::getPlateNumber,  req.getPlateNumber() );
        }


        if (StringUtils.isNotBlank(req.getSender())) {
            MsgSenderEnum msgSenderEnum = MsgTypeConverter.convertSender(req.getSender());
            if (msgSenderEnum != null) {
                lambdaQueryWrapper.eq(SfcImMsgRecord::getMsgSender, msgSenderEnum.getCode());
            }
        }

        if (Objects.nonNull(req.getMsgSendStatus())) {
            lambdaQueryWrapper.eq(SfcImMsgRecord::getMsgSendStatus, req.getMsgSendStatus());
        }
        
        lambdaQueryWrapper.orderByDesc(SfcImMsgRecord::getId);
        
        Integer pageNum = req.getPageNum();
        pageNum = Objects.isNull(pageNum) ? 1 : pageNum; // 默认第1页
        
        Integer pageSize = req.getPageSize();
        pageSize = Objects.isNull(pageSize) ? 15 : pageSize; // 默认每页15条
        
        // 计算offset
        int offset = (pageNum - 1) * pageSize;
        
        // 设置分页限制
        lambdaQueryWrapper.last(" limit " + offset + "," + pageSize);
        
        // 执行查询

        return baseMapper.selectList(lambdaQueryWrapper);
    }

    @Override
    public Long countMsgRecord(QueryMsgRecordReqDTO req) {
        LambdaQueryWrapper<SfcImMsgRecord> lambdaQueryWrapper = new LambdaQueryWrapper<SfcImMsgRecord>();
        
        // 如果有订单ID，按订单ID过滤
        if (StringUtils.isNotBlank(req.getOrderId())) {
            lambdaQueryWrapper.eq(SfcImMsgRecord::getOrderId, req.getOrderId());
        }

        // 如果有会话标识，按会话标识过滤
        if (StringUtils.isNotBlank(req.getSessionKey())) {
            lambdaQueryWrapper.eq(SfcImMsgRecord::getSessionKey, req.getSessionKey());
        }
        
        if (StringUtils.isNotBlank(req.getStartTime())) {
            try {
                Date startDate = DateUtil.string2Date(req.getStartTime());
                lambdaQueryWrapper.ge(SfcImMsgRecord::getMsgSendTime, startDate);
            } catch (Exception e) {
                // 日期格式不正确，忽略该条件
            }
        }
        
        if (StringUtils.isNotBlank(req.getEndTime())) {
            try {
                Date endDate = DateUtil.string2Date(req.getEndTime());
                lambdaQueryWrapper.le(SfcImMsgRecord::getMsgSendTime, endDate);
            } catch (Exception e) {
                // 日期格式不正确，忽略该条件
            }
        }
        
        // 如果有用户ID，按用户ID或unionId过滤
        if (StringUtils.isNotBlank(req.getUserId())) {
            try {
                Long memberId = Long.parseLong(req.getUserId());
                lambdaQueryWrapper.eq(SfcImMsgRecord::getMemberId, memberId);
            } catch (NumberFormatException e) {
                lambdaQueryWrapper.eq(SfcImMsgRecord::getUnionId, req.getUserId());
            }
        }

        if (StringUtils.isNotBlank(req.getPlateNumber())) {
            lambdaQueryWrapper.eq(SfcImMsgRecord::getPlateNumber,  req.getPlateNumber() );
        }
        
        if (StringUtils.isNotBlank(req.getMessageType())) {
            MsgTypeEnum msgTypeEnum = MsgTypeConverter.convertMessageType(req.getMessageType());
            if (msgTypeEnum != null) {
                lambdaQueryWrapper.eq(SfcImMsgRecord::getMsgType, msgTypeEnum.getMsgType());
            }
        }
        
        if (StringUtils.isNotBlank(req.getSender())) {
            MsgSenderEnum msgSenderEnum = MsgTypeConverter.convertSender(req.getSender());
            if (msgSenderEnum != null) {
                lambdaQueryWrapper.eq(SfcImMsgRecord::getMsgSender, msgSenderEnum.getCode());
            }
        }

        if (Objects.nonNull(req.getMsgSendStatus())) {
            lambdaQueryWrapper.eq(SfcImMsgRecord::getMsgSendStatus, req.getMsgSendStatus());
        }
        // 执行count查询
        return baseMapper.selectCount(lambdaQueryWrapper);
    }
}
