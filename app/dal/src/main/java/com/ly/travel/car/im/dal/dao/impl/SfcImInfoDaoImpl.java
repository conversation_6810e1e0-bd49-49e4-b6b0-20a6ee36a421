package com.ly.travel.car.im.dal.dao.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.im.dal.dao.SfcImInfoDao;
import com.ly.travel.car.im.dal.entity.SfcImInfo;
import com.ly.travel.car.im.dal.mapper.SfcImInfoMapper;
import com.ly.travel.car.im.facade.dto.DriverUnreadMsgDTO;
import com.ly.travel.car.im.model.dto.QueryMsgDTO;
import com.ly.travel.car.im.model.enums.MsgStatusEnum;
import com.ly.travel.car.im.model.enums.OrderChannelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SfcImInfoDaoImpl extends ServiceImpl<SfcImInfoMapper, SfcImInfo> implements SfcImInfoDao {

    @Override
    public SfcImInfo findBySessionKey(String sessionKey,Integer platId) {
        if (Objects.nonNull(platId) &&  OrderChannelEnum.isMaDa(platId)){
            return baseMapper.selectList(new LambdaQueryWrapper<SfcImInfo>()
                            .eq(SfcImInfo::getSessionKey, sessionKey)
                            .eq(SfcImInfo::getPlateId, platId)
                            .last("limit 1")
                            .orderByDesc(SfcImInfo::getId))
                    .stream()
                    .findFirst()
                    .orElse(null);
        }
        return baseMapper.selectList(new LambdaQueryWrapper<SfcImInfo>()
                        .eq(SfcImInfo::getSessionKey, sessionKey)
                        .last("limit 1")
                        .orderByDesc(SfcImInfo::getId))
                .stream()
                .findFirst()
                .orElse(null);
    }

    @Override
    public SfcImInfo findByMidAndDriverId(Long memberId, String driverId) {
        return baseMapper.selectList(new LambdaQueryWrapper<SfcImInfo>()
                        .eq(SfcImInfo::getMemberId, memberId)
                        .eq(SfcImInfo::getDriverId, driverId)
                        .last("limit 1")
                        .orderByDesc(SfcImInfo::getId))
                .stream()
                .findFirst()
                .orElse(null);
    }

    @Override
    public SfcImInfo findSfcImInfo(String sessionKey, String orderId) {
        return baseMapper.selectList(new LambdaQueryWrapper<SfcImInfo>()
                        .eq(SfcImInfo::getSessionKey, sessionKey)
                        .eq(SfcImInfo::getOrderId, orderId)
                        .last("limit 1")
                        .orderByDesc(SfcImInfo::getId))
                .stream()
                .findFirst()
                .orElse(null);
    }

    @Override
    public Long findByUserInfoTotal(QueryMsgDTO queryMsgDTO) {
        return baseMapper.selectCount(condition(new LambdaQueryWrapper<>(), queryMsgDTO));
    }

    @Override
    public List<SfcImInfo> findByUserInfoPage(QueryMsgDTO queryMsgDTO) {
        return baseMapper.selectList(condition(new LambdaQueryWrapper<>(), queryMsgDTO)
                .orderByDesc(SfcImInfo::getLatestMsgTime).last(" limit " + queryMsgDTO.getOffset() + "," + queryMsgDTO.getSize()));
    }

    @Override
    public List<SfcImInfo> findUnreadMsgInfo(QueryMsgDTO queryMsgDTO) {
        return baseMapper.selectList(condition(new LambdaQueryWrapper<>(), queryMsgDTO));
    }

    @Override
    public List<SfcImInfo> findDriverUnreadMsgInfo(List<DriverUnreadMsgDTO> driverUnreadMsgDTOS) {
        return baseMapper.selectList(new LambdaQueryWrapper<SfcImInfo>().in(SfcImInfo::getOrderId, driverUnreadMsgDTOS));
    }

    @Override
    public List<SfcImInfo> findByOrderId(String orderId) {
        if (StringUtils.isBlank(orderId)) {
            return Collections.emptyList();
        }
        return baseMapper.selectList(new LambdaQueryWrapper<SfcImInfo>().eq(SfcImInfo::getOrderId, orderId));
    }

    @Override
    public SfcImInfo findByTcConversationId(String tcConversationId) {
        return baseMapper.selectList(new LambdaQueryWrapper<SfcImInfo>()
                        .eq(SfcImInfo::getTcSessionId, tcConversationId)
                        .last("limit 1")
                        .orderByDesc(SfcImInfo::getId))
                .stream()
                .findFirst()
                .orElse(null);
    }

    private LambdaQueryWrapper<SfcImInfo> condition(LambdaQueryWrapper<SfcImInfo> lambdaQueryWrapper, QueryMsgDTO queryMsgDTO) {
        LoggerUtils.info(log, "[condition] 查询req：{}", FastJsonUtils.toJSONString(queryMsgDTO));
        String unionId = queryMsgDTO.getUnionId();
        Long memberId = queryMsgDTO.getMemberId();
        String sessionKey = queryMsgDTO.getSessionKey();
        String latestMsgTime = queryMsgDTO.getLatestMsgTime();
        String orderSerialNo = queryMsgDTO.getOrderSerialNo();
        Integer plateId = queryMsgDTO.getPlateId();
        boolean onlyQueryUnreadMsg = queryMsgDTO.isOnlyQueryUnreadMsg();
        List<Long> memberIds=  CollectionUtil.isNotEmpty(queryMsgDTO.getMemberIds())? queryMsgDTO.getMemberIds().stream().filter(Objects::nonNull)
                .filter(i->!Objects.equals(0L,i)).collect(Collectors.toList()) : Collections.emptyList();

        if (Objects.nonNull(plateId)) {
            lambdaQueryWrapper.eq(SfcImInfo::getPlateId, plateId);
        }


        if(CollectionUtil.isNotEmpty(memberIds)){
            lambdaQueryWrapper.in(SfcImInfo::getMemberId, memberIds);
        }else{
            if (Objects.nonNull(memberId)) {
                lambdaQueryWrapper.eq(SfcImInfo::getMemberId, memberId);
            }

            if (StringUtils.isNotBlank(unionId)) {
                lambdaQueryWrapper.eq(SfcImInfo::getUnionId, unionId);
            }
        }

        if (StringUtils.isNotBlank(sessionKey)) {
            lambdaQueryWrapper.eq(SfcImInfo::getSessionKey, sessionKey);
        }

        if (StringUtils.isNotBlank(latestMsgTime)) {
            lambdaQueryWrapper.gt(SfcImInfo::getLatestMsgTime, latestMsgTime);
        }

        if (onlyQueryUnreadMsg) {
            lambdaQueryWrapper.gt(SfcImInfo::getUnreadMsgNum, MsgStatusEnum.UNREAD.getCode());
        }

        if (StringUtils.isNotBlank(orderSerialNo)) {
            lambdaQueryWrapper.and(wrapper -> wrapper.eq(SfcImInfo::getOrderId, orderSerialNo).or()
                    .like(SfcImInfo::getAllOrderIds, "%" + orderSerialNo + "%"));
        }else {
            lambdaQueryWrapper.notLike(SfcImInfo::getOrderId, "SFC");
            lambdaQueryWrapper.notLike(SfcImInfo::getOrderId, "YNC");
        }


        LoggerUtils.info(log, "[condition] 查询sql：{}", FastJsonUtils.toJSONString(lambdaQueryWrapper.getSqlSegment()));

        return lambdaQueryWrapper;
    }
}
