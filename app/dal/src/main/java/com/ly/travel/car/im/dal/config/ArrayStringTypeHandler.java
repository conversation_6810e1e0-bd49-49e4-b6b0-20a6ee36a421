package com.ly.travel.car.im.dal.config;

import com.baomidou.mybatisplus.annotation.TableName;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * 处理数组类型的字符串，例如服务类型 前端传的[1,2]
 * 配合 {@link TableName#autoResultMap()} 一起使用
 */
@MappedTypes(String.class)
@MappedJdbcTypes(JdbcType.VARCHAR)
public class ArrayStringTypeHandler extends BaseTypeHandler<String> {
    private static final String ARRAY_SEPARATOR = ",";

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, String parameter, JdbcType jdbcType)
            throws SQLException {
        ps.setString(i, ARRAY_SEPARATOR + parameter + ARRAY_SEPARATOR);
    }

    @Override
    public String getNullableResult(ResultSet rs, String columnName)
            throws SQLException {
        return formatArrayString(rs.getString(columnName));
    }

    @Override
    public String getNullableResult(ResultSet rs, int columnIndex)
            throws SQLException {
        return formatArrayString(rs.getString(columnIndex));
    }

    @Override
    public String getNullableResult(CallableStatement cs, int columnIndex)
            throws SQLException {
        return formatArrayString(cs.getString(columnIndex));
    }

    private static String formatArrayString(String arrayString) {
        if (arrayString == null) {
            return null;
        }
        // 去除前后的逗号
        if (arrayString.startsWith(ARRAY_SEPARATOR)) {
            arrayString = arrayString.substring(1);
        }
        if (arrayString.endsWith(ARRAY_SEPARATOR)) {
            arrayString = arrayString.substring(0, arrayString.length() - 1);
        }
        return arrayString;
    }

    /**
     * 对于查询条件前后增加逗号
     *
     * @param param 查询条件
     * @return 格式化好的结果
     */
    public static String formatSelectParam(String param) {
        if (param == null) {
            return null;
        }
        // 补充前面的逗号
        if (!param.startsWith(ARRAY_SEPARATOR)) {
            param = ARRAY_SEPARATOR + param;
        }
        // 补充后面的逗号
        if (!param.endsWith(ARRAY_SEPARATOR)) {
            param = param + ARRAY_SEPARATOR;
        }
        return param;
    }
}
