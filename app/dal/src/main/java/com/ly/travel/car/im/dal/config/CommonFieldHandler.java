package com.ly.travel.car.im.dal.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.ly.travel.car.im.dal.utils.EnvUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;

import java.util.Date;

@Slf4j
public class Common<PERSON>ield<PERSON>andler implements MetaObjectHandler {
    @Override
    public void insertFill(MetaObject metaObject) {
        this.setFieldValByName("env", EnvUtil.getEnv(), metaObject);
        this.setFieldValByName("v", 1L, metaObject);

        if (metaObject.hasGetter("createTime")) {
            Object orgCreateTime = metaObject.getValue("createTime");
            if (orgCreateTime == null) {
                this.setFieldValByName("createTime", new Date(), metaObject);
            }
        }

        this.setFieldValByName("updateTime", new Date(), metaObject);

    }

    @Override
    public void updateFill(MetaObject metaObject) {
        this.setFieldValByName("updateTime", new Date(), metaObject);
    }
}
