package com.ly.travel.car.im.dal.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.travel.car.im.dal.entity.SfcImMsgRecord;
import com.ly.travel.car.im.facade.request.QueryMsgRecordReqDTO;

import java.util.List;

public interface SfcImMsgRecordDao extends IService<SfcImMsgRecord> {

    /**
     * 根据消息id查询
     *
     * @param msgId
     * @return
     */
    SfcImMsgRecord findByMsgId(String msgId);

    /**
     * 查询订单消息数量
     *
     * @param sessionKey
     * @param msgSender
     * @param orderId
     * @param msgStatus
     * @return
     */
    Long selectCount(String sessionKey, Integer msgSender, String orderId, Integer msgStatus);

    /**
     * 根据消息类型查询消息
     *
     * @param sessionKey
     * @return
     */
    SfcImMsgRecord findByMsgRecord(String sessionKey, String orderId, Integer msgType);

    /**
     * 查询用户前五条最新消息
     *
     * @param sessionKey
     * @return
     */
    Page<SfcImMsgRecord> selectRecordPage(Page<SfcImMsgRecord> page, String sessionKey,String orderId);

    /**
     * 查询重复消息
     *
     * @param sessionKey
     * @param msgId
     * @param msgSender
     * @return
     */
    SfcImMsgRecord queryRepeatMsg(String sessionKey, String msgId, Integer msgSender, Integer msgSendStatus);

    /**
     * 查询用户聊天记录
     *
     * @param sessionKey
     * @param createTime
     * @param msgSendTime
     * @param queryDirection
     * @return
     */
    List<SfcImMsgRecord> queryMsgRecord(List<String> sessionKey, String createTime, String msgSendTime, Integer queryDirection, Integer queryNum);

    /**
     * 查询未读消息
     *
     * @return
     */
    List<SfcImMsgRecord> queryUnreadMsg(List<String> sessionKey, Integer msgSender);

    /**
     * 查询用户聊天记录
     *
     * @param req 查询请求参数
     * @return 聊天记录列表
     */
    List<SfcImMsgRecord> queryMsgRecord(QueryMsgRecordReqDTO req);

    /**
     * 查询聊天记录总数
     *
     * @param req 查询请求参数
     * @return 总记录数
     */
    Long countMsgRecord(QueryMsgRecordReqDTO req);

    /**
     * 查询未读消息
     *
     * @return
     */
    List<SfcImMsgRecord> queryUnreadMsgByOrderId(String orderId, Integer msgSender);

}
