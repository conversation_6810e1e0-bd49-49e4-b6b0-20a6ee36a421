package com.ly.travel.car.im.dal.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.travel.car.im.dal.entity.SfcImInfo;
import com.ly.travel.car.im.facade.dto.DriverUnreadMsgDTO;
import com.ly.travel.car.im.model.dto.QueryMsgDTO;

import java.util.List;

public interface SfcImInfoDao extends IService<SfcImInfo> {

    /**
     * 根据sessionKey查询会话列表
     *
     * @return
     */
    SfcImInfo findBySessionKey(String sessionKey,Integer plateId);

    /**
     * 根据sessionKey查询会话列表
     *
     * @return
     */
    SfcImInfo findByMidAndDriverId(Long memberId,String driverId);

    /**
     * 根据sessionKey&订单号查询会话列表
     *
     * @return
     */
    SfcImInfo findSfcImInfo(String sessionKey,String orderId);

    /**
     * 根据用户信息查询数量
     *
     * @param queryMsgDTO
     * @return
     */
    Long findByUserInfoTotal(QueryMsgDTO queryMsgDTO);

    /**
     * 根据用户信息查询分页
     *
     * @param queryMsgDTO
     * @return
     */
    List<SfcImInfo> findByUserInfoPage(QueryMsgDTO queryMsgDTO);

    /**
     * 根据用户信息查询未读消息列表
     *
     * @param queryMsgDTO
     * @return
     */
    List<SfcImInfo> findUnreadMsgInfo(QueryMsgDTO queryMsgDTO);

    /**
     * 获取是否有司机消息列表
     *
     * @param driverUnreadMsgDTOS
     * @return
     */
    List<SfcImInfo> findDriverUnreadMsgInfo(List<DriverUnreadMsgDTO> driverUnreadMsgDTOS);

    /**
     * 根据订单号查询会话
     */
    List<SfcImInfo> findByOrderId(String orderId);

    /**
     * 根据同程会话id查询会话
     */
    SfcImInfo findByTcConversationId(String tcConversationId);
}
