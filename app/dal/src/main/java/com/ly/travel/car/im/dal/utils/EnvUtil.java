package com.ly.travel.car.im.dal.utils;

import org.apache.commons.lang3.StringUtils;

public class EnvUtil {

    /**
     * 获取环境标识
     *
     * @return
     */
    public static String getEnv() {
        String env = EnvUtil.getHostNameEnv();
        if (StringUtils.isBlank(env)) {
            env = "test";
        }
        return env;
    }

    /**
     * 获取环境变量
     * DAOKEENV
     *
     * @return
     */
    private static String getHostNameEnv() {
        String env = System.getenv("DAOKEENV");
        if (StringUtils.startsWith(env, "stage")) {
            return "stage";
        }
        return env;
    }
}
