<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="
		http://www.springframework.org/schema/beans 
		http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/tx
		http://www.springframework.org/schema/tx/spring-tx.xsd
		http://www.springframework.org/schema/aop
		http://www.springframework.org/schema/aop/spring-aop.xsd
		http://www.springframework.org/schema/context 
		http://www.springframework.org/schema/context/spring-context.xsd">

    <context:annotation-config/>
    <context:component-scan base-package="com.ly.travel.car.im"/>
    <aop:aspectj-autoproxy proxy-target-class="true"/>
<!--    <tx:annotation-driven/>-->

    <bean id="dataSource" class="com.ly.dal.datasource.RoutableDataSource" init-method="init" destroy-method="close">
        <property name="env" value="${uniform.env}"/>
        <property name="projectId" value="car.shared.mobility.im.chat.service"/>
        <property name="dbName" value="DCDB_TECarResource"/>
        <property name="connectionProperties">
            <map>
                <entry key="zeroDateTimeBehavior" value="convertToNull"/>
            </map>
        </property>
    </bean>



    <bean id="datasourceProxy" class="org.springframework.jdbc.datasource.LazyConnectionDataSourceProxy">
        <property name="targetDataSource" ref="dataSource" />
    </bean>

    <bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource">
            <ref bean="datasourceProxy" />
        </property>
        <qualifier value="transactionManager"/>
    </bean>

    <bean id="transactionTemplate" class="org.springframework.transaction.support.TransactionTemplate">
        <property name="transactionManager">
            <ref bean="transactionManager" />
        </property>
    </bean>

    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.ly.travel.car.im.dal.mapper"/>
        <property name="sqlSessionFactoryBeanName" value="sqlSessionFactory"/>
    </bean>
<!--    <bean id="commonFieldHandler" class="com.ly.travel.car.im.dal.config.CommonFieldHandler"/>-->

<!--    <bean id="commonGlobalConfig" class="com.baomidou.mybatisplus.core.config.GlobalConfig">-->
<!--        <property name="metaObjectHandler" ref="commonFieldHandler"/>-->
<!--    </bean>-->

    <bean id="sqlSessionFactory" class="com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean">
        <property name="dataSource" ref="datasourceProxy"/>
<!--        <property name="globalConfig" ref="commonGlobalConfig"/>-->
        <property name="mapperLocations" value="classpath:mappers/mysql/*.xml"/>
        <!--		<property name="configLocation" value="classpath:mybatis/mybatis-config.xml"/>-->
        <property name="typeAliasesPackage" value="com.ly.travel.car.im.dal.entity.*"/>
        <!-- 插件注册 -->
        <property name="plugins">
            <list>
                <bean class="com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor">
                    <property name="interceptors">
                        <list>
                            <!-- 注册分页插件 -->
                            <bean class="com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor">
                                <property name="dbType" value="MYSQL"/>
                            </bean>
                            <bean class="com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor">
                            </bean>

                        </list>
                    </property>
                </bean>
            </list>
        </property>
    </bean>




</beans>
