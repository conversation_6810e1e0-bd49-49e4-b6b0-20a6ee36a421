sof.version=${sof.version}
app.name=${app.name}
app.version=${app.version}
app.type=${app.type}

# database
uniform.env=${uniform.env}
sof-env=${sof-env}

# dubbo
dubbo.application.name=${dubbo.application.name}
dubbo.registry.address=${dubbo.registry.address}
dubbo.container=${dubbo.container}
dubbo.service.gsname=${dubbo.service.gsname}
dubbo.service.port=${dubbo.service.port}
dubbo.service.registry.address=${dubbo.service.registry.address}
dubbo.service.deploy.container=${dubbo.service.deploy.container}
dubbo.service.version=${dubbo.service.version}

# http client
http_read_timeout=${http_read_timeout}
connect_timeout=${connect_timeout}

# redis
redis.groupNames=${redis.groupNames}

# mq
mq.nameSrvAddress=${mq.nameSrvAddress}

# dsf
dsf.car.integration.gsName=${dsf.car.integration.gsName}
dsf.car.integration.version=${dsf.car.integration.version}
dsf.car.order.service.gsName=${dsf.car.order.service.gsName}
dsf.car.order.service.version=${dsf.car.order.service.version}
dsf.car.order.core.gsName=${dsf.car.order.core.gsName}
dsf.car.order.core.version=${dsf.car.order.core.version}
