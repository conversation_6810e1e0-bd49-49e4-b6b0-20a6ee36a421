/**
 * LY.com Inc.
 * Copyright (c) 2004-2016 All Rights Reserved.
 */
package com.ly.travel.car.im.web.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @version $Id: SampleController.java, v 0.1 2016年7月11日 下午10:12:13 allen Exp $
 */
@Controller
@RequestMapping("/page")
public class RedirectController {

    /**
     * 用页面A跳转.适用于不需要传参数的纯页面.
     * 在前端调试时，不需要写controller,直接写页面即可.
     *
     * @param module  the module
     * @param page    the page
     * @param request the request
     * @param model   the model
     * @return the string
     */
    @RequestMapping(value = "/{module}/{page}", method = RequestMethod.GET)
    public String page(@PathVariable("module") String module, @PathVariable("page") String page, HttpServletRequest request, Model model) {
        for (Object key : request.getParameterMap().keySet()) {
            String keyStr = key.toString();
            model.addAttribute(keyStr, request.getParameter(keyStr));
        }
        return StringUtils.join(module, "/", page);
    }
}
