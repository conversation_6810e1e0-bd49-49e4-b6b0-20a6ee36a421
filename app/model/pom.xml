<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <groupId>com.ly.travel.car</groupId>
        <artifactId>shared-mobility-im-chat-service-parent</artifactId>
        <version>*******-RELEASE</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>shared-mobility-im-chat-service-model</artifactId>
    <packaging>jar</packaging>

    <name>LY shared-mobility-im-chat-service-model</name>
    <description>LY shared-mobility-im-chat-service-model</description>

    <dependencies>
        <dependency>
            <groupId>com.ly.travel.shared.mobility.supply</groupId>
            <artifactId>mobility-supply-integration-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ly.travel.car</groupId>
            <artifactId>shared-mobility-order-service-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ly.travel.shared.mobility.supply</groupId>
            <artifactId>shared-mobility-supply-order-core-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ly.travel.shared.mobility.supply</groupId>
            <artifactId>shared-mobility-supply-trade-core-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ly.travel.car</groupId>
            <artifactId>shared-mobility-trade-core-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ly.travel.car.distribution</groupId>
            <artifactId>shared-mobility-distribution-order-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ly.travel.car</groupId>
            <artifactId>shared-mobility-im-chat-service-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ly.dal</groupId>
            <artifactId>dal-new</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ly.car</groupId>
            <artifactId>car-support</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
    </dependencies>
</project>
