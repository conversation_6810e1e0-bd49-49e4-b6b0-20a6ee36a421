package com.ly.travel.car.im.model.enums;

import org.apache.commons.lang3.StringUtils;


public enum TencentStatusEnum {
    create_order(201 ,"派单中"),
    decision_success(301, "⻋主接单"),
    pay_success(401, "⽤户已⽀付"),
    setout(501, "待⻋主到达"),
    arrived (601, "⻋主到达起点, 待出发"),
    service (701, "乘客上⻋"),
    driver_finish (801, "⻋主到达终点, 待下⻋"),
    finish (901, "乘客下⻋"),
    canceled (910, "订单取消"),
//腾讯聚合订单状态：201 派单中 301 ⻋主接单 401 ⽤户已⽀付 501 待⻋主到达 601 ⻋主到达起点, 待出发 701 乘客上⻋ 801 ⻋主到达终点, 待下⻋ 901 乘客下⻋ 910 订单取消
    ;

    private Integer code;
    private String msg;

    TencentStatusEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static String getMsgByCode(Integer code) {
        for (TencentStatusEnum enumItem : TencentStatusEnum.values()) {
            if (enumItem.getCode().equals(code)) {
                return enumItem.getMsg();
            }
        }
        return StringUtils.EMPTY;
    }
}
