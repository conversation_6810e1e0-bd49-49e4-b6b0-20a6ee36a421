package com.ly.travel.car.im.model.config;

import com.ly.sof.utils.log.LogContextUtils;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.tcbase.config.AppProfile;
import com.ly.tcbase.config.ConfigCenterClient;
import com.ly.travel.car.im.model.dto.*;
import com.ly.travel.car.im.model.enums.ConfigCenterKeyEnum;
import com.ly.travel.car.im.model.enums.ProductTypeNum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

@Slf4j
public class ImConfigCenter {

    /**
     * 同程im支持消息类型配置
     *
     * @return
     */
    public static List<String> getSupportMsgTypeConfig() {
        try {
            String json = ConfigCenterClient.get("im.support.msg.type.config");
            LoggerUtils.info(log, "[getSupportMsgTypeConfig] 获取im支持消息类型配置rsp：{}", json);
            if (StringUtils.isNotBlank(json)) {
                return FastJsonUtils.fromJSON2List(json, String.class);
            }
        } catch (Exception e) {
            LoggerUtils.error(log, "[getSupportMsgTypeConfig][error] 获取im支持消息类型配置异常", e);
        }
        return null;
    }

    /**
     * 消息配置v1
     *
     * @param productType
     * @param orderId
     * @return
     */
    public static MsgConfigDTO getMsgConfig(String productType, String orderId) {
        LogContextUtils.setCategory("getMsgConfig");
        List<MsgConfigV2DTO> list = getMsgConfigV2();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        if (StringUtils.isBlank(productType) && StringUtils.isNotBlank(orderId)) {
            if (orderId.startsWith(ProductTypeNum.SFC.getOrderPrefix())) {
                productType = ProductTypeNum.SFC.getProductName();
            }
            if (orderId.startsWith(ProductTypeNum.WYC.getOrderPrefix())) {
                productType = ProductTypeNum.WYC.getProductName();
            }
            if (orderId.startsWith(ProductTypeNum.WNC.getOrderPrefix())) {
                productType = ProductTypeNum.WNC.getProductName();
            }
        }
        String finalProductType = productType;
        MsgConfigV2DTO msgConfigV2DTO = list.stream().filter(n -> n.getProductType().equals(finalProductType)).findFirst().orElse(null);
        LoggerUtils.info(log, "获取消息配置rsp：{}", FastJsonUtils.toJSONString(msgConfigV2DTO));
        return Objects.nonNull(msgConfigV2DTO) ? msgConfigV2DTO.getMsgConfigDTO() : null;
    }

    /**
     * 消息配置v2
     *
     * @return
     */
    public static List<MsgConfigV2DTO> getMsgConfigV2() {
        try {
            LogContextUtils.setCategory("getMsgConfig");
            String jsonConfig = ConfigCenterClient.get("im.quick.language.msg");
            LoggerUtils.info(log, "获取消息配置rsp：{}", jsonConfig);
            if (StringUtils.isNotBlank(jsonConfig)) {
                return FastJsonUtils.fromJSON2List(jsonConfig, MsgConfigV2DTO.class);
            }
        } catch (Exception e) {
            LoggerUtils.error(log, "获取消息配置异常", e);
        }
        return null;
    }

    /**
     * platId匹配短信规则配置
     */
    public static SmsRuleConfigDTO matchIMSmsRuleConfigByPlatId(Integer platId) {
        //获取短信规则配置
        List<SmsRuleConfigDTO> imSmsRuleConfigs = ImConfigCenter.getSmsRuleConfig();
        LoggerUtils.info(log, "[matchIMSmsRuleConfigByPlatId] 配置结果：{}", FastJsonUtils.toJSONString(imSmsRuleConfigs));
        if (CollectionUtils.isEmpty(imSmsRuleConfigs)) {
            return new SmsRuleConfigDTO();
        }

        SmsRuleConfigDTO config = imSmsRuleConfigs.stream().filter(n -> n.getPlatId().equals(platId)).findFirst().orElse(null);
        return Objects.nonNull(config) ? config : new SmsRuleConfigDTO();
    }

    /**
     * 消息规则配置
     *
     * @return
     */
    public static List<SmsRuleConfigDTO> getSmsRuleConfig() {
        try {
            String jsonConfig = ConfigCenterClient.get("im.sms.rule.config.platId");
            LoggerUtils.info(log, "[getSmsRuleConfig] 获取消息规则配置rsp：{}", jsonConfig);
            if (StringUtils.isNotBlank(jsonConfig)) {
                return FastJsonUtils.fromJSON2List(jsonConfig, SmsRuleConfigDTO.class);
            }
        } catch (Exception e) {
            LoggerUtils.error(log, "[getSmsRuleConfig][error] 获取消息规则配置异常", e);
        }
        return null;
    }

    /**
     * app推送配置
     *
     * @return
     */
    public static Integer getAppPushConfig() {
        try {
            String pushConfig = ConfigCenterClient.get("im.app.push.config");
            LoggerUtils.info(log, "[getAppPushConfig] 获取app推送配置rsp：{}", pushConfig);
            if (StringUtils.isNotBlank(pushConfig)) {
                return Integer.valueOf(pushConfig);
            }
        } catch (Exception e) {
            LoggerUtils.error(log, "[getAppPushConfig][error] 获取app推送配置异常", e);
        }
        return 0;
    }

    /**
     * 距离配置
     *
     * @return
     */
    public static String getCommonMsgDistanceConfig() {
        try {
            return ConfigCenterClient.get("common.message.distance.config");
        } catch (Exception e) {
            LoggerUtils.error(log, "[getCommonMsgDistanceConfig][error] 获取距离配置异常", e);
        }
        return Strings.EMPTY;
    }

    /**
     * 新老系统开关
     *
     * @return
     */
    public static String getVersion() {
        try {
            String ver = ConfigCenterClient.get("car.shared.mobility.im.chat.service", "im.ver");
            LoggerUtils.info(log, "[getVersion] im.ver：{}", ver);
            return ver;
        } catch (Exception e) {
            LoggerUtils.error(log, "[getVersion][error] 获取im.ver异常", e);
        }
        return Strings.EMPTY;
    }

    /**
     * 夜间提示时间区间
     *
     * @return
     */
    public static TimeRangeDTO getTimeRange() {
        try {
            String timeRange = ConfigCenterClient.get("car.shared.mobility.im.chat.service", "im.night.time.range");
            LoggerUtils.info(log, "[getTimeRange] 夜间提示时间区间：{}", timeRange);
            TimeRangeDTO timeRangeDTO = FastJsonUtils.fromJSONString(timeRange, TimeRangeDTO.class);
            return timeRangeDTO;
        } catch (Exception e) {
            LoggerUtils.error(log, "[getTimeRange][error] 获取夜间提示时间区间异常", e);
        }
        return null;
    }

    /**
     * 历史订单数
     *
     * @return
     */
    public static Integer getHistoryOrderCount() {
        try {
            String orderCount = ConfigCenterClient.get("car.shared.mobility.im.chat.service", "im.order.count");
            LoggerUtils.info(log, "[getHistoryOrderCount] 历史订单数：{}", orderCount);
            return StringUtils.isNotEmpty(orderCount) ? Integer.parseInt(orderCount) : 35;
        } catch (Exception e) {
            LoggerUtils.error(log, "[getHistoryOrderCount][error] 获取历史订单数", e);
        }
        return 35;
    }


    /**
     * 消息规则配置
     *
     * @return
     */
    public static SessionRuleConfigDTO  getSessionKeyRule(Integer platId) {
        try {
            String jsonConfig = ConfigCenterClient.get("im.session.key.config.platId");
            LoggerUtils.info(log, "[getSessionKeyRule] 配置结果：{}", FastJsonUtils.toJSONString(jsonConfig));
            if (StringUtils.isNotBlank(jsonConfig)) {
                List<SessionRuleConfigDTO> sessionRuleConfigDTOS = FastJsonUtils.fromJSON2List(jsonConfig, SessionRuleConfigDTO.class);
                if (CollectionUtils.isEmpty(sessionRuleConfigDTOS)) {
                    return new SessionRuleConfigDTO();
                }

                SessionRuleConfigDTO config = sessionRuleConfigDTOS.stream().filter(n -> n.getPlatId().equals(platId)).findFirst().orElse(null);
                return Objects.nonNull(config) ? config : new SessionRuleConfigDTO();
            }
        } catch (Exception e) {
            LoggerUtils.error(log, "[getSmsRuleConfig][error] 获取消息规则配置异常", e);
        }
        return null;
    }


    /**
     * 特殊渠会员解密
     *
     * @return
     */
    public static DecryptMemberConfigDTO  getDecryptMemberConfig() {
        try {
            String jsonConfig = ConfigCenterClient.get("im.decrypt.member.config");
            LoggerUtils.info(log, "[getDecryptMemberConfig] 配置结果：{}", FastJsonUtils.toJSONString(jsonConfig));
            if (StringUtils.isNotBlank(jsonConfig)) {
                DecryptMemberConfigDTO config = FastJsonUtils.fromJSONString(jsonConfig, DecryptMemberConfigDTO.class);
                return Objects.nonNull(config) ? config : new DecryptMemberConfigDTO();
            }
        } catch (Exception e) {
            LoggerUtils.error(log, "[getDecryptMemberConfig][error] 获取规则配置异常", e);
        }
        return null;
    }
    /**
     * 三方关系配置
     *
     * @return
     */
    public static ThirdPartyConfigDTO getThirdPartyConfig() {
        try {
            String jsonConfig = ConfigCenterClient.get("im.third.party.config");
            LoggerUtils.info(log, "[getThirdPartyConfig] 配置结果：{}", FastJsonUtils.toJSONString(jsonConfig));
            if (StringUtils.isNotBlank(jsonConfig)) {
                ThirdPartyConfigDTO config = FastJsonUtils.fromJSONString(jsonConfig, ThirdPartyConfigDTO.class);
                return Objects.nonNull(config) ? config : new ThirdPartyConfigDTO();
            }
        } catch (Exception e) {
            LoggerUtils.error(log, "[getThirdPartyConfig][error] 获取规则配置异常", e);
        }
        return null;
    }




    /**
     * 约约主动创建会话
     *
     * @return
     */
    public static boolean isOpenChat() {
        try {
            String openChat = ConfigCenterClient.get("car.shared.mobility.im.chat.service", "im.openChat");
            LoggerUtils.info(log, "[isOpenChat] 开启会话：{}", openChat);
            if (StringUtils.isNotEmpty(openChat)){
                return openChat.equals("true");
            }else {
                return false;
            }
        } catch (Exception e) {
            LoggerUtils.error(log, "[isOpenChat][error] 开启会话异常", e);
        }
        return false;
    }

    /**
     * 关闭im会话
     *
     * @return
     */
    public static boolean isCloseChat() {
        try {
            String openChat = ConfigCenterClient.get("car.shared.mobility.im.chat.service", "im.closeChat");
            LoggerUtils.info(log, "[closeChat] 关闭会话：{}", openChat);
            if (StringUtils.isNotEmpty(openChat)){
                return openChat.equals("true");
            }else {
                return false;
            }
        } catch (Exception e) {
            LoggerUtils.error(log, "[closeChat][error] 关闭会话异常", e);
        }
        return false;
    }

    public static boolean isLimitMsg() {
        try {
            String openChat = ConfigCenterClient.get("car.shared.mobility.im.chat.service", "im.limitMsg");
            LoggerUtils.info(log, "[limitMsg] 限制消息：{}", openChat);
            if (StringUtils.isNotEmpty(openChat)){
                return openChat.equals("true");
            }else {
                return false;
            }
        } catch (Exception e) {
            LoggerUtils.error(log, "[limitMsg][error] 限制消息异常", e);
        }
        return false;
    }

    public static String getConfigValue(ConfigCenterKeyEnum key) {
        String value;
        try {
            value = ConfigCenterClient.get(key.getKey());
            if (StringUtils.isBlank(value)) {
                value = key.getDefaultValue();
            }
        } catch (Exception e) {
            LoggerUtils.error(log, "[getConfigValue][error] getConfigValue异常", e);
            value = key.getDefaultValue();

        }
        return value;
    }

    public static boolean isLimitPhone() {
        try {
            String limitPhone = ConfigCenterClient.get("car.shared.mobility.im.chat.service", "im.limitPhone");
            LoggerUtils.info(log, "[limitPhone] 限制电话：{}", limitPhone);
            if (StringUtils.isNotEmpty(limitPhone)){
                return limitPhone.equals("true");
            }else {
                return false;
            }
        } catch (Exception e) {
            LoggerUtils.error(log, "[limitPhone][error] 限制电话异常", e);
        }
        return false;
    }
}
