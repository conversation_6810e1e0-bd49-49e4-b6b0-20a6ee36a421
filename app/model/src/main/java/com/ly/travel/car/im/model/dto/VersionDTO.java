package com.ly.travel.car.im.model.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class VersionDTO implements Serializable {

    private static final long serialVersionUID = 9205490370894419949L;

    /**
     * 系统开关 v1：老版本  v2：新版本
     */
    private String version;

    /**
     * 聊天页
     */
    private ChatDTO chat;

    /**
     * 列表页
     */
    private ListDTO list;


    @Data
    public static class ChatDTO implements Serializable {

        private static final long serialVersionUID = 3903074585286724462L;

        /**
         * 新微信
         */
        private String newWxLink;

        /**
         * 新APP
         */
        private String newAppLink;

        /**
         * 老跳转链接
         */
        private String oldLink;

        private String alipayLink;

        private String sharedAlipayLink;

        private String madaLink;

        private String rideLink;

        private String inLink;

        private String elongLink;
    }

    @Data
    public static class ListDTO implements Serializable {

        private static final long serialVersionUID = 2111440133154814412L;

        /**
         * 新微信
         */
        private String newWxLink;

        /**
         * 新APP
         */
        private String newAppLink;

        /**
         * 老跳转链接
         */
        private String oldLink;

        private String alipayLink;

        private String sharedAlipayLink;

        private String madaLink;

        private String rideLink;

        private String inLink;

        private String elongLink;
    }
}
