package com.ly.travel.car.im.model.dto;

import com.ly.travel.car.im.model.constant.RocketDelayLevel;
import lombok.Data;

import java.io.Serializable;

/**
 * 短信发送配置
 */
@Data
public class SmsRuleConfigDTO implements Serializable {

    private static final long serialVersionUID = -4341464030476837471L;

    /**
     * 平台id
     */
    private Integer platId;

    /**
     * 延迟等级
     */
    private Integer delayLevel = RocketDelayLevel.MINUTE_1;

    /**
     * 短信发送限制次数
     */
    private Integer sentLimitCount = 1;

    /**
     * 短信发送限制有效天数
     */
    private Integer day = 1;
}
