package com.ly.travel.car.im.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OrderChannelEnum {

    XCX(852, "小程序"),
    ANDROID_APP(434, "安卓"),
    IOS_APP(433, "IOS"),
    MaDa_Android(10177, "马达安卓"),
    MaDa_IOS(10178, "马达iOS"),
    IN_XCX(8522, "独立小程序"),
    alipay(10088, "支付宝小程序"),
    SHARED_alipay(10190, "支付宝独小"),
    RIDE(1443, "乘车呗"),
    ELONG_IOS(463, "艺龙IOS"),
    DISTRIBUTION(883, "同程小分销"),
    ELONG_Android(464, "艺龙安卓"),
    ;

    private final int code;
    private final String desc;

    public static OrderChannelEnum getByCode(Integer code) {
        for (OrderChannelEnum e : OrderChannelEnum.values()) {
            if (e.getCode() == code) {
                return e;
            }
        }
        return XCX;
    }

    public boolean isApp() {
        return this.equals(IOS_APP) || this.equals(ANDROID_APP) || this.equals(alipay) || this.equals(SHARED_alipay) || this.equals(MaDa_IOS)|| this.equals(MaDa_Android);
    }

    public boolean isTcAppWithoutMada() {
        return this.equals(IOS_APP) || this.equals(ANDROID_APP)  || this.equals(ELONG_IOS)  || this.equals(ELONG_Android);
    }

    public boolean isMaDa() {
        return this.equals(MaDa_IOS)|| this.equals(MaDa_Android);
    }

    public boolean isAppForDecrypt() {
        return this.equals(IOS_APP) || this.equals(ANDROID_APP) || this.equals(MaDa_IOS)|| this.equals(MaDa_Android);
    }

    public boolean isXcx() {
        return this.equals(XCX)||this.equals(IN_XCX);
    }

    public static boolean isApp(Integer orderChannel) {
        return getByCode(orderChannel).isApp();
    }

    public static boolean isTCAppWithoutMada(Integer orderChannel) {
        return getByCode(orderChannel).isTcAppWithoutMada();
    }

    public static boolean isMaDa(Integer orderChannel) {
        return getByCode(orderChannel).isMaDa();
    }

    public static boolean isAppForDecrypt(Integer orderChannel) {
        return getByCode(orderChannel).isAppForDecrypt();
    }

    public static boolean isXcx(Integer orderChannel) {
        return getByCode(orderChannel).isXcx();
    }

    public static OrderChannelEnum getByPlatId(Integer id) {
        for (OrderChannelEnum e : OrderChannelEnum.values()) {
            if (e.getCode() == id) {
                return e;
            }
        }
        return null;
    }
}
