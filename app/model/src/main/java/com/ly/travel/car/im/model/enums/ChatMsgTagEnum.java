package com.ly.travel.car.im.model.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
public enum ChatMsgTagEnum {

    im_passenger_unread_sms("im_passenger_unread_sms"),

    im_passenger_unread_app("im_passenger_unread_App"),
    
    im_chat_message_dispatch("im_chat_message_dispatch"),
    im_chat_message_distribute("im_chat_message_distribute"),
    ;

    private final String name;

    ChatMsgTagEnum(String name) {
        this.name = name;
    }

    public static ChatMsgTagEnum fromName(String name) {
        for (ChatMsgTagEnum tag : values()) {
            if (StringUtils.equals(tag.getName(), name)) {
                return tag;
            }
        }
        return null;
    }
}

