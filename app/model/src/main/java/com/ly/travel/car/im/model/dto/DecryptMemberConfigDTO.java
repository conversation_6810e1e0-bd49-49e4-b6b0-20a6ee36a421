package com.ly.travel.car.im.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 短信发送配置
 */
@Data
public class DecryptMemberConfigDTO implements Serializable {

    private static final long serialVersionUID = -4341464030476837471L;

    /**
     * 平台id
     */
    private List<Integer> plateIds;

    private String appKey;

    private String appSecret;

    /**
     * 解密会员Token
     */
    private String checkMemberTokenUrl;

}
