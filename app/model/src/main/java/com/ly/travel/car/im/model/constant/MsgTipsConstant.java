package com.ly.travel.car.im.model.constant;

public interface MsgTipsConstant {
    String SEND_FAIL = "暂不可发送自定义消息，可使用下方快捷消息沟通";
    String BEFORE_LIMIT = "当前你可以发送%s条消息，收到回复后可继续沟通";
    String AFTER_LIMIT = "对方回复后，你可以继续发送%s条消息";
    String RISK = "该消息含敏感词，无法发送";
    String PAY_LIMIT = "消息发送失败，支付成功可发送自定义消息";

    /**
     * 改派提醒
     */
    String REASSIGNMENT = "为保障顺利出行，您可以重新寻找车主";
    String SAFE_RISK_WARN = "<div class=\"notice-card-title refuse-tip\">请拒绝线下交易</div>\n" +
            "<div class=\"notice-card-content\">\n" +
            "    <p><span class=\"text-tip\">请勿进行线下或第三方平台交易，并对任何涉及交易和转账的问题保持警惕</span>，否则平台将无法介入处理并保障您的出行和人身财产安全。</p>\n" +
            "</div>";

    String NIGHT_WARN = "<div class=\"notice-card-title refuse-tip\">请注意夜间出行安全</div><div class=\"notice-card-content\"><p><span class=\"text-tip\">请注意夜间出行安全</span>，您的行程预计将于【%s】开始夜间出行请保持警惕，遇到问题及时联系客服或拨打110报警，同程时刻关注夜间出行安全。</p></div>";
}
