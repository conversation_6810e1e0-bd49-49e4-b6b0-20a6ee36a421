package com.ly.travel.car.im.model.dto;

import lombok.Data;

import java.util.Date;
@Data
public class CarPassengerUnreadSubDTO {

    /**
     * 消息ID
     */
    private String msgId;

    /**
     * 会话标识（供应商Code/子供应商Code + 会员ID/unionId + 司机ID）
     */
    private String sessionKey;

    /**
     * 主键
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 修改时间
     */
    private Date modified;

    /**
     * 订单编号
     */
    private String orderId;

    /**
     * 短订短号
     */
    private String orderShowId;

    /**
     * 用户open_id
     */
    private String openId;

    /**
     * 用户union_id
     */
    private String unionId;

    /**
     * 0体系会员ID
     */
    private Long memberId;

    /**
     * 付款类型(0.前付，1.后付)
     */
    private Integer amountType;

    /**
     * 订单状态
     */
    private Integer status;

    /**
     * 支付状态 <br/>
     * 0:未支付；<br/>
     * 1：支付成功；<br/>
     * 2：支付失败；<br/>
     * 3：支付中；<br/>
     * 4：虚拟支付；<br/>
     * 5：客服操作免单；<br/>
     * 6：待补款；<br/>
     * 10：申请退款；<br/>
     * 11：退款成功；<br/>
     * 12：退款失败。<br/>
     */
    private Integer payStatus;

    /**
     * 拼车状态<br/>
     * 0.选择不拼车；<br/>
     * 1.选择拼车；<br/>
     * 2.拼车失败；<br/>
     * 3.拼车成功；<br/>
     */
    private Integer carpoolStatus;

    /**
     * 是否已结算（0:未结算，1已结算）
     */
    private Integer balanceStatus;

    /**
     * 是否跨城（0:同城，1：跨城）
     */
    private Integer crossCity;

    /**
     * 供应商code
     */
    private String supplierCode;

    /**
     * 供应商订单id
     */
    private String supplierOrderId;

    /**
     * 乘客姓名
     */
    private String passengerName;

    /**
     * 乘客电话
     */
    private String passengerCellphone;

    /**
     * 乘客人数
     */
    private Integer passengerNumber;

    /**
     * 总价（单位：分）
     */
    private Integer totalAmount;

    /**
     * 付款金额(单位：分)
     */
    private Integer payAmount;

    /**
     * 结算金额（单位：分）
     */
    private Integer balanceAmount;

    /**
     * 原始金额（单位：分）
     */
    private Integer originAmount;

    /**
     * 折扣金额(单位：分)
     */
    private Integer discountAmount;

    /**
     * 拼车差价退款金额(单位：分)
     */
    private Integer carpoolReturnAmount;

    /**
     * 有损金额(单位：分)
     */
    private Integer lostAmount;

    /**
     * 预退款金额(单位：分)
     */
    private Integer returnAmount;

    /**
     * 已经退款金额(单位：分)
     */
    private Integer returnedAmount;

    /**
     * 乘客确认上车
     */
    private Integer passengerOnCar;

    /**
     * 乘客确认到达目的地
     */
    private Integer passengerArrive;

    /**
     * 乘客上车时间
     */
    private Date passengerOnCarTime;

    /**
     * 乘客到达时间
     */
    private Date passengerArriveTime;

    /**
     * 付款时间
     */
    private Date payTime;

    /**
     * 用车时间
     */
    private Date useTime;

    /**
     * 最早用车时间
     */
    private Date earliestTime;

    /**
     * 最晚用车时间
     */
    private Date latestTime;

    /**
     * 供应商服务时间(司机到达乘客起点)
     */
    private Date serviceTime;

    /**
     * 供应商结束时间(司机到达乘客终点)
     */
    private Date finishTime;

    /**
     * 取消时间
     */
    private Date cancelTime;

    /**
     * 产品id
     */
    private Integer productId;

    /**
     * 分销渠道
     */
    private Long refId;

    /**
     * 环境变量 PROD/TEST
     */
    private String env;

    /**
     * 是否删除，0有效，1删除
     */
    private Integer deleted;

    /**
     * 是否分销订单（0:否,1:是）
     */
    private Integer distributorFlag;

    /**
     * 感谢费
     */
    private Integer thanksFee;

    /**
     * 加价金额(单位：分)
     */
    private Integer addAmount;

    /**
     * 渠道类型
     */
    private Integer platId;
}
