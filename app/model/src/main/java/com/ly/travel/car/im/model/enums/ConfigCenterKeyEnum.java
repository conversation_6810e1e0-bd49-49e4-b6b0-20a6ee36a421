package com.ly.travel.car.im.model.enums;

import lombok.Getter;

/**
 * 虚拟号接入类型
 */
@Getter
public enum ConfigCenterKeyEnum {


    /**
     * 并发锁等待时间
     */
    im_dispatch_concurrent_lock_wait_time("im_dispatch_concurrent_lock_wait_time","3"),
    /**
     * 并发锁过期时间
     */
    im_dispatch_concurrent_lock_expire_time("im_dispatch_concurrent_lock_expire_time","3"),
    im_dispatch_send_msg_timeout("im_dispatch_send_msg_timeout","1000"),
    im_distribute_send_msg_timeout("im_distribute_send_msg_timeout","1000"),


    ;
    public String  key ;
    public String defaultValue;

    ConfigCenterKeyEnum(String key, String defaultValue) {
        this.key = key;
        this.defaultValue = defaultValue;
    }


}
