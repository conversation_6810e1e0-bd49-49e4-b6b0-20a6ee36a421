package com.ly.travel.car.im.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class OrderDetailDTO implements Serializable {

    private static final long serialVersionUID = -7687256106554128098L;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 订单状态
     */
    private int status;

    /**
     * 渠道类型
     */
    private int platId;

    /**
     * 用户open_id
     */
    private String openId;

    /**
     * 产品id
     */
    private int productId;

    /**
     * 供应商订单号
     */
    private String supplierOrderId;

    /**
     * 供应商code
     */
    private String supplierCode;

    /**
     * 用户ID
     */
    private String unionId;

    /**
     * 会员ID
     */
    private String memberId;

    /**
     * 分销渠道ID
     */
    private Long refId;

    /**
     * 渠道类型
     */
    private int channelType;

    /**
     * 司机
     */
    private String driverName;

    /**
     * 司机是否支持IM功能
     */
    private int driverChatFlag;

    /**
     * 司机会话ID
     */
    private String driverChatId;

    /**
     * 车牌号
     */
    private String plateNumber;

    /**
     * 起始地址
     */
    private String startAddress;

    /**
     * 抵达地址
     */
    private String endAddress;

    /**
     * 预估公里
     */
    private BigDecimal estimateKilo;

    /**
     * 起始城市ID
     */
    private int startCityId;

    /**
     * 抵达城市id
     */
    private int endCityId;

    /**
     * 起始经度
     */
    private BigDecimal startLng;

    /**
     * 起始纬度
     */
    private BigDecimal startLat;

    /**
     * 抵达精度
     */
    private BigDecimal endLng;

    /**
     * 抵达纬度
     */
    private BigDecimal endLat;

    /**
     * 最晚用车时间
     */
    private Date latestTime;

    /**
     * 最早用车时间
     */
    private Date earliestTime;

    /**
     * 支付状态
     */
    private int payStatus;

    /**
     * 司机手机号
     */
    private String driverPhone;

    /**
     * 付款金额(单位：分)
     */
    private int payAmount;

    /**
     * 拼车状态
     */
    private int carpoolStatus;

    /**
     * 是否跨城
     */
    private int crossCity;

    /**
     * 车身颜色
     */
    private String carColor;

    /**
     * 车型
     */
    private String carBrand;

    /**
     * 订单类型
     */
    private int orderType;
    private int payCategory;

    /**
     * 乘客虚拟号
     */
    private String passengerVirtualPhone;

    /**
     * 乘客手机号
     */
    private String passengerPhone;

    /**
     * 订单标签集合
     */
    private List<String> orderTags;

    /**
     * 商品流水号
     */
    private String businessOrderNo;

    /**
     * 乘客电话
     */
    private String passengerCellphone;

    /**
     * 司机id
     */
    private String driverId;
}
