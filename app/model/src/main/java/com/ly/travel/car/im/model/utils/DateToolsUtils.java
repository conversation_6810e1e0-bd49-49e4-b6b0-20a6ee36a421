package com.ly.travel.car.im.model.utils;

import com.ly.travel.car.im.model.config.ImConfigCenter;
import com.ly.travel.car.im.model.dto.TimeRangeDTO;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.logging.log4j.util.Strings;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

public abstract class DateToolsUtils {
    public static final String PATTERN_DATE = "yyyy-MM-dd";
    public static final String PATTERN_DATE_MINUTE = "yyyy-MM-dd HH:mm";
    public static final String PATTERN_DATE_TIME = "yyyy-MM-dd HH:mm:ss";
    public static final String PATTERN_MONTH_MINUTE = "MM月dd日 HH:mm";
    public static final String PATTERN_MONTH_MINUTE_SECOND = "MM月dd日 HH:mm:ss";
    public static final String PATTERN_HOUR_MINUTE = "HH:mm";
    public static final String PATTERN_FULL_TIME = "yyyy-MM-dd HH:mm:ss.SSS";
    public static final String PATTERN_LAST_TIME = "yyyy-MM-dd 23:59:59";
    public static final String PATTERN_CUT = "yyyyMMddHHmmss";
    public static final String PATTERN_DATE_TIME_ZERO = "yyyy-M-d HH:mm:ss";
    public static final String PATTERN_MONTH_MINUTE_ZERO = "M月d日HH:mm";
    public static final Date DEFAULT_DATE = new Date(0);

    public static final DateTimeFormatter FORMATTER_DATE_TIME = DateTimeFormatter.ofPattern(PATTERN_DATE_TIME);
    public static final DateTimeFormatter FORMATTER_TIME = DateTimeFormatter.ofPattern(PATTERN_HOUR_MINUTE);

    public static LocalDateTime string2LocalDateTime(String string) {
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return LocalDateTime.parse(string, fmt);
    }

    public static String DateToString(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日 HH:mm");
        return sdf.format(date);
    }

    public static String stringToString(String string) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        Date parse = null;
        try {
            parse = sdf.parse(string);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return DateToString(parse);
    }

    /**
     * 清除时分秒毫秒
     *
     * @return
     */
    public static Date cleanTime(Date date) {
        try {
            return DateUtils.parseDate(DateFormatUtils.format(date, PATTERN_DATE), PATTERN_DATE);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取当前时间，清除时分秒毫秒
     *
     * @return
     */
    public static Date cleanTime() {
        return cleanTime(new Date());
    }

    /**
     * 字符串时间解析
     *
     * @param str
     * @param pattern
     * @return
     */
    public static Date parseDate(final String str, final String pattern) {
        try {
            return DateUtils.parseDate(str, pattern);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 获取默认时间(0时)
     *
     * @return
     */
    public static Date defaultDate() {
        return DEFAULT_DATE;
    }

    public static String toString(Date date, String format) {
        if (date != null) {
            return DateFormatUtils.format(date, format);
        }
        return Strings.EMPTY;
    }

    public static String toString(Date date) {
        if (date != null) {
            return DateFormatUtils.format(date, PATTERN_DATE_TIME);
        }
        return Strings.EMPTY;
    }

    /**
     * 获取月，1-12
     *
     * @param date
     * @return
     */
    public static int getMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.MONTH) + 1;
    }

    /**
     * 获取天，1-31
     *
     * @param date
     * @return
     */
    public static int getDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.DATE);
    }

    /**
     * 获取小时，0-23
     *
     * @param date
     * @return
     */
    public static int getHour(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.HOUR_OF_DAY);
    }

    /**
     * 获取分钟,0-59
     *
     * @param date
     * @return
     */
    public static int getMinute(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.MINUTE);
    }

    /**
     * 获取数字周几
     *
     * @param date
     * @return
     */
    public static int getWeekNumber(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        return cal.get(Calendar.DAY_OF_WEEK) - 1;
    }

    /**
     * 获取周几
     *
     * @param date
     * @return
     */
    public static String getWeekStr(Date date) {
        String[] weekDays = {"周日", "周一", "周二", "周三", "周四", "周五", "周六"};
        return weekDays[getWeekNumber(date)];
    }

    public static long getTimeDif(String date1, String date2) {
        try {
            Date d1 = DateUtils.parseDate(date1, PATTERN_DATE_TIME);
            Date d2 = DateUtils.parseDate(date2, PATTERN_DATE_TIME);
            return d1.getTime() > d2.getTime() ? d1.getTime() - d2.getTime() : d2.getTime() - d1.getTime();
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    public static long getTimeDif(Date d1, Date d2) {
        return d1.getTime() > d2.getTime() ? d1.getTime() - d2.getTime() : d2.getTime() - d1.getTime();
    }

    /**
     * 获取当年第几天
     */
    public static int getDayOfYear(Date date) {
        Instant instant = date.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        LocalDateTime localDateTime = instant.atZone(zoneId).toLocalDateTime();
        return localDateTime.getDayOfYear();
    }

    /**
     * 获取当天剩余时间（秒）
     */
    public static long getLeftTimeOfToday() {
        LocalDateTime midnight = LocalDateTime.now().plusDays(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
        return ChronoUnit.SECONDS.between(LocalDateTime.now(), midnight);
    }

    public static Date addSeconds(Date date, int amount) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.SECOND, amount);
        return cal.getTime();
    }

    public static Date addMinute(Date date, int amount) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(12, amount);
        return cal.getTime();
    }

    public static Date addDay(Date date, int amount) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(5, amount);
        return cal.getTime();
    }

    /**
     * 把时间转换为 “11月09日 星期五 17:35” 这种格式
     *
     * @param date
     * @return
     */
    public static String calDate(Date date) {
        String[] weekDays = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};
        String weekDay = weekDays[getWeekNumber(date)];
        return DateFormatUtils.format(date, "MM月dd日 " + weekDay + " HH:mm");
    }

    /**
     * 是否是今天
     */
    public static boolean isToday(Date date) {
        if (Objects.isNull(date)) {
            return Boolean.FALSE;
        }
        String param = toString(date, PATTERN_DATE);
        String now = toString(new Date(), PATTERN_DATE);
        return StringUtils.equals(param, now);
    }

    /**
     * 月日，星期（当天显示今天），时间
     *
     * @param date
     * @return
     */
    public static String toSpecialString(Date date) {
        boolean isToday = isToday(date);
        if (isToday) {
            return DateFormatUtils.format(date, "MM月dd日 今天 HH:mm");
        } else {
            return calDate(date);
        }
    }

    public static String getShortIdV3() {
        LocalDateTime now = LocalDateTime.now();
        return (long) (now.getYear() - 2017) * 100000000L + (long) now.getDayOfYear() * 86400L + (long) (now.getHour() * 3600) + (long) (now.getMinute() * 60) + (long) now.getSecond() + getUUIDSalt();
    }

    private static String getUUIDSalt() {
        int i = UUID.randomUUID().hashCode() % 1000;
        return formatSaltString(i);
    }

    private static String formatSaltString(int i) {
        i = i < 0 ? -1 * i : i;
        String s = String.valueOf(i);
        switch (s.length()) {
            case 0:
                return "0000";
            case 1:
                return "000" + s;
            case 2:
                return "00" + s;
            case 3:
                return "0" + s;
            default:
                return s;
        }
    }

    /**
     * 用车时间是否在限定营运时间之内
     *
     * @param useTime
     * @return
     */
    public static boolean operatingTime(Date useTime) {
        Date d = cleanTime(useTime);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(d);
        calendar.add(Calendar.HOUR, 1);
        Date start = calendar.getTime();
        calendar.add(Calendar.HOUR, 3);
        Date end = calendar.getTime();

        if (useTime.getTime() >= start.getTime() && useTime.getTime() <= end.getTime()) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * @return <p>1:nextDay是day之后的日期</p>
     * <p>-1:nextDay是day之前的日期</p>
     * <p>0:nextDay与day是同一天</p>
     */
    public static int dayDif(Date day, Date nextDay) {
        ZoneId zoneId = ZoneId.systemDefault();
        Instant dayInstant = day.toInstant();
        LocalDate dayDate = dayInstant.atZone(zoneId).toLocalDate();
        Instant nextDayInstant = nextDay.toInstant();
        LocalDate nextDayDate = nextDayInstant.atZone(zoneId).toLocalDate();
        if (dayDate.isBefore(nextDayDate)) {
            return 1;
        } else if (dayDate.isAfter(nextDayDate)) {
            return -1;
        }
        return 0;
    }

    public static LocalDateTime convert(Date date) {
        ZoneId zoneId = ZoneId.systemDefault();
        Instant dayInstant = date.toInstant();
        LocalDateTime dateTime = dayInstant.atZone(zoneId).toLocalDateTime();
        return dateTime;
    }

    public static LocalDateTime dateToLocalDateTime(Date date) {
        Instant instant = date.toInstant();
        ZoneId zone = ZoneId.systemDefault();
        return LocalDateTime.ofInstant(instant, zone);
    }

    public static boolean dateIsNight(Date date) {
        //判断入参时间和当前时间是否在同一天
        LocalDate earliestTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localDate = new Date().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        if (!earliestTime.isEqual(localDate)) {
            return Boolean.FALSE;
        }

        // 创建一个Calendar实例，并设置时区为本地时区
        Calendar calendar = Calendar.getInstance(TimeZone.getDefault());
        // 设置指定时间
        calendar.setTime(date);
        // 获取指定时间的小时数
        int hourOfDay = calendar.get(Calendar.HOUR_OF_DAY);
        //获取时间区间配置
        TimeRangeDTO timeRange = ImConfigCenter.getTimeRange();
        // 判断指定时间是否在夜间20:00-06:00之间
        if (hourOfDay >= timeRange.getStartTime() || hourOfDay < timeRange.getEndTime()) {
            return Boolean.TRUE;
        } else {
            return Boolean.FALSE;
        }
    }
}
