package com.ly.travel.car.im.model.utils;

import com.alibaba.fastjson.JSON;
import com.ly.travel.car.im.facade.dto.BaseResponse;
import com.ly.travel.car.im.facade.dto.CheckMemberTokenDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;
import java.util.HashMap;

@Slf4j
public abstract class PlatformUtils {

    /**
     * 是否APP
     *
     * @param platform
     * @return
     */
    public static boolean isApp(String platform) {
        return StringUtils.equalsIgnoreCase(platform, "APP");
    }

    /**
     * 是否WX
     *
     * @param platform
     * @return
     */
    public static boolean isWX(String platform) {
        return StringUtils.equalsIgnoreCase(platform, "WX");
    }

    /**
     * 获取修正版memberId
     *
     * @param
     * @param memberId
     * @return
     */
    public static String handlerMemberId(String memberId, String platform) {
        if (StringUtils.isNotBlank(platform) && StringUtils.equalsIgnoreCase(platform, "APP")) {
            try {
                return memberIdAESDecrypt(memberId);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        } else {
            return Strings.EMPTY;
        }
    }

    /**
     * 解密会员ID
     *
     * @param oMemberId
     * @return
     * @throws UnsupportedEncodingException
     * @throws Exception
     */
    public static String memberIdAESDecrypt(String oMemberId) throws UnsupportedEncodingException, Exception {
        String memberId = "";
        String key = "";
        byte[] rijnIV = new byte[]{19, 51, 93, 127, 82, 41, 44, 21, 59, 81, 85, 35, 79, 25, 54, 61};
        if (String.valueOf(oMemberId).startsWith("A0_")) {
            oMemberId = String.valueOf(oMemberId).replace("A0_", "");
            key = "a!Q@#z$Eddtc&&*!jhd(HjitEfhyF^%b";
        } else if (String.valueOf(oMemberId).startsWith("I0_")) {
            oMemberId = String.valueOf(oMemberId).replace("I0_", "");
            key = "i!Q@#z$Eddtc&&*!jhd(HjitEfhyF^%b";
        } else {
            rijnIV = new byte[]{84, 67, 77, 111, 98, 105, 108, 101, 91, 65, 69, 83, 95, 73, 86, 93};
            key = "q!Q@#z$Eddtc&&*!jhd(HjitEfhy)^%r";
        }

        byte[] data = Utility.asBin(oMemberId);
        byte[] keyBytes = key.getBytes("UTF-8");
        memberId = AesHelper.aesDecrypt(data, keyBytes, rijnIV);
        return memberId;
    }

    public static void main(String[] args) {
        String t = "158631936";
        System.out.println(memberIdDecrypt(t));
    }

    public static String memberIdDecrypt(String data){
        try {
            String key = "wCxH6J3M0dK9zln7xhb6Dg==";
            String iv = "2335462726362347";
            byte[] saltKey = Base64.decodeBase64(key.getBytes(StandardCharsets.UTF_8));
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            SecretKeySpec k = new SecretKeySpec(saltKey, "AES");
            cipher.init(Cipher.DECRYPT_MODE, k, new IvParameterSpec(iv.getBytes()));
            String memberStr = new String(cipher.doFinal(Base64.decodeBase64(data)), StandardCharsets.UTF_8);
            // 使用 '#' 作为分隔符分割字符串
            String[] memberInfo = memberStr.split("#");
            // 检查是否至少有三个部分，并获取第一个部分
            if (memberInfo.length > 2) {
                return memberInfo[2];
            } else {
                return null;
            }
        } catch (Exception ex) {
            return null;
        }
    }

    public static class Utility {
        public Utility() {
        }

        public static String asHex(byte[] buf) {
            StringBuffer strBuf = new StringBuffer(buf.length * 2);

            for (int i = 0; i < buf.length; ++i) {
                if ((buf[i] & 255) < 16) {
                    strBuf.append("0");
                }
                strBuf.append(Long.toString((long) (buf[i] & 255), 16));
            }

            return strBuf.toString();
        }

        public static byte[] asBin(String src) {
            if (src.length() < 1) {
                return null;
            } else {
                byte[] encrypted = new byte[src.length() / 2];

                for (int i = 0; i < src.length() / 2; ++i) {
                    int high = Integer.parseInt(src.substring(i * 2, i * 2 + 1), 16);
                    int low = Integer.parseInt(src.substring(i * 2 + 1, i * 2 + 2), 16);
                    encrypted[i] = (byte) (high * 16 + low);
                }

                return encrypted;
            }
        }
    }

    private static class AesHelper {
        public AesHelper() {
        }

        public static String aesDecrypt(byte[] cipherText, byte[] rijnKey, byte[] rijnIV) throws Exception {
            return AesDecrypt.aesDecrypt(cipherText, rijnKey, rijnIV);
        }

        public static byte[] aesEncrypt(String plainText, byte[] rijnKey, byte[] rijnIV) throws Exception {
            byte[] byteContent = plainText.getBytes("utf-8");
            return AesEncrypt.aesEncrypt(byteContent, rijnKey, rijnIV);
        }
    }

    private static class AesEncrypt {
        public AesEncrypt() {
        }

        public static byte[] aesEncrypt(byte[] plainText, byte[] rijnKey, byte[] rijnIV) throws Exception {
            try {
                SecretKey secretKey = new SecretKeySpec(rijnKey, "AES");
                IvParameterSpec ivSpec = new IvParameterSpec(rijnIV);
                Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
                cipher.init(1, secretKey, ivSpec);
                byte[] decrypt = cipher.doFinal(plainText);
                return decrypt;
            } catch (GeneralSecurityException var7) {
                return null;
            }
        }
    }

    private static class AesDecrypt {
        public AesDecrypt() {
        }

        public static String aesDecrypt(byte[] cipherText, byte[] rijnKey, byte[] rijnIV) throws Exception {
            try {
                SecretKey secretKey = new SecretKeySpec(rijnKey, "AES");
                IvParameterSpec ivSpec = new IvParameterSpec(rijnIV);
                Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
                cipher.init(2, secretKey, ivSpec);
                byte[] decrypt = cipher.doFinal(cipherText);
                return new String(decrypt, "UTF-8");
            } catch (GeneralSecurityException var7) {
                return Strings.EMPTY;
            }
        }
    }
}
