package com.ly.travel.car.im.model.enums;

import com.ly.travel.car.tradecore.model.enums.OrderState;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter

public enum OrderStatusEnum {

    DRAFT(0, "待派单"),
    DISPATCHING(1, "派单中"),
    CANCELED(2, "订单取消"),
    RECEIVING_ORDER(3, "司机已接单"),
    RE_DISPATCHING(4, "订单改派中"),
    AWAITING_TRAVEL(5, "司机已到达"),
    IN_TRIP(6, "行程中"),
    TRIP_FINISHED(7, "行程结束"),
    ORDER_CLOSED(8, "订单完成"),
    PRE_CREATED(11, "等待派车"),
    PRE_TO_FORM_ORDER(12, "预约单已转正式单"),
    PRE_ORDER_CANCELED(13, "预约单已取消"),
    PRE_ORDER_DELAY_CANCELED(14, "派车中"),
    SUPPLY_CONFIRM(15, "供应商已确认");

    private final int code;
    private final String desc;
    private static final Map<Integer, OrderStatusEnum> ENUMS = new HashMap();
    public static OrderStatusEnum getByCode(int code) {
        return (OrderStatusEnum)ENUMS.get(code);
    }
    public int getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    private OrderStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    static {
        OrderStatusEnum[] var0 = values();
        int var1 = var0.length;

        for(int var2 = 0; var2 < var1; ++var2) {
            OrderStatusEnum value = var0[var2];
            ENUMS.put(value.code, value);
        }

    }

}
