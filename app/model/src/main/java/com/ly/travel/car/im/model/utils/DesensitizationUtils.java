package com.ly.travel.car.im.model.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class DesensitizationUtils {

    private final static String IGNORE_SPECIAL_CHAR_REGEX = "[`~!@#$%^&*()+=|{}':;',\\\\[\\\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]|\\s*";

    /**
     * 敏感词脱敏
     *
     * @param value          内容
     * @param sensitiveWords 敏感词
     */
    public static String desensitization(String value, String sensitiveWords) {
        if (Objects.isNull(value) || "".equals(value)) {
            return null;
        }

        if (Objects.isNull(sensitiveWords) || "".equals(sensitiveWords)) {
            return null;
        }

        if (sensitiveWords.length() > value.length()) {
            return null;
        }

        // 敏感词
        char[] sensitiveChars = sensitiveWords.toCharArray();

        // 消息内容
        char[] valueChars = value.toCharArray();
        List<Integer> eIndex = new ArrayList<>();
        List<Integer> beginIndex = new ArrayList<>();
        int index = 0;
        for (int i = 0; i < valueChars.length; i++) {
            char sensitiveChar = sensitiveChars[index];
            char valueChar = valueChars[i];
            if (sensitiveChar == valueChar) {
                index++;
                // 相等的下标
                beginIndex.add(i);
            } else {
                // 判断是否为特殊字符
                List<Character> characters = stringToList();
                if (!characters.contains(valueChar)) { // 不是特殊字符
                    beginIndex.clear();
                    index = 0;
                }
            }
            if (sensitiveChars.length == index) {
                if (beginIndex.size() == sensitiveChars.length) {
                    eIndex.addAll(beginIndex);
                }
                beginIndex.clear();
                index = 0;
            }
        }

        // 替换敏感词
        if (!eIndex.isEmpty()) {
            StringBuilder sb = new StringBuilder(value);
            for (Integer e : eIndex) {
                sb.setCharAt(e, '*');
            }
            return sb.toString();
        }
        return null;
    }

    /**
     * String转List
     */
    private static List<Character> stringToList() {
        return IGNORE_SPECIAL_CHAR_REGEX.chars()
                .mapToObj(c -> (char) c)
                .collect(Collectors.toList());
    }
}
