package com.ly.travel.car.im.model.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
public enum CarpoolStatusEnum {

    NOT_CARPOOL(0, "不拼座"),

    CARPOOLING(1, "拼座中"),

    CARPOOL_UNSUCCESS(2, "未拼成"),

    CARPOOL_SUCCESS(3, "已拼成"),
    ;

    private Integer code;

    private String msg;

    CarpoolStatusEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static String getMsgByCode(Integer code) {
        for (CarpoolStatusEnum enumItem : CarpoolStatusEnum.values()) {
            if (enumItem.getCode().equals(code)) {
                return enumItem.getMsg();
            }
        }
        return StringUtils.EMPTY;
    }
}
