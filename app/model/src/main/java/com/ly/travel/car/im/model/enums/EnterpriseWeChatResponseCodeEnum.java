package com.ly.travel.car.im.model.enums;

import org.apache.logging.log4j.util.Strings;

import java.util.Objects;

/**
 * 企微消息推送响应码
 */
public enum EnterpriseWeChatResponseCodeEnum {

    CODE_1000(1000, "请求成功"),
    CODE_400(400, "缺少鉴权参数"),
    CODE_4011(4011, "无效的时间戳"),
    CODE_4012(4012, "无效的签名"),
    CODE_4013(4013, "重复的签名"),
    CODE_204001(204001, "请求设备,消息发送失败"),
    CODE_204002(204002, "非私域好友"),
    CODE_204004(204004, "未找到可用账号"),
    CODE_204005(204005, "通道占用"),
    ;

    private Integer code;
    private String message;

    EnterpriseWeChatResponseCodeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static String getMessageByCode(Integer code) {
        for (EnterpriseWeChatResponseCodeEnum value : EnterpriseWeChatResponseCodeEnum.values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value.getMessage();
            }
        }
        return Strings.EMPTY;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
