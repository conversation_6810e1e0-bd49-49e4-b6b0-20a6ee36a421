package com.ly.travel.car.im.model.utils;

import com.ly.travel.car.im.facade.enums.MsgSenderEnum;
import com.ly.travel.car.im.facade.enums.MsgTypeEnum;
import org.apache.commons.lang3.StringUtils;

/**
 * 消息类型转换工具类
 */
public class MsgTypeConverter {

    /**
     * 将字符串消息类型转换为枚举类型
     *
     * @param messageType 字符串消息类型（TEXT: 文本, IMAGE: 图片, AUDIO: 语音, LOCATION: 坐标）
     * @return 对应的MsgTypeEnum值
     */
    public static MsgTypeEnum convertMessageType(String messageType) {
        if (StringUtils.isBlank(messageType)) {
            return null;
        }

        switch (messageType) {
            case "TEXT":
            case "CUSTOM_TEXT":
                return MsgTypeEnum.CUSTOM_TEXT;
            case "IMAGE":
                return MsgTypeEnum.IMAGE;
            case "AUDIO":
                return MsgTypeEnum.VOICE;
            case "LOCATION":
                return MsgTypeEnum.LOC;
            case "QUICK_MSG":
                return MsgTypeEnum.QUICK_MSG;
            case "SYSTEM":
                return MsgTypeEnum.SYSTEM;
            case "NOTICE":
                return MsgTypeEnum.TIP;
            case "NIGHT_WARN":
                return MsgTypeEnum.NIGHT_WARN;
            case "DISTRIBUTE_CUSTOM_TEXT":
                return MsgTypeEnum.DISTRIBUTE_CUSTOM_TEXT;
            case "DISTRIBUTE_READ_ACK":
                return MsgTypeEnum.DISTRIBUTE_READ_ACK;
            default:
                return null;
        }
    }

    /**
     * 将字符串发送方转换为枚举类型
     *
     * @param sender 字符串发送方（CUSTOMER_SERVICE: 客服, DRIVER: 司机）
     * @return 对应的MsgSenderEnum值
     */
    public static MsgSenderEnum convertSender(String sender) {
        if (StringUtils.isBlank(sender)) {
            return null;
        }

        switch (sender) {
            case "CUSTOMER_SERVICE":
                return MsgSenderEnum.PASSENGER; // 假设客服是乘客方
            case "DRIVER":
                return MsgSenderEnum.DRIVER;
            case "SYSTEM":
                return MsgSenderEnum.SYS;
            default:
                return null;
        }
    }
} 