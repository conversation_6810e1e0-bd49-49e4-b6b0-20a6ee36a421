package com.ly.travel.car.im.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 消息分发DTO
 */
@Data
public class ChatMessageDispatchDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    private String orderId;

    /**
     * 消息ID
     */
    private String msgId;

    /**
     * 供应商代码
     */
    private String supplierCode;

    /**
     * 供应商订单号
     */
    private String passengerOrderGuid;

    /**
     * 最新时间
     */
    private String latestTime;

    /**
     * 投递状态
     */
    private Integer status;
} 