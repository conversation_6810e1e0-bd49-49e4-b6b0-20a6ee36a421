package com.ly.travel.car.im.model.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 系统消息错误映射枚举
 * 用于将后端错误消息映射为用户友好的消息
 */
public enum SystemMsgErrorMappingEnum {

    TRIP_EXPIRED("行程出发时间已过期", "该行程已结束，无法与车主发送消息"),
    SENSITIVE_WORD("敏感词校验", "发送失败，消息不可包含敏感词"),
    SYSTEM_ERROR("", "系统消息发送失败");

    private final String errorKeyword;
    private final String userMessage;

    SystemMsgErrorMappingEnum(String errorKeyword, String userMessage) {
        this.errorKeyword = errorKeyword;
        this.userMessage = userMessage;
    }

    /**
     * 根据错误消息进行模糊匹配，返回对应的用户友好消息
     * @param errorMessage 原始错误消息
     * @return 用户友好的错误消息
     */
    public static String mapErrorMessage(String errorMessage) {
        if (StringUtils.isBlank(errorMessage)) {
            return SYSTEM_ERROR.getUserMessage();
        }
        
        for (SystemMsgErrorMappingEnum mapping : SystemMsgErrorMappingEnum.values()) {
            if (StringUtils.isNotBlank(mapping.getErrorKeyword()) && 
                errorMessage.contains(mapping.getErrorKeyword())) {
                return mapping.getUserMessage();
            }
        }
        
        // 如果没有匹配到任何关键词，返回默认的系统错误消息
        return SYSTEM_ERROR.getUserMessage();
    }

    public String getErrorKeyword() {
        return errorKeyword;
    }

    public String getUserMessage() {
        return userMessage;
    }
} 