package com.ly.travel.car.im.model.dto;

import com.google.common.collect.Lists;
import com.ly.travel.car.im.facade.dto.Pagination;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class QueryMsgDTO extends Pagination.Pageable implements Serializable {

    private static final long serialVersionUID = 1786363647535672412L;

    /**
     * 用户id
     */
    private String unionId;

    /**
     * 会员id
     */
    private Long memberId;

    /**
     * 会话标识（供应商Code/子供应商Code + 会员ID/unionId + 司机ID）
     */
    private String sessionKey;

    /**
     * 最新消息时间
     */
    private String latestMsgTime;
    /**
     * 订单号
     */
    private String orderSerialNo;

    /**
     * 是否只查询未读消息
     */
    private boolean onlyQueryUnreadMsg;

    private Integer plateId;


    /**
     * 会员id
     */
    private List<Long> memberIds;

}
