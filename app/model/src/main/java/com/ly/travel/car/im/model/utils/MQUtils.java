package com.ly.travel.car.im.model.utils;

import com.ly.sof.api.mq.common.UniformEvent;
import com.ly.sof.utils.log.LoggerUtils;
import com.ly.sof.utils.mapping.FastJsonUtils;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class MQUtils {
    public static String getMsgBody(UniformEvent event) {
        Object obj = event.getPayload();
        LoggerUtils.info(log, "[getMsgBody] payLoad = {}", FastJsonUtils.toJSONString(obj));
        if (obj instanceof byte[]) {
            return new String((byte[]) obj);
        } else {
            return FastJsonUtils.toJSONString(obj);
        }
    }
}
