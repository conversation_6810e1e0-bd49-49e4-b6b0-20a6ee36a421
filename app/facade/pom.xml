<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <groupId>com.ly.travel.car</groupId>
        <artifactId>shared-mobility-im-chat-service-parent</artifactId>
        <version>*******-RELEASE</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>shared-mobility-im-chat-service-facade</artifactId>
    <packaging>jar</packaging>

    <name>LY shared-mobility-im-chat-service-facade</name>
    <description>LY shared-mobility-im-chat-service-facade</description>

    <dependencies>
        <dependency>
            <groupId>com.ly.sof</groupId>
            <artifactId>sof-facade-base</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.ws.rs</groupId>
            <artifactId>javax.ws.rs-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ly.dal</groupId>
            <artifactId>dal-new</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ly.sof</groupId>
            <artifactId>sof-utils</artifactId>
        </dependency>
    </dependencies>
</project>
