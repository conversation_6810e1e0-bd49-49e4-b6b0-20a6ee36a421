package com.ly.travel.car.im.facade.request;

import com.ly.travel.car.im.facade.dto.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class QueryUnreadMsgCountReqDTO extends BaseDTO {

    private static final long serialVersionUID = 8046311428366392704L;

    /**
     * 会话标识（供应商Code/子供应商Code + 会员ID/unionId + 司机ID）
     */
    private String sessionKey;

    /**
     * 产品类型 0：顺风车  1:网约车  2:万能车
     */
    private Integer productType;

    private Integer platId;
}
