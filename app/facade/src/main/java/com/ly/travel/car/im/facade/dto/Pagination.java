package com.ly.travel.car.im.facade.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

@Data
public class Pagination<T> implements Serializable {

    private static final long serialVersionUID = -5771563934094450475L;

    /**
     * 分页参数
     */
    @JsonIgnore
    private Pageable pageable;

    /**
     * 数据
     */
    private List<T> list;

    /**
     * 总页数
     */
    private long totalPage;

    /**
     * 总条数
     */
    private long totalElement;

    /**
     * 当前页
     */
    private long currentPage;

    public Pagination(List<T> list, long totalElement, Pageable pageable) {
        if (list == null) {
            this.list = Collections.emptyList();
        } else {
            this.list = list;
        }
        this.totalElement = totalElement;
        this.pageable = pageable;
        this.totalPage = (long) Math.ceil(totalElement / (double) pageable.getSize());
        this.currentPage = pageable.getPage();
    }

    public <R> Pagination<R> map(Function<T, R> map) {
        List<R> newList = this.list.stream().map(map).collect(Collectors.toList());
        return new Pagination<>(newList, totalElement, pageable);
    }

    public Pagination<T> forEach(Consumer<T> action) {
        this.list.forEach(action);
        return this;
    }

    @Data
    public static class Pageable {

        /**
         * 页数
         */
        private int page = 0;

        /**
         * 条数
         */
        private int size = 30;

        public int getOffset() {
            return page * size;
        }
    }
}
