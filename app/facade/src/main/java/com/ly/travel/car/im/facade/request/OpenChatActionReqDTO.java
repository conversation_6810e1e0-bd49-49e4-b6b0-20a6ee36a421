package com.ly.travel.car.im.facade.request;

import com.ly.travel.car.im.facade.dto.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class OpenChatActionReqDTO extends BaseDTO {

    private static final long serialVersionUID = -7564099462105570509L;

    /**
     * 同程订单号（供应链）
     */
    private String tcOrderId;

    /**
     * 供应商订单号
     */
    private String supplierOrderId;

    /**
     * 供应商code
     */
    private String supplierCode;

    /**
     * 同程会话id
     */
    private String tcConversationId;

    /**
     * 供应商会话id
     */
    private String supplierConversationId;

    /**
     * 子供应商订单号
     */
    private String subSupplierOrderId;

    /**
     * 乘客id
     */
    private String passengerId;

    /**
     * 司机id
     */
    private String driverId;
}
