package com.ly.travel.car.im.facade.response;

import com.ly.sof.facade.base.BaseResponseDTO;
import com.ly.travel.car.im.facade.dto.MsgDTO;
import com.ly.travel.car.im.facade.dto.Pagination;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class QueryMsgRspDTO extends BaseResponseDTO {

    private static final long serialVersionUID = 5851681402632121509L;

    private Pagination<MsgDTO> pagination;

    public static QueryMsgRspDTO fail(String traceId, String errorCode, String errorMessage) {
        QueryMsgRspDTO response = new QueryMsgRspDTO();
        response.setTraceId(traceId);
        response.setSuccess(false);
        response.setErrorCode(errorCode);
        response.setErrorMessage(errorMessage);
        return response;
    }

    public static QueryMsgRspDTO success(String traceId, Pagination<MsgDTO> pagination) {
        QueryMsgRspDTO response = new QueryMsgRspDTO();
        response.setTraceId(traceId);
        response.setPagination(pagination);
        response.setSuccess(true);
        return response;
    }
}
