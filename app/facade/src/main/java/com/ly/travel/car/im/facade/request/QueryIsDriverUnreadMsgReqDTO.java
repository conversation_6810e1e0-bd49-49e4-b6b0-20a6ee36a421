package com.ly.travel.car.im.facade.request;

import com.ly.travel.car.im.facade.dto.BaseDTO;
import com.ly.travel.car.im.facade.dto.DriverUnreadMsgDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class QueryIsDriverUnreadMsgReqDTO extends BaseDTO {

    private static final long serialVersionUID = 8895081223836016384L;

    /**
     * 是否有未读消息司机
     */
    private List<DriverUnreadMsgDTO> driverMessageRequests;
}
