package com.ly.travel.car.im.facade.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ly.dal.util.DateUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class MsgRecordDTO implements Serializable {

    private static final long serialVersionUID = 3462071493561463203L;

    /**
     * 消息记录ID
     */
    private Long msgRecordId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DateUtil.DATE_PATTERN_YYYY_MM_DD_HH_MM_SS, timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = DateUtil.DATE_PATTERN_YYYY_MM_DD_HH_MM_SS, timezone = "GMT+8")
    private Date updateTime;

    /**
     * 会话标识（供应商Code/子供应商Code + 会员ID/unionId + 司机ID）
     */
    private String sessionKey;

    /**
     * 用户unionId
     */
    private String unionId;

    /**
     * 会员ID
     */
    private Long memberId;

    /**
     * 司机车牌号
     */
    private String plateNumber;

    /**
     * 供应商Code
     */
    private String supplierCode;

    /**
     * 司机昵称
     */
    private String driverNickName;

    /**
     * 司机性别 0-未知 1-男 2-女
     */
    private Integer driverGender;

    /**
     * 司机ID
     */
    private Long driverId;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 供应商订单号
     */
    private String supplierOrderId;

    /**
     * 消息唯一ID
     */
    private String msgId;

    /**
     * 消息内容
     */
    private MsgContentDTO msgContent;

    /**
     * 消息类型 0-快捷消息 1-自定义文本 2-定位 3-语音 4-图片 5-卡片消息 6-系统消息 7-提示公告
     */
    private Integer msgType;

    /**
     * 消息发送者 1-乘客 2-司机
     */
    private Integer msgSender;

    /**
     * 场景类型（同顺风车订单状态枚举）
     */
    private Integer sceneType;

    /**
     * 消息状态 0-未读 1-已读
     */
    private Integer msgStatus;

    /**
     * 消息发送状态 0-失败 1-成功
     */
    private Integer msgSendStatus;

    /**
     * 消息发送时间
     */
    @JsonFormat(pattern = DateUtil.DATE_PATTERN_YYYY_MM_DD_HH_MM_SS, timezone = "GMT+8")
    private Date msgSendTime;

    /**
     * 消息版本，用于区分不同迭代消息格式的兼容
     */
    private Integer msgVersion;

    /**
     * 消息投递状态
     */
    private Integer dispatchStatus;
}
