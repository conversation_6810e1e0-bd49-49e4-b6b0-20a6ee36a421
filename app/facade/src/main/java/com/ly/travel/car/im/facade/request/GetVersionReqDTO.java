package com.ly.travel.car.im.facade.request;

import com.ly.sof.facade.base.BaseRequestDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class GetVersionReqDTO extends BaseRequestDTO {

    private static final long serialVersionUID = 7675288476664965022L;

    /**
     * 0：列表页  1：聊天页
     */
    private Integer type;

    /**
     * 渠道 WX、APP
     */
    private String platform;


    /**
     * 渠道id
     */
    private Integer platId;
}
