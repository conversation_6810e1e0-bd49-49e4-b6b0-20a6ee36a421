package com.ly.travel.car.im.facade.dto;

import com.ly.sof.facade.base.BaseRequestDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
public class BaseDTO extends BaseRequestDTO implements Serializable {

    private static final long serialVersionUID = 4264947355379981480L;

    /**
     * 用户id
     */
    private String unionId;

    /**
     * 会员id
     */
    private String memberId;

    /**
     * openId
     */
    private String openId;

    /**
     * XCX：小程序 WX：小程序H5 APP：app WEB：M站
     */
    private String platform;


    /**
     *
     */
    private Integer platId;


    /**
     * 页数
     */
    private int page = 0;

    /**
     * 条数
     */
    private int size = 30;

    public int getOffset() {
        return page * size;
    }

    public String filter1() {
        return StringUtils.defaultIfBlank(unionId, memberId);
    }
}
