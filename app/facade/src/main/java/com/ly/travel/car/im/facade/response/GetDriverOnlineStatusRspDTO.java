package com.ly.travel.car.im.facade.response;

import com.ly.sof.facade.base.BaseResponseDTO;
import com.ly.travel.car.im.facade.dto.DriverOnlineStatusDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class GetDriverOnlineStatusRspDTO extends BaseResponseDTO {

    private static final long serialVersionUID = 5411987146769501731L;

    private DriverOnlineStatusDTO driverOnlineStatusDTO;

    public static GetDriverOnlineStatusRspDTO fail(String traceId, String errorCode, String errorMessage) {
        GetDriverOnlineStatusRspDTO response = new GetDriverOnlineStatusRspDTO();
        response.setTraceId(traceId);
        response.setSuccess(false);
        response.setErrorCode(errorCode);
        response.setErrorMessage(errorMessage);
        return response;
    }

    public static GetDriverOnlineStatusRspDTO success(String traceId, DriverOnlineStatusDTO driverOnlineStatusDTO) {
        GetDriverOnlineStatusRspDTO response = new GetDriverOnlineStatusRspDTO();
        response.setTraceId(traceId);
        response.setDriverOnlineStatusDTO(driverOnlineStatusDTO);
        response.setSuccess(true);
        return response;
    }
}
