package com.ly.travel.car.im.facade.request;

import com.ly.travel.car.im.facade.dto.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class QueryMsgReqDTO extends BaseDTO {

    private static final long serialVersionUID = -8933711337322338957L;

    /**
     * 会话标识（供应商Code/子供应商Code + 会员ID/unionId + 司机ID）
     */
    private String sessionKey;
    /**
     * 订单号
     */
    private String orderSerialNo;

    private Integer platId;
}
