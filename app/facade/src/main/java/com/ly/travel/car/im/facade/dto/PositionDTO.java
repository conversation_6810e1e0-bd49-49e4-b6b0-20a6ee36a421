package com.ly.travel.car.im.facade.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class PositionDTO implements Serializable {

    private static final long serialVersionUID = 9014981204779672654L;

    /**
     * 区县编码
     */
    private String adCode;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 城市名
     */
    private String cityName;

    /**
     * 经度
     */
    private String lat;

    /**
     * 纬度
     */
    private String lon;

    /**
     * 目的地详细地址（短地址）
     */
    private String shortAddr;
}
