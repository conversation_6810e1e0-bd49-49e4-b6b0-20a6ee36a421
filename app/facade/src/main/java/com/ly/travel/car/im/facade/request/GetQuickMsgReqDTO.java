package com.ly.travel.car.im.facade.request;

import com.ly.travel.car.im.facade.dto.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class GetQuickMsgReqDTO extends BaseDTO {

    private static final long serialVersionUID = -6593344964441018535L;

    /**
     * 场景类型（同订单状态枚举）
     */
    private Integer sceneType;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 产品类型 sfc=顺风车 wyc=网约车 wnc=万能车
     */
    private String productType;
}
