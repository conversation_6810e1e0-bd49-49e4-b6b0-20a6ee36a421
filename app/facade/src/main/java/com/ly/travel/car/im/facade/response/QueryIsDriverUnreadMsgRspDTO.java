package com.ly.travel.car.im.facade.response;

import com.ly.sof.facade.base.BaseResponseDTO;
import com.ly.travel.car.im.facade.dto.DriverUnreadMsgDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class QueryIsDriverUnreadMsgRspDTO extends BaseResponseDTO {

    private static final long serialVersionUID = -8374292486473856764L;

    private List<DriverUnreadMsgDTO> unreadMsgDTOS;

    public static QueryIsDriverUnreadMsgRspDTO fail(String traceId, String errorCode, String errorMessage) {
        QueryIsDriverUnreadMsgRspDTO response = new QueryIsDriverUnreadMsgRspDTO();
        response.setTraceId(traceId);
        response.setSuccess(false);
        response.setErrorCode(errorCode);
        response.setErrorMessage(errorMessage);
        return response;
    }

    public static QueryIsDriverUnreadMsgRspDTO success(String traceId, List<DriverUnreadMsgDTO> unreadMsgDTOS) {
        QueryIsDriverUnreadMsgRspDTO response = new QueryIsDriverUnreadMsgRspDTO();
        response.setTraceId(traceId);
        response.setUnreadMsgDTOS(unreadMsgDTOS);
        response.setSuccess(true);
        return response;
    }
}
