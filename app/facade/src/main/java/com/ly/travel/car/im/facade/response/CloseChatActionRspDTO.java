package com.ly.travel.car.im.facade.response;

import com.ly.sof.facade.base.BaseResponseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class CloseChatActionRspDTO extends BaseResponseDTO {

    private static final long serialVersionUID = 7935319022094575613L;

    public static CloseChatActionRspDTO fail(String traceId, String errorCode, String errorMessage) {
        CloseChatActionRspDTO response = new CloseChatActionRspDTO();
        response.setTraceId(traceId);
        response.setSuccess(false);
        response.setErrorCode(errorCode);
        response.setErrorMessage(errorMessage);
        return response;
    }

    public static CloseChatActionRspDTO success(String traceId) {
        CloseChatActionRspDTO response = new CloseChatActionRspDTO();
        response.setTraceId(traceId);
        response.setSuccess(true);
        return response;
    }
}
