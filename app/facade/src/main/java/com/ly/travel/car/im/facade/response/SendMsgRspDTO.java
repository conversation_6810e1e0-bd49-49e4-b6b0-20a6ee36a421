package com.ly.travel.car.im.facade.response;

import com.ly.sof.facade.base.BaseResponseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class SendMsgRspDTO extends BaseResponseDTO {

    private static final long serialVersionUID = -3787608581104022674L;

    /**
     * 发送消息id
     */
    private String msgId;

    public static SendMsgRspDTO fail(String traceId, String errorCode, String errorMessage, String msgId) {
        SendMsgRspDTO response = new SendMsgRspDTO();
        response.setTraceId(traceId);
        response.setSuccess(Boolean.FALSE);
        response.setErrorCode(errorCode);
        response.setErrorMessage(errorMessage);
        response.setMsgId(msgId);
        return response;
    }

    public static SendMsgRspDTO success(String traceId, String msgId) {
        SendMsgRspDTO response = new SendMsgRspDTO();
        response.setTraceId(traceId);
        response.setSuccess(Boolean.TRUE);
        response.setMsgId(msgId);
        return response;
    }
}
