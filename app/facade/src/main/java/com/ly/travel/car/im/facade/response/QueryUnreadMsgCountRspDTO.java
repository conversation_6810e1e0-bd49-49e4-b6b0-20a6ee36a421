package com.ly.travel.car.im.facade.response;

import com.ly.sof.facade.base.BaseResponseDTO;
import com.ly.travel.car.im.facade.dto.UnreadMsgDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class QueryUnreadMsgCountRspDTO extends BaseResponseDTO {

    private static final long serialVersionUID = -7286374128903152921L;

    private UnreadMsgDTO unreadMsgDTO;

    public static QueryUnreadMsgCountRspDTO fail(String traceId, String errorCode, String errorMessage) {
        QueryUnreadMsgCountRspDTO response = new QueryUnreadMsgCountRspDTO();
        response.setTraceId(traceId);
        response.setSuccess(false);
        response.setErrorCode(errorCode);
        response.setErrorMessage(errorMessage);
        return response;
    }

    public static QueryUnreadMsgCountRspDTO success(String traceId, UnreadMsgDTO unreadMsgDTO) {
        QueryUnreadMsgCountRspDTO response = new QueryUnreadMsgCountRspDTO();
        response.setTraceId(traceId);
        response.setUnreadMsgDTO(unreadMsgDTO);
        response.setSuccess(true);
        return response;
    }
}
