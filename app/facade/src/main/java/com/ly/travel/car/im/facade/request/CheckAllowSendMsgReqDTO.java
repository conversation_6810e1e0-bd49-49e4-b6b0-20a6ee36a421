package com.ly.travel.car.im.facade.request;

import com.ly.travel.car.im.facade.dto.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class CheckAllowSendMsgReqDTO extends BaseDTO {

    private static final long serialVersionUID = -8808811097954560698L;

    /**
     * imKey：哈啰的driverId，其他供应商没有driverId，如需接入IM，imKey使用车牌号plateNumber
     */
    private String imKey;

    /**
     * 供应商订单号
     */
    private String supplierOrderId;

    private String tcOrderId;
}
