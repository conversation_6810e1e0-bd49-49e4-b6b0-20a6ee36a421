package com.ly.travel.car.im.facade.response;

import com.ly.sof.facade.base.BaseResponseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class GetMsgRspDTO extends BaseResponseDTO {

    private static final long serialVersionUID = -8622997056746258237L;

    /**
     * 发送消息id
     */
    private String msgId;

    public static GetMsgRspDTO fail(String traceId, String errorCode, String errorMessage, String msgId) {
        GetMsgRspDTO response = new GetMsgRspDTO();
        response.setTraceId(traceId);
        response.setSuccess(Boolean.FALSE);
        response.setErrorCode(errorCode);
        response.setErrorMessage(errorMessage);
        response.setMsgId(msgId);
        return response;
    }

    public static GetMsgRspDTO success(String traceId, String msgId) {
        GetMsgRspDTO response = new GetMsgRspDTO();
        response.setTraceId(traceId);
        response.setSuccess(Boolean.TRUE);
        response.setMsgId(msgId);
        return response;
    }
}
