package com.ly.travel.car.im.facade.request;

import com.ly.travel.car.im.facade.dto.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class QueryMsgRecordReqDTO extends BaseDTO {

    private static final long serialVersionUID = -5135310749841809898L;

    /**
     * 会话标识（供应商Code/子供应商Code + 会员ID/unionId + 司机ID）
     */
    private String sessionKey;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 上一次消息记录ID
     */
    private Long lastMsgRecordId;

    /**
     * 查询方向 0-向下 1-向上
     */
    private Integer queryDirection;

    /**
     * 查询条数
     */
    private Integer queryNum;

    /**
     * 页码，从1开始
     */
    private Integer pageNum;

    /**
     * 每页记录数
     */
    private Integer pageSize;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 消息发送时间
     */
    private String msgSendTime;


    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 用户ID或手机号
     */
    private String userId;

    /**
     * 消息类型（TEXT: 文本, IMAGE: 图片, AUDIO: 语音, LOCATION: 坐标）
     */
    private String messageType;

    /**
     * 发送方（CUSTOMER_SERVICE: 客服, DRIVER: 司机）
     */
    private String sender;

    private Integer msgSendStatus;

    /**
     * 车牌号
     */
    private String plateNumber;
}
