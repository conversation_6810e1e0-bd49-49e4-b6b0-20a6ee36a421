package com.ly.travel.car.im.facade.response;

import com.ly.sof.facade.base.BaseResponseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class CheckAllowSendMsgRspDTO extends BaseResponseDTO {

    private static final long serialVersionUID = 1830001685224907274L;

    private Boolean allowSend;

    public static CheckAllowSendMsgRspDTO fail(String traceId, String errorCode, String errorMessage) {
        CheckAllowSendMsgRspDTO response = new CheckAllowSendMsgRspDTO();
        response.setTraceId(traceId);
        response.setSuccess(false);
        response.setErrorCode(errorCode);
        response.setErrorMessage(errorMessage);
        return response;
    }

    public static CheckAllowSendMsgRspDTO success(String traceId) {
        CheckAllowSendMsgRspDTO response = new CheckAllowSendMsgRspDTO();
        response.setTraceId(traceId);
        response.setSuccess(true);
        return response;
    }
}
