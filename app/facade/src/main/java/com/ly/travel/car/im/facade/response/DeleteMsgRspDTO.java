package com.ly.travel.car.im.facade.response;

import com.ly.sof.facade.base.BaseResponseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class DeleteMsgRspDTO extends BaseResponseDTO {

    private static final long serialVersionUID = -6322761570744357172L;

    /**
     * 会话表主键
     */
    private Long id;

    public static DeleteMsgRspDTO fail(String traceId, String errorCode, String errorMessage, Long id) {
        DeleteMsgRspDTO response = new DeleteMsgRspDTO();
        response.setTraceId(traceId);
        response.setSuccess(false);
        response.setErrorCode(errorCode);
        response.setErrorMessage(errorMessage);
        response.setId(id);
        return response;
    }

    public static DeleteMsgRspDTO success(String traceId, Long id) {
        DeleteMsgRspDTO response = new DeleteMsgRspDTO();
        response.setTraceId(traceId);
        response.setSuccess(true);
        response.setId(id);
        return response;
    }
}
