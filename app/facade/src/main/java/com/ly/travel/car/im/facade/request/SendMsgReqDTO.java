package com.ly.travel.car.im.facade.request;

import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.im.facade.dto.BaseDTO;
import com.ly.travel.car.im.facade.dto.ContentDTO;
import com.ly.travel.car.im.facade.dto.MsgContentDTO;
import com.ly.travel.car.im.facade.enums.MsgSenderEnum;
import com.ly.travel.car.im.facade.enums.MsgTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class SendMsgReqDTO extends BaseDTO {

    private static final long serialVersionUID = 4264947355379981480L;

    /**
     * imKey
     */
    private String imKey;

    /**
     * 供应商Code
     */
    private String supplierCode;

    /**
     * 司机昵称
     */
    private String driverNickName;

    /**
     * 司机ID
     */
    private String driverId;

    /**
     * 供应商订单号
     */
    private String supplierOrderId;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 司机车牌号
     */
    private String plateNumber;

    /**
     * 场景类型
     */
    private Integer sceneType;

    /**
     * 用户昵称
     */
    private String userNickName;

    /**
     * 用户头像
     */
    private String userAvatarUrl;

    /**
     * 消息体
     */
    private MsgContentDTO msgContent;

    /**
     * 已读页面来源
     */
    private String pageSource;

    /**
     * 已读页面来源refId
     */
    private String readSourceRefId;

    /**
     * 这个不用前端传
     */
    private Date msgSendTime;

    /**
     * 会话标识 前端不用传
     */
    private String sessionKey;
    private String readContent;

    private Integer platId;

    public String content() {
        ContentDTO contentDto = ContentDTO.builder()
                .ts(this.msgSendTime.getTime())
                .sendType(0)
                .fromNickname(getUserNickName())
                .fromAvatarUrl(getUserAvatarUrl())
                .ignore(MsgTypeEnum.READ_ACK.getMsgType().equals(getMsgType()))
                .driverNickname(getDriverNickName())
                .passengerOrderGuid(getSupplierOrderId())
                .driverOrderGuid("")
                .roleType(MsgSenderEnum.PASSENGER.getCode().toString())
                .build();
        contentDto.populateData(getMsgContent());
        return FastJsonUtils.toJSONString(contentDto);
    }

    public String getMsgId() {
        return msgContent.getMsgId();
    }

    public void setMsgId(String msgId) {
        msgContent.setMsgId(msgId);
    }

    public Integer getMsgType() {
        return msgContent.getMsgType();
    }
}
