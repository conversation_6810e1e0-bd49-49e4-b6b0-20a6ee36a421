package com.ly.travel.car.im.facade.response;

import com.ly.sof.facade.base.BaseResponseDTO;
import com.ly.travel.car.im.facade.dto.UnreadMsgDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class QueryUnreadMsgRspDTO extends BaseResponseDTO {

    private static final long serialVersionUID = -7286374128903152921L;

    private UnreadMsgDTO unreadMsgDTO;

    public static QueryUnreadMsgRspDTO fail(String traceId, String errorCode, String errorMessage) {
        QueryUnreadMsgRspDTO response = new QueryUnreadMsgRspDTO();
        response.setTraceId(traceId);
        response.setSuccess(false);
        response.setErrorCode(errorCode);
        response.setErrorMessage(errorMessage);
        return response;
    }

    public static QueryUnreadMsgRspDTO success(String traceId, UnreadMsgDTO unreadMsgDTO) {
        QueryUnreadMsgRspDTO response = new QueryUnreadMsgRspDTO();
        response.setTraceId(traceId);
        response.setUnreadMsgDTO(unreadMsgDTO);
        response.setSuccess(true);
        return response;
    }
}
