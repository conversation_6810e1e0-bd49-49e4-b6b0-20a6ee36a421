package com.ly.travel.car.im.facade.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class MemberInfoDTO implements Serializable {
    private static final long serialVersionUID = -3056074168735004L;
    private String unionId;
    private String openId;
    /**
     * 绑定的同程会员Id
     */
    private Long memberId;
    /**
     * 会员体系 0:同程 33:微信
     */
    private Integer memberSystem;
    /**
     * 绑定平台
     */
    private String bindPlat;
    /**
     * 绑定时间
     */
    private String bindDate;
}
