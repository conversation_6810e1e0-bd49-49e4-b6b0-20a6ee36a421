package com.ly.travel.car.im.facade;

import com.ly.travel.car.im.facade.request.CloseChatActionReqDTO;
import com.ly.travel.car.im.facade.request.GetMsgReqDTO;
import com.ly.travel.car.im.facade.request.OpenChatActionReqDTO;
import com.ly.travel.car.im.facade.response.CloseChatActionRspDTO;
import com.ly.travel.car.im.facade.response.GetMsgRspDTO;
import com.ly.travel.car.im.facade.response.OpenChatActionRspDTO;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;

@Path("supplierchat")
public interface SupplierChatFacade {

    /**
     * 获取司机消息
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("getMsg")
    GetMsgRspDTO getMsg(GetMsgReqDTO req);

    /**
     * 关闭会话  -- 约约特性
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("closeChatAction")
    CloseChatActionRspDTO closeChatAction(CloseChatActionReqDTO req);

    /**
     * 创建会话 -- 约约特性
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("openChatAction")
    OpenChatActionRspDTO openChatAction(OpenChatActionReqDTO req);
}
