package com.ly.travel.car.im.facade.response;

import com.ly.sof.facade.base.BaseResponseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class GetQuickMsgRspDTO extends BaseResponseDTO {

    private static final long serialVersionUID = -1095076310543583541L;

    private List<String> quickMsgList;

    public static GetQuickMsgRspDTO fail(String traceId, String errorCode, String errorMessage) {
        GetQuickMsgRspDTO response = new GetQuickMsgRspDTO();
        response.setTraceId(traceId);
        response.setSuccess(false);
        response.setErrorCode(errorCode);
        response.setErrorMessage(errorMessage);
        return response;
    }

    public static GetQuickMsgRspDTO success(String traceId, List<String> quickMsgList) {
        GetQuickMsgRspDTO response = new GetQuickMsgRspDTO();
        response.setTraceId(traceId);
        response.setQuickMsgList(quickMsgList);
        response.setSuccess(true);
        return response;
    }
}
