package com.ly.travel.car.im.facade.response;

import com.ly.sof.facade.base.BaseResponseDTO;
import com.ly.travel.car.im.facade.dto.OpenChatActionDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class OpenChatActionRspDTO extends BaseResponseDTO {

    private static final long serialVersionUID = 9191999420538973143L;

    private OpenChatActionDTO openChatActionDTO;

    public static OpenChatActionRspDTO fail(String traceId, String errorCode, String errorMessage) {
        OpenChatActionRspDTO response = new OpenChatActionRspDTO();
        response.setTraceId(traceId);
        response.setSuccess(false);
        response.setErrorCode(errorCode);
        response.setErrorMessage(errorMessage);
        return response;
    }

    public static OpenChatActionRspDTO success(String traceId, OpenChatActionDTO openChatActionDTO) {
        OpenChatActionRspDTO response = new OpenChatActionRspDTO();
        response.setTraceId(traceId);
        response.setOpenChatActionDTO(openChatActionDTO);
        response.setSuccess(true);
        return response;
    }
}
