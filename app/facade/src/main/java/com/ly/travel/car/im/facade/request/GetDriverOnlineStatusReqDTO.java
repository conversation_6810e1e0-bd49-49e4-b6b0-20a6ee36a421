package com.ly.travel.car.im.facade.request;

import com.ly.travel.car.im.facade.dto.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class GetDriverOnlineStatusReqDTO extends BaseDTO {

    private static final long serialVersionUID = 5630573787916942650L;

    /**
     * 供应商订单号
     */
    private String supplierOrderId;

    /**
     * 司机id
     */
    private String driverId;
}
