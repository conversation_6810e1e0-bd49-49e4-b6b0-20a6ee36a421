package com.ly.travel.car.im.facade.dto;

import java.io.Serializable;
import java.util.Map;

import com.ly.sof.utils.mapping.FastJsonUtils;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class ContentDTO implements Serializable {

    private static final long serialVersionUID = -442154308788152911L;

    private String data;

    /**
     * 发送消息时间戳，毫秒
     */
    private Long ts;

    /**
     * 0发送，1重发，填0就可以
     */
    private Integer sendType;

    private String fromNickname;

    /**
     * 渠道用户头像图片url
     */
    private String fromAvatarUrl;

    /**
     * 是否忽略该消息（端上不保存不展示），false就可以了
     */
    private Boolean ignore;

    /**
     * 司机id
     */
    private String fromUserGuid;

    /**
     * 司机昵称
     */
    private String driverNickname;

    /**
     * 供应商订单号
     */
    private String passengerOrderGuid;

    /**
     * 司机单号，渠道拿得到就传
     */
    private String driverOrderGuid;

    /**
     * 1：乘客  2：司机
     */
    private String roleType;

    /**
     * 消息id
     */
    private String msgId;

    public void populateData(MsgContentDTO msgContentDTO) {
        switch (msgContentDTO.getMsgType()) {
            case 0:
            case 1:
            case 14:
                TextDataDTO textDataDto = new TextDataDTO();
                textDataDto.setContent(msgContentDTO.getContent());
                setData(FastJsonUtils.toJSONString(textDataDto));
                break;
            case 2:
                LocDataDTO locDataDto = new LocDataDTO();
                locDataDto.setLat(msgContentDTO.getLat());
                locDataDto.setContent(msgContentDTO.getContent());
                locDataDto.setLon(msgContentDTO.getLon());
                locDataDto.setLongAddress(msgContentDTO.getLongAddress());
                locDataDto.setShortAddress(msgContentDTO.getShortAddress());
                setData(FastJsonUtils.toJSONString(locDataDto));
                break;
            case 3:
                VoiceDataDTO voiceDataDto = new VoiceDataDTO();
                voiceDataDto.setContent(msgContentDTO.getContent());
                voiceDataDto.setAudioURL(msgContentDTO.getUrl());
                voiceDataDto.setFileName(msgContentDTO.getPath());
                voiceDataDto.setBeganPlayTime(msgContentDTO.getBeganPlayTime());
                voiceDataDto.setAudioDuration(msgContentDTO.getAudioDuration());
                voiceDataDto.setHasPlay(msgContentDTO.getHasPlay());
                voiceDataDto.setConversationShowText(msgContentDTO.getShowText());
                setData(FastJsonUtils.toJSONString(voiceDataDto));
                break;
            case 4:
                ImageDataDTO imageDataDto = new ImageDataDTO();
                imageDataDto.setContent(msgContentDTO.getContent());
                imageDataDto.setImageUrl(msgContentDTO.getUrl());
                imageDataDto.setImagePath(msgContentDTO.getPath());
                imageDataDto.setConversationShowText(msgContentDTO.getShowText());
                setData(FastJsonUtils.toJSONString(imageDataDto));
                break;
            case 7:
                ReadReceiptDataDTO readReceiptDataDto = new ReadReceiptDataDTO();
                readReceiptDataDto.setContent(msgContentDTO.getContent());
                setData(FastJsonUtils.toJSONString(readReceiptDataDto));
                break;
            case 9:
                TimeCardDataDTO timeCardDataDto = new TimeCardDataDTO();
                timeCardDataDto.setContent(msgContentDTO.getContent());
                timeCardDataDto.setConversationShowText(msgContentDTO.getShowText());
                Map timeCardExtension = FastJsonUtils.fromJSONString(msgContentDTO.getExtension(), Map.class);
                timeCardDataDto.setArriveTime((String) timeCardExtension.get("arriveTime"));
                timeCardDataDto.setPaxOrderId((String) timeCardExtension.get("paxOrderId"));
                timeCardDataDto.setMsgStatus((Integer) timeCardExtension.get("msgStatus"));
                setData(FastJsonUtils.toJSONString(timeCardDataDto));
                break;
            case 10:
                NegotiateCardDataDTO negotiateCardDataDto = new NegotiateCardDataDTO();
                negotiateCardDataDto.setContent(msgContentDTO.getContent());
                negotiateCardDataDto.setConversationShowText(msgContentDTO.getShowText());
                Map negotiateCardExtension = FastJsonUtils.fromJSONString(msgContentDTO.getExtension(), Map.class);
                negotiateCardDataDto.setTip1Response((String) negotiateCardExtension.get("tip1Response"));
                negotiateCardDataDto.setTip1Type((Integer) negotiateCardExtension.get("tip1Type"));
                negotiateCardDataDto.setTip1((String) negotiateCardExtension.get("tip1"));
                negotiateCardDataDto.setTip2Response((String) negotiateCardExtension.get("tip2Response"));
                negotiateCardDataDto.setTip2Type((Integer) negotiateCardExtension.get("tip2Type"));
                negotiateCardDataDto.setTip2((String) negotiateCardExtension.get("tip2"));
                negotiateCardDataDto.setInvalidResponse((String) negotiateCardExtension.get("invalidResponse"));
                negotiateCardDataDto.setPaxOrderGuid((String) negotiateCardExtension.get("paxOrderGuid"));
                negotiateCardDataDto.setText((String) negotiateCardExtension.get("text"));
                negotiateCardDataDto.setStatus((Integer) negotiateCardExtension.get("status"));
                setData(FastJsonUtils.toJSONString(negotiateCardDataDto));
                break;
            case 11:
                OrderCardDataDTO orderCardDataDto = new OrderCardDataDTO();
                Map orderCardExtension = FastJsonUtils.fromJSONString(msgContentDTO.getExtension(), Map.class);
                orderCardDataDto.setCardTitle((String) orderCardExtension.get("cardTitle"));
                orderCardDataDto.setEndPlanStartTime((String) orderCardExtension.get("endPlanStartTime"));
                orderCardDataDto.setEndPosition((PositionDTO) orderCardExtension.get("endPosition"));
                orderCardDataDto.setPassengerGuid((String) orderCardExtension.get("passengerGuid"));
                orderCardDataDto.setPlanStartTime((String) orderCardExtension.get("planStartTime"));
                orderCardDataDto.setStartPlanStartTime((String) orderCardExtension.get("startPlanStartTime"));
                orderCardDataDto.setStartPosition((PositionDTO) orderCardExtension.get("startPosition"));
                setData(FastJsonUtils.toJSONString(orderCardDataDto));
                break;
        }
    }
}
