package com.ly.travel.car.im.facade.request;

import com.ly.sof.facade.base.BaseRequestDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class GetChatSwitchInfosReqDTO extends BaseRequestDTO {

    private static final long serialVersionUID = 7675288476664965022L;

    /**
     * 订单状态
     */
    private Integer orderState;


    /**
     * 司机code
     */
    private String driverCode;


    /**
     * 渠道id
     */
    private Integer plateId;

    /**
     * 供应商code
     */
    private String supplierCode;

    /**
     * 订单完成时间
     */
    private String gmtTripFinished;

    /**
     * im延迟时间
     */
    private Integer imFinishDelayHour;

    /**
     * 用户ID
     */
    private String unionId;

    /**
     * 会员ID
     */
    private String memberId;

    /**
     * 来源
     */
    private int source;


}
