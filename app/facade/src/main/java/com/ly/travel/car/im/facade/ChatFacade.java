package com.ly.travel.car.im.facade;

import com.ly.travel.car.im.facade.request.*;
import com.ly.travel.car.im.facade.response.*;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;

@Path("chat")
public interface ChatFacade {

    /**
     * 乘客消息发送司机
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("sendMsg")
    SendMsgRspDTO sendMsg(SendMsgReqDTO req);

    /**
     * 查询用户消息列表
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("queryMsgList")
    QueryMsgRspDTO queryMsgList(QueryMsgReqDTO req);

    /**
     * 查询用户聊天记录
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("queryMsgRecord")
    QueryMsgRecordRspDTO queryMsgRecord(QueryMsgRecordReqDTO req);

    /**
     * 获取消息快捷语
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("getQuickMsgList")
    GetQuickMsgRspDTO getQuickMsgList(GetQuickMsgReqDTO req);

    /**
     * 获取司机在离线状态
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("getDriverOnlineStatus")
    GetDriverOnlineStatusRspDTO getDriverOnlineStatus(GetDriverOnlineStatusReqDTO req);

    /**
     * 上报用户状态
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("uploadUserStatus")
    UploadUserStatusRspDTO uploadUserStatus(UploadUserStatusReqDTO req);

    /**
     * 能否发送消息检查
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("checkAllowSendMsg")
    CheckAllowSendMsgRspDTO checkAllowSendMsg(CheckAllowSendMsgReqDTO req);

    /**
     * 删除消息列表
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("deleteMsg")
    DeleteMsgRspDTO deleteMsg(DeleteMsgReqDTO req);

    /**
     * 查询未读消息列表&未读消息数量
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("queryUnreadMsg")
    QueryUnreadMsgRspDTO queryUnreadMsg(QueryUnreadMsgReqDTO req);

    /**
     * 查询未读消息消息数量
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("queryUnreadMsgCount")
    QueryUnreadMsgCountRspDTO queryUnreadMsgCount(QueryUnreadMsgCountReqDTO req);

    /**
     * 查询是否有司机未读消息
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("queryIsDriverUnreadMsg")
    QueryIsDriverUnreadMsgRspDTO queryIsDriverUnreadMsg(QueryIsDriverUnreadMsgReqDTO req);

    /**
     * 获取版本
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("getVersion")
    GetVersionRspDTO getVersion(GetVersionReqDTO req);

    /**
     * 获取getImKeyInfos
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("getImKeyInfos")
    GetImKeyInfosRspDTO getImKeyInfos(GetImKeyInfosReqDTO req);

    /**
     * 查询用户聊天记录
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("queryInnerMsgRecord")
    QueryMsgRecordRspDTO queryInnerMsgRecord(QueryMsgRecordReqDTO req);



    /**
     * 获取getImKeyInfos
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("getChatSwitchInfos")
    GetChatSwitchInfosRspDTO getChatSwitchInfos(GetChatSwitchInfosReqDTO req);
}
