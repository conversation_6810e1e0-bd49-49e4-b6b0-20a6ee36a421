package com.ly.travel.car.im.facade;

import com.ly.travel.car.im.facade.dto.BaseDTO;
import com.ly.travel.car.im.facade.request.GetMsgTemplateReqDTO;
import com.ly.travel.car.im.facade.response.CallBackRspDTO;
import com.ly.travel.car.im.facade.response.GetMsgTemplateRspDTO;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;

@Path("wxsub")
public interface WxSubFacade {

    /**
     * 获取订阅消息模版
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("getMsgTemplate")
    GetMsgTemplateRspDTO getMsgTemplate(GetMsgTemplateReqDTO req);

    /**
     * 订阅回调
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("callBack")
    CallBackRspDTO callBack(BaseDTO req);
}
