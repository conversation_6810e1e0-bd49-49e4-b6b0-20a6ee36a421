package com.ly.travel.car.im.facade.response;

import com.ly.sof.facade.base.BaseResponseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class CallBackRspDTO extends BaseResponseDTO {

    private static final long serialVersionUID = 9092727468922189642L;

    public static CallBackRspDTO fail(String traceId, String errorCode, String errorMessage) {
        CallBackRspDTO response = new CallBackRspDTO();
        response.setTraceId(traceId);
        response.setSuccess(false);
        response.setErrorCode(errorCode);
        response.setErrorMessage(errorMessage);
        return response;
    }

    public static CallBackRspDTO success(String traceId) {
        CallBackRspDTO response = new CallBackRspDTO();
        response.setTraceId(traceId);
        response.setSuccess(true);
        return response;
    }
}
