package com.ly.travel.car.im.facade.enums;

import lombok.Getter;

@Getter
public enum MsgTypeEnum {

    CUSTOM_TEXT(1, "自定义文本", "OtherMsgHandler"),

    QUICK_MSG(0, "快捷消息", "OtherMsgHandler"),

    LOC(2, "定位", "OtherMsgHandler"),

    VOICE(3, "语音", "OtherMsgHandler"),

    IMAGE(4, "图片", "OtherMsgHandler"),

    SYSTEM(6, "系统消息", "OtherMsgHandler"),

    TIP(7, "提示公告", "TipMsgHandler"),

    READ_ACK(8, "已读消息", "ReadMsgHandler"),

    TIME_CARD(9, "时间卡片", "OtherMsgHandler"),

    NEGOTIATE_CARD(10, "协商卡片", "OtherMsgHandler"),

    ORDER_CARD(11, "订单卡片", "OtherMsgHandler"),

    INVITE_DRIVER(12, "邀请车主", "InviteDriverMsgHandler"),

    INVITE_DRIVER_ACK(13, "邀请车主回复", ""),

    INVITE_DRIVER_TEXT(14, "邀请车主文本", "InviteDriverTextMsgHandler"),

    SFC_IM_REASSIGNMENT(15, "改派弹窗提示", "OtherMsgHandler"),

    SAFE_WARN_TEXT(16, "安全中心告警", "OtherMsgHandler"),

    NIGHT_TIP(17, "夜间提示", "TipMsgHandler"),

    NIGHT_WARN(18, "夜间出行提示", "OtherMsgHandler"),

    INVITE_DRIVER_SYS(19, "邀请车主系统提示消息", "InviteDriverSysMsgHandler"),

    OTHER(99, "其它类型", ""),

    DISTRIBUTE_CUSTOM_TEXT(20, "分销自定义文本", "DistributeOtherMsgHandler"),

    DISTRIBUTE_READ_ACK(21, "分销已读", "DistributeReadMsgHandler"),

    SWITCH_NOTIFICATION(101, "开关切换通知", "");

    private Integer msgType;

    private String desc;

    private String handlerName;

    MsgTypeEnum(Integer msgType, String desc, String handlerName) {
        this.msgType = msgType;
        this.desc = desc;
        this.handlerName = handlerName;
    }

    public static String getHandlerName(Integer msgType) {
        MsgTypeEnum msgTypeEnum = MsgTypeEnum.forValue(msgType);
        return msgTypeEnum.getHandlerName();
    }

    public static MsgTypeEnum forValue(int value) {
        for (MsgTypeEnum type : values()) {
            if (type.msgType == value) {
                return type;
            }
        }
        return null;
    }
}
