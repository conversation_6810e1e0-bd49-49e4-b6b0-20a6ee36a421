package com.ly.travel.car.im.facade.response;

import com.ly.sof.facade.base.BaseResponseDTO;
import com.ly.travel.car.im.facade.dto.MsgRecordDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class QueryMsgRecordRspDTO extends BaseResponseDTO {

    private static final long serialVersionUID = -8102124417599458701L;

    private List<MsgRecordDTO> data;
    
    /**
     * 是否还有更多消息
     */
    private Boolean hasMore;
    
    /**
     * 下一页查询的参考消息ID
     */
    private Long nextLastMsgRecordId;
    
    /**
     * 当前页码
     */
    private Integer currentPage;
    
    /**
     * 每页记录数
     */
    private Integer pageSize;
    
    /**
     * 总记录数
     */
    private Long total;

    /**
     * 上一次消息记录ID
     */
    private Long lastMsgRecordId;

    public static QueryMsgRecordRspDTO fail(String traceId, String errorCode, String errorMessage) {
        QueryMsgRecordRspDTO response = new QueryMsgRecordRspDTO();
        response.setTraceId(traceId);
        response.setSuccess(false);
        response.setErrorCode(errorCode);
        response.setErrorMessage(errorMessage);
        return response;
    }

    public static QueryMsgRecordRspDTO success(String traceId, List<MsgRecordDTO> data) {
        QueryMsgRecordRspDTO response = new QueryMsgRecordRspDTO();
        response.setTraceId(traceId);
        response.setData(data);
        response.setSuccess(true);
        response.setHasMore(false); // 默认没有更多数据
        return response;
    }

    public static QueryMsgRecordRspDTO success(String traceId, List<MsgRecordDTO> data,Long lastMsgRecordId) {
        QueryMsgRecordRspDTO response = new QueryMsgRecordRspDTO();
        response.setTraceId(traceId);
        response.setData(data);
        response.setSuccess(true);
        response.setHasMore(false);
        response.setLastMsgRecordId(lastMsgRecordId);
        return response;
    }
    
}
