package com.ly.travel.car.im.facade.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.ly.dal.util.DateUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class MsgDTO implements Serializable {

    private static final long serialVersionUID = -8265960995235804733L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DateUtil.DATE_PATTERN_YYYY_MM_DD_HH_MM_SS, timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = DateUtil.DATE_PATTERN_YYYY_MM_DD_HH_MM_SS, timezone = "GMT+8")
    private Date updateTime;

    /**
     * 会话标识（供应商Code/子供应商Code + 会员ID/unionId + 司机ID）
     */
    private String sessionKey;

    /**
     * 用户unionId
     */
    private String unionId;

    /**
     * 会员ID
     */
    private Long memberId;

    /**
     * 司机ID
     */
    private String driverId;

    /**
     * 司机车牌号
     */
    private String plateNumber;

    /**
     * 供应商Code
     */
    private String supplierCode;

    /**
     * 司机昵称
     */
    private String driverNickName;

    /**
     * 司机性别 0-未知 1-男 2-女
     */
    private Integer driverGender;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 供应商订单号
     */
    private String supplierOrderId;

    /**
     * 未读消息数量
     */
    private Integer unreadMsgNum;

    /**
     * 消息数量
     */
    private Long totalMsgNum;

    /**
     * 最新消息记录ID
     */
    private Long latestMsgRecordId;

    /**
     * 最新消息内容简介
     */
    private String latestMsgDesc;

    /**
     * 最新消息类型 0-快捷消息 1-自定义文本 2-定位 3-语音 4-图片 5-卡片消息 6-系统消息 7-提示公告
     */
    private Integer latestMsgType;

    /**
     * 最新消息时间
     */
    @JsonFormat(pattern = DateUtil.DATE_PATTERN_YYYY_MM_DD_HH_MM_SS, timezone = "GMT+8")
    private Date latestMsgTime;
}
