package com.ly.travel.car.im.facade.request;

import com.ly.sof.facade.base.BaseRequestDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class CloseChatActionReqDTO extends BaseRequestDTO {

    private static final long serialVersionUID = -4357568608975537350L;

    /**
     * 同程会话id
     */
    private String tcConversationId;

    /**
     * 司机id
     */
    private String driverId;
}
