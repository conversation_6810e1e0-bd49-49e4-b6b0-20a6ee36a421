package com.ly.travel.car.im.facade;

import com.ly.travel.car.im.facade.request.*;
import com.ly.travel.car.im.facade.response.*;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;

@Path("distributechat")
public interface DistributeChatFacade {

    /**
     * 分销乘客消息发送司机
     */
    @POST
    @Produces({"application/json; charset=UTF-8"})
    @Consumes({"application/json; charset=UTF-8"})
    @Path("sendMsg")
    SendMsgRspDTO sendMsg(DistributeSendMsgReqDTO req);


}
