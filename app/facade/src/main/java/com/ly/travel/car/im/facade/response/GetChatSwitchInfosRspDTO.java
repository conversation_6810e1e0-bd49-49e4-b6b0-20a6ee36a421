package com.ly.travel.car.im.facade.response;

import com.ly.sof.facade.base.BaseResponseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class GetChatSwitchInfosRspDTO extends BaseResponseDTO {

    private static final long serialVersionUID = -1095076310543583541L;


    private boolean chatSwitch;

    private boolean phoneSwitch;


    public static GetChatSwitchInfosRspDTO fail(String traceId, String errorCode, String errorMessage) {
        GetChatSwitchInfosRspDTO response = new GetChatSwitchInfosRspDTO();
        response.setTraceId(traceId);
        response.setSuccess(false);
        response.setErrorCode(errorCode);
        response.setErrorMessage(errorMessage);
        return response;
    }

    public static GetChatSwitchInfosRspDTO success(String traceId) {
        GetChatSwitchInfosRspDTO response = new GetChatSwitchInfosRspDTO();
        response.setTraceId(traceId);
        response.setSuccess(true);
        return response;
    }
}
