package com.ly.travel.car.im.facade.dto;

import com.ly.sof.utils.mapping.FastJsonUtils;
import com.ly.travel.car.im.facade.enums.MsgTypeEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Data
public class MsgContentDTO implements Serializable {

    private static final long serialVersionUID = 3925644045864180220L;

    /**
     * 消息GUID
     */
    private String msgId;

    /**
     * 消息类型：0-快捷消息 1-自定义文本 2-定位 3-语音 4-图片 5-卡片消息 6-系统消息 7-提示公告
     */
    private Integer msgType;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 提示文案（不支持时展示）
     */
    private String showText;

    /**
     * 扩展字段json格式（预留）
     */
    private String extension;

    /**
     * 提示
     */
    private String tips;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 经度
     */
    private String lon;

    /**
     * 长地址
     */
    private String longAddress;

    /**
     * 短地址
     */
    private String shortAddress;

    /**
     * 资源路径
     */
    private String url;

    /**
     * 文件名
     */
    private String path;

    /**
     * 开始播放时间
     */
    private Integer beganPlayTime;

    /**
     * 音频时长
     */
    private Integer audioDuration;

    private Boolean hasPlay;

    /**
     * 分销消息GUID
     */
    private String distributionMsgId;

    private static final Pattern TIMESTAMP_PATTERN = Pattern.compile("\\d{13}");

    public static MsgContentDTO build(Integer msgType, ContentDTO contentDTO) {
        MsgContentDTO msgContentDTO = buildMsgContentDto(contentDTO.getMsgId(), msgType, contentDTO.getData(),"");
        return msgContentDTO;
    }

    public static void build(MsgRecordDTO dto, ImMsgRecordDTO imMsgRecordDTO) {
        Integer msgType = imMsgRecordDTO.getMsgType();
        String msgId = imMsgRecordDTO.getMsgId();
        String msgContent = imMsgRecordDTO.getMsgContent();

        if (Objects.equals(MsgTypeEnum.SYSTEM.getMsgType(), msgType)
                || Objects.equals(MsgTypeEnum.SFC_IM_REASSIGNMENT.getMsgType(), msgType)
                || Objects.equals(MsgTypeEnum.TIP.getMsgType(), msgType)
                || Objects.equals(MsgTypeEnum.INVITE_DRIVER.getMsgType(), msgType)
                || Objects.equals(MsgTypeEnum.INVITE_DRIVER_ACK.getMsgType(), msgType)
                || Objects.equals(MsgTypeEnum.SAFE_WARN_TEXT.getMsgType(), msgType)
                || Objects.equals(MsgTypeEnum.NIGHT_WARN.getMsgType(), msgType)
                || Objects.equals(MsgTypeEnum.NIGHT_TIP.getMsgType(), msgType)
                || Objects.equals(MsgTypeEnum.INVITE_DRIVER_SYS.getMsgType(), msgType)
        ) {
            MsgContentDTO msgContentDTO = buildMsgContentDto(msgId, msgType, msgContent,"");
            dto.setMsgContent(msgContentDTO);
            return;
        }
        ContentDTO contentDTO = FastJsonUtils.fromJSONString(msgContent, ContentDTO.class);
        MsgContentDTO msgContentDTO = buildMsgContentDto(msgId, msgType, contentDTO.getData(),msgContent);
        dto.setMsgContent(msgContentDTO);
    }

    public static MsgContentDTO buildMsgContentDto(String msgId, Integer msgType, String msgContent,String contentDTO) {
        MsgContentDTO msgContentDTO = new MsgContentDTO();
        msgContentDTO.setMsgId(msgId);
        msgContentDTO.setMsgType(msgType);

        switch (msgType) {
            case 0:
            case 1:
            case 14:
            case 19:
                TextDataDTO textDataDTO = FastJsonUtils.fromJSONString(msgContent, TextDataDTO.class);
                if (Objects.nonNull(textDataDTO)) {
                    msgContentDTO.setContent(textDataDTO.getContent());
                } else {
                    LocDataDTO locDataDTO = FastJsonUtils.fromJSONString(contentDTO, LocDataDTO.class);
                    if (Objects.nonNull(locDataDTO)) {
                        msgContentDTO.setContent(locDataDTO.getContent());
                    }
                }
                break;
            case 2:
                LocDataDTO locDataDTO = FastJsonUtils.fromJSONString(msgContent, LocDataDTO.class);
                msgContentDTO.setContent(locDataDTO.getContent());
                msgContentDTO.setLat(locDataDTO.getLat());
                msgContentDTO.setLon(locDataDTO.getLon());
                msgContentDTO.setLongAddress(locDataDTO.getLongAddress());
                msgContentDTO.setShortAddress(locDataDTO.getShortAddress());
                break;
            case 3:
                VoiceDataDTO voiceDataDTO = FastJsonUtils.fromJSONString(msgContent, VoiceDataDTO.class);
                msgContentDTO.setContent(voiceDataDTO.getContent());
                msgContentDTO.setShowText(voiceDataDTO.getConversationShowText());
                msgContentDTO.setUrl(voiceDataDTO.getAudioURL());
                msgContentDTO.setPath(voiceDataDTO.getFileName());
                msgContentDTO.setBeganPlayTime(voiceDataDTO.getBeganPlayTime());
                msgContentDTO.setAudioDuration(voiceDataDTO.getAudioDuration());
                msgContentDTO.setHasPlay(voiceDataDTO.getHasPlay());
                break;
            case 4:
                ImageDataDTO imageDataDTO = FastJsonUtils.fromJSONString(msgContent, ImageDataDTO.class);
                msgContentDTO.setContent(imageDataDTO.getContent());
                msgContentDTO.setShowText(imageDataDTO.getConversationShowText());
                msgContentDTO.setUrl(imageDataDTO.getImageUrl());
                msgContentDTO.setPath(imageDataDTO.getImagePath());
                break;
            case 9:
                TimeCardDataDTO timeCardDataDTO = FastJsonUtils.fromJSONString(msgContent, TimeCardDataDTO.class);
                msgContentDTO.setContent(replaceTimestampsWithFormattedDates(timeCardDataDTO.getContent()));
                msgContentDTO.setShowText(timeCardDataDTO.getConversationShowText());
                msgContentDTO.setExtension(FastJsonUtils.toJSONString(timeCardDataDTO));
                break;
            case 10:
                NegotiateCardDataDTO negotiateCardDataDTO = FastJsonUtils.fromJSONString(msgContent, NegotiateCardDataDTO.class);
                msgContentDTO.setContent(negotiateCardDataDTO.getContent());
                msgContentDTO.setShowText(negotiateCardDataDTO.getConversationShowText());
                msgContentDTO.setExtension(FastJsonUtils.toJSONString(negotiateCardDataDTO));
                break;
            case 11:
                OrderCardDataDTO orderCardDataDTO = FastJsonUtils.fromJSONString(msgContent, OrderCardDataDTO.class);
                msgContentDTO.setExtension(FastJsonUtils.toJSONString(orderCardDataDTO));
                break;
            case 12:
            case 13:
                msgContentDTO = FastJsonUtils.fromJSONString(msgContent, MsgContentDTO.class);
                msgContentDTO.setMsgId(msgId);
                msgContentDTO.setMsgType(msgType);
                break;
            case 6:
            case 7:
            case 15:
            case 16:
            case 17:
            case 18:
                msgContentDTO.setContent(msgContent);
                break;
        }
        return msgContentDTO;
    }

    private static String replaceTimestampsWithFormattedDates(String input) {
        if (StringUtils.isBlank(input)) {
            return input;
        }
        Matcher matcher = TIMESTAMP_PATTERN.matcher(input);
        StringBuffer output = new StringBuffer();
        SimpleDateFormat formatter = new SimpleDateFormat("MM月dd日 HH:mm");

        while (matcher.find()) {
            long timestamp = Long.parseLong(matcher.group(0));
            String formattedDate = formatter.format(new Date(timestamp));
            matcher.appendReplacement(output, formattedDate);
        }
        matcher.appendTail(output);
        return output.toString();
    }
}
