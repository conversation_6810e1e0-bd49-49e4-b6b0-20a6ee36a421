package com.ly.travel.car.im.facade.response;

import com.ly.sof.facade.base.BaseResponseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class GetImKeyInfosRspDTO extends BaseResponseDTO {

    private static final long serialVersionUID = -1095076310543583541L;


    private String imSessionKey;

    private String imSessionKeyFinishTime;

    private Integer imFinishDelayHour;

    public static GetImKeyInfosRspDTO fail(String traceId, String errorCode, String errorMessage) {
        GetImKeyInfosRspDTO response = new GetImKeyInfosRspDTO();
        response.setTraceId(traceId);
        response.setSuccess(false);
        response.setErrorCode(errorCode);
        response.setErrorMessage(errorMessage);
        return response;
    }

    public static GetImKeyInfosRspDTO success(String traceId, String imSessionKey,int imFinishDelayHour,String imSessionKeyFinishTime) {
        GetImKeyInfosRspDTO response = new GetImKeyInfosRspDTO();
        response.setTraceId(traceId);
        response.setImSessionKey(imSessionKey);
        response.setImSessionKeyFinishTime(imSessionKeyFinishTime);
        response.setImFinishDelayHour(imFinishDelayHour);
        response.setSuccess(true);
        return response;
    }

    public static GetImKeyInfosRspDTO success(String traceId) {
        GetImKeyInfosRspDTO response = new GetImKeyInfosRspDTO();
        response.setTraceId(traceId);
        response.setSuccess(true);
        return response;
    }
}
