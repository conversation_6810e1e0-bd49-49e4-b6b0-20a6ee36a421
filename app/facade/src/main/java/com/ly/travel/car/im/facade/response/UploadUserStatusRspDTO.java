package com.ly.travel.car.im.facade.response;

import com.ly.sof.facade.base.BaseResponseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class UploadUserStatusRspDTO extends BaseResponseDTO {

    private static final long serialVersionUID = 902220560276063126L;

    public static UploadUserStatusRspDTO fail(String traceId, String errorCode, String errorMessage) {
        UploadUserStatusRspDTO response = new UploadUserStatusRspDTO();
        response.setTraceId(traceId);
        response.setSuccess(false);
        response.setErrorCode(errorCode);
        response.setErrorMessage(errorMessage);
        return response;
    }

    public static UploadUserStatusRspDTO success(String traceId) {
        UploadUserStatusRspDTO response = new UploadUserStatusRspDTO();
        response.setTraceId(traceId);
        response.setSuccess(true);
        return response;
    }
}
