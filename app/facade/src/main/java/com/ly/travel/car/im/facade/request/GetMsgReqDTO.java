package com.ly.travel.car.im.facade.request;

import com.ly.sof.facade.base.BaseRequestDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class GetMsgReqDTO extends BaseRequestDTO {

    private static final long serialVersionUID = -2031348991141269814L;

    /**
     * 司机id
     */
    private String driverId;

    /**
     * 发送内容
     */
    private String content;

    /**
     * 同程消息类型
     */
    private String tcMsgType;

    /**
     * 供应商消息类型
     */
    private String supplierMsgType;

    /**
     * 同程会话id --约约特性
     */
    private String tcConversationId;

    /**
     * 供应商code
     */
    private String supplierCode;
}
