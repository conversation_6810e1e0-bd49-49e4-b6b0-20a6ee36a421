package com.ly.travel.car.im.facade.response;

import com.ly.sof.facade.base.BaseResponseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class GetVersionRspDTO extends BaseResponseDTO {

    private static final long serialVersionUID = -3665146415251919176L;

    /**
     * 系统开关 v1:旧版本  v2:新版本
     */
    private String version;

    /**
     * 跳转链接
     */
    private String linkUrl;

    public static GetVersionRspDTO fail(String traceId, String errorCode, String errorMessage) {
        GetVersionRspDTO response = new GetVersionRspDTO();
        response.setTraceId(traceId);
        response.setSuccess(Boolean.FALSE);
        response.setErrorCode(errorCode);
        response.setErrorMessage(errorMessage);
        return response;
    }

    public static GetVersionRspDTO success(String traceId, String version, String linkUrl) {
        GetVersionRspDTO response = new GetVersionRspDTO();
        response.setTraceId(traceId);
        response.setVersion(version);
        response.setLinkUrl(linkUrl);
        response.setSuccess(Boolean.TRUE);
        return response;
    }
}
