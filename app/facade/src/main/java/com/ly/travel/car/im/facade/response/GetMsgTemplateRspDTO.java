package com.ly.travel.car.im.facade.response;

import com.ly.sof.facade.base.BaseResponseDTO;
import com.ly.travel.car.im.facade.dto.MsgTemplateDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class GetMsgTemplateRspDTO extends BaseResponseDTO {

    private static final long serialVersionUID = 5946731866433517311L;

    private MsgTemplateDTO msgTemplateDTO;

    public static GetMsgTemplateRspDTO fail(String traceId, String errorCode, String errorMessage) {
        GetMsgTemplateRspDTO response = new GetMsgTemplateRspDTO();
        response.setTraceId(traceId);
        response.setSuccess(false);
        response.setErrorCode(errorCode);
        response.setErrorMessage(errorMessage);
        return response;
    }

    public static GetMsgTemplateRspDTO success(String traceId, MsgTemplateDTO msgTemplateDTO) {
        GetMsgTemplateRspDTO response = new GetMsgTemplateRspDTO();
        response.setTraceId(traceId);
        response.setMsgTemplateDTO(msgTemplateDTO);
        response.setSuccess(true);
        return response;
    }
}
