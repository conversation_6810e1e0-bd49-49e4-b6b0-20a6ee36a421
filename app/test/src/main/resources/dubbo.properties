sof.version=1.0.0
app.name=ImChatService
app.version=1.0-SNAPSHOT
app.type=web

# database
uniform.env=test
sof-env=test

# dubbo
dubbo.application.name=car.shared.mobility.im.chat.service
dubbo.registry.address=tcdsf://testdsf.tcent.cn
dubbo.container=spring,log4j
dubbo.service.gsname=dsf.car.shared.mobility.im.chat.service
dubbo.service.port=11011
dubbo.service.registry.address=qa.dsf2.17usoft.com
dubbo.service.deploy.container=tomcat
dubbo.service.version=*******

# mq
mq.nameSrvAddress=**************\:9876;*************\:9876

# http client
http_read_timeout=5000
connect_timeout=5000

# redis
redis.groupNames=car.im.chat.service.group

# dsf
dsf.car.integration.gsName=dsf.car.shared.mobility.supply.integration
dsf.car.integration.version=*******
dsf.car.order.service.gsName=dsf.car.order.service
dsf.car.order.service.version=*******
dsf.car.order.core.gsName=dsf.car.shared.mobility.supply.order.core
dsf.car.order.core.version=*******