<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="off" monitorInterval="1800" packages="com.ly.travel.car.im.test">

    <Properties>
        <Property name="log.file.path">log/logs</Property>
        <Property name="log.gzip.path">log/logs/gzip</Property>
        <Property name="pattern">%d{yyyy-MM-dd HH:mm:ss.SSS} %-5p [%X{LOGGER_MODULE}][%X{LOGGER_CATEGORY}][%X{LOGGER_SUBCATEGORY}][%X{LOGGER_FILTER1}][%X{LOGGER_FILTER2}]%X{apmTrace} %m%n
        </Property>
    </Properties>

    <Appenders>
        <Console name="STDOUT-APPENDER" target="SYSTEM_OUT">
            <PatternLayout charset="UTF-8"
                           pattern="${pattern}"/>
        </Console>

        <Console name="STDERR-APPENDER" target="SYSTEM_ERR">
            <PatternLayout charset="UTF-8"
                           pattern="${pattern}"/>
        </Console>

        <RollingRandomAccessFile name="ERROR-APPENDER"
                                 fileName="${log.file.path}/common-error.log"
                                 filePattern="${log.gzip.path}/common-error/%d{yyyy-MM-dd}-%i.gz">
            <PatternLayout charset="UTF-8"
                           pattern="${pattern}"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="250 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
            <Filters>
                <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="DEFAULT-APPENDER"
                                 fileName="${log.file.path}/common-default.log"
                                 filePattern="${log.gzip.path}/common-default/%d{yyyy-MM-dd}-%i.gz">
            <PatternLayout charset="UTF-8" pattern="${pattern}"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="250 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="APP-DEFAULT-APPENDER"
                                 fileName="${log.file.path}/car-im-chat-service.log"
                                 filePattern="${log.gzip.path}/car-im-chat-service/%d{yyyy-MM-dd}-%i.gz">
            <PatternLayout charset="UTF-8" pattern="${pattern}"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="250 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
            <Filters>
                <!-- 排除掉ERROR -->
                <ThresholdFilter level="ERROR" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="SOF-DEFAULT-APPENDER"
                                 fileName="${log.file.path}/sof-default.log"
                                 filePattern="${log.gzip.path}/sof-default/%d{yyyy-MM-dd}-%i.gz">
            <PatternLayout charset="UTF-8" pattern="${pattern}"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="250 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="DUBBO-APPENDER"
                                 fileName="${log.file.path}/dubbo.log"
                                 filePattern="${log.gzip.path}/dubbo/%d{yyyy-MM-dd}-%i.gz">
            <PatternLayout charset="UTF-8" pattern="${pattern}"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="250 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
            <Filters>
                <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="JVM-MONITOR-DIGEST-APPENDER"
                                 fileName="${log.file.path}/jvm-monitor-digest.log"
                                 filePattern="${log.gzip.path}/jvm-monitor-digest/%d{yyyy-MM-dd}-%i.gz">
            <PatternLayout charset="UTF-8" pattern="${pattern}"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="250 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
            <Filters>
                <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </RollingRandomAccessFile>

    </Appenders>

    <Loggers>
        <logger name="STDOUT" additivity="false" level="INFO">
            <appender-ref ref="STDOUT-APPENDER"/>
        </logger>

        <logger name="STDERR" additivity="false" level="ERROR">
            <appender-ref ref="STDERR-APPENDER"/>
        </logger>
        <logger name="com.ly.sof" additivity="false" level="INFO">
            <appender-ref ref="SOF-DEFAULT-APPENDER"/>
            <appender-ref ref="ERROR-APPENDER"/>
        </logger>
        <logger name="org.springframework" additivity="false" level="WARN">
            <appender-ref ref="SOF-DEFAULT-APPENDER"/>
            <appender-ref ref="ERROR-APPENDER"/>
        </logger>
        <logger name="com.alibaba.dubbo" additivity="false" level="INFO">
            <appender-ref ref="DUBBO-APPENDER"/>
            <appender-ref ref="ERROR-APPENDER"/>
        </logger>
        <logger name="com.alibaba.dubbo.rpc.protocol.rest.support" additivity="false" level="WARN">
            <appender-ref ref="DUBBO-APPENDER"/>
            <appender-ref ref="ERROR-APPENDER"/>
        </logger>
        <logger name="java.sql" additivity="false" level="ERROR">
            <appender-ref ref="ERROR-APPENDER"/>
        </logger>
        <logger name="dbcfg" additivity="false" level="ERROR">
            <appender-ref ref="ERROR-APPENDER"/>
        </logger>
        <logger name="com.ibatis" additivity="false" level="WARN">
            <appender-ref ref="SOF-DEFAULT-APPENDER"/>
            <appender-ref ref="ERROR-APPENDER"/>
        </logger>
        <logger name="JVM-MONITOR-DIGEST-LOGGER" additivity="false" level="INFO">
            <appender-ref ref="JVM-MONITOR-DIGEST-APPENDER"/>
        </logger>
        <logger name="com.ly.travel.car.im.test" additivity="false" level="INFO" includeLocation="false">
            <appender-ref ref="APP-DEFAULT-APPENDER"/>
            <appender-ref ref="ERROR-APPENDER"/>
        </logger>
        <logger name="com.ly.dal" additivity="false" level="WARN">
            <appender-ref ref="DUBBO-APPENDER"/>
            <appender-ref ref="ERROR-APPENDER"/>
        </logger>
        <logger name="com.alibaba.dubbo.common.serialize.support.kryo.CompatibleKryo" additivity="false" level="ERROR">
            <appender-ref ref="DUBBO-APPENDER"/>
            <appender-ref ref="ERROR-APPENDER"/>
        </logger>
        <logger name="com.alibaba.dubbo.registry.tcdsf.ControlCenterClient" additivity="false" level="WARN">
            <appender-ref ref="DUBBO-APPENDER"/>
            <appender-ref ref="ERROR-APPENDER"/>
        </logger>
        <logger name="com.alibaba.dubbo.registry.tcdsf.TcDsfRegistry" additivity="false" level="WARN">
            <appender-ref ref="DUBBO-APPENDER"/>
            <appender-ref ref="ERROR-APPENDER"/>
        </logger>
        <logger name="com.ly.tcbase.core.log.ComponentLog" additivity="false" level="WARN">
            <appender-ref ref="DUBBO-APPENDER"/>
            <appender-ref ref="ERROR-APPENDER"/>
        </logger>
        <logger name="RocketmqClient" additivity="false" level="ERROR">
            <appender-ref ref="SOF-DEFAULT-APPENDER"/>
            <appender-ref ref="ERROR-APPENDER"/>
        </logger>

        <!-- sof日志 -->
        <logger name="com.ly.sof" level="INFO"/>
        <logger name="com.ly.sof.api.i18n" level="WARN"/>
        <logger name="com.ly.sof.api.mq.consumer.DefaultUniformEventSubscriber" level="WARN"/>
        <logger name="JVM-MONITOR-DIGEST-LOGGER" level="WARN"/>

        <!-- 组件日志 -->
        <logger name="com.ly.ie.sync.data.rate" level="WARN"/>
        <logger name="com.ly.forbidpool.client" level="WARN"/>
        <logger name="com.ly.tcbase.cacheclient.metric" level="ERROR"/>

        <!-- 框架日志 -->
        <logger name="com.ly.dsf" level="WARN"/>
        <logger name="com.ly.spat.dsf" level="WARN"/>
        <logger name="org.springframework" level="WARN"/>
        <logger name="com.ly.tcbase.core.log.ComponentLog" level="WARN"/>
        <logger name="com.alibaba.dubbo"/>
        <logger name="com.alibaba.dubbo.rpc.protocol.rest.support" level="WARN"/>
        <logger name="com.alibaba.dubbo.common.serialize.support.kryo.CompatibleKryo" level="ERROR"/>
        <logger name="com.alibaba.dubbo.registry.tcdsf.ControlCenterClient" level="WARN"/>
        <logger name="com.alibaba.dubbo.registry.tcdsf.TcDsfRegistry" level="WARN"/>
        <logger name="com.alibaba.dubbo.common.logger.LoggerFactory"/>
        <logger name="RocketmqClient" level="ERROR"/>
        <logger name="DefaultMQPushConsumerImpl" level="ERROR"/>
        <logger name="io.lettuce.core" level="ERROR"/>

        <root level="INFO" includeLocation="false">
            <appender-ref ref="APP-DEFAULT-APPENDER"/>
            <appender-ref ref="ERROR-APPENDER"/>
        </root>
    </Loggers>
</Configuration>