<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.ly.travel.car</groupId>
		<artifactId>shared-mobility-im-chat-service-parent</artifactId>
		<version>*******-RELEASE</version>
		<relativePath>../../pom.xml</relativePath>
	</parent>

	<artifactId>shared-mobility-im-chat-service-test</artifactId>
	<packaging>jar</packaging>
	<name>LY shared-mobility-im-chat-service-test</name>
	<description>LY shared-mobility-im-chat-service-test</description>

	<dependencies>
		<dependency>
			<groupId>com.ly.flight.toolkit</groupId>
			<artifactId>sof-dev-launcher</artifactId>
			<version>*******.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>com.ly.travel.car</groupId>
			<artifactId>shared-mobility-im-chat-service-biz</artifactId>
		</dependency>
		<dependency>
			<groupId>com.ly.travel.car</groupId>
			<artifactId>shared-mobility-im-chat-service-facade-impl</artifactId>
		</dependency>
		<dependency>
			<groupId>com.ly.travel.car</groupId>
			<artifactId>shared-mobility-im-chat-service-web</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-dbcp</groupId>
			<artifactId>commons-dbcp</artifactId>
		</dependency>
		<dependency>
			<groupId>com.ibatis</groupId>
			<artifactId>ibatis</artifactId>
		</dependency>
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
		</dependency>

		<!-- Test dependecies -->
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-all</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.easymock</groupId>
			<artifactId>easymock</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-module-junit4</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-api-easymock</artifactId>
			<scope>test</scope>
		</dependency>
	</dependencies>
</project>
