sof.version=${sof.version}
app.name=${app.name}
app.version=${app.version}
app.type=${app.type}

# database
uniform.env=${uniform.env}
sof-env=${sof-env}

# dubbo
dubbo.application.name=${dubbo.application.name}
dubbo.registry.address=${dubbo.registry.address}
dubbo.container=${dubbo.container}
dubbo.service.gsname=${dubbo.service.gsname}
dubbo.service.port=${dubbo.service.port}
dubbo.service.registry.address=${dubbo.service.registry.address}
dubbo.service.deploy.container=${dubbo.service.deploy.container}
dubbo.service.version=${dubbo.service.version}

# http client
http_read_timeout=${http_read_timeout}
connect_timeout=${connect_timeout}

# redis
redis.groupNames=${redis.groupNames}

# mq
mq.nameSrvAddress=${mq.nameSrvAddress}
chat.mq.topic=${chat.mq.topic}
chat.mq.consumer.group=${chat.mq.consumer.group}
chat.mq.producer.group=${chat.mq.producer.group}
chat.dispatch.mq.topic=${chat.dispatch.mq.topic}
chat.dispatch.mq.consumer.group=${chat.dispatch.mq.consumer.group}
chat.distribute.mq.topic=${chat.distribute.mq.topic}
chat.distribute.mq.consumer.group=${chat.distribute.mq.consumer.group}


# dsf
dsf.car.integration.gsName=${dsf.car.integration.gsName}
dsf.car.integration.version=${dsf.car.integration.version}
dsf.car.order.service.gsName=${dsf.car.order.service.gsName}
dsf.car.order.service.version=${dsf.car.order.service.version}
dsf.car.order.core.gsName=${dsf.car.order.core.gsName}
dsf.car.order.core.version=${dsf.car.order.core.version}
dsf.car.supply.trade.core.gsName=${dsf.car.supply.trade.core.gsName}
dsf.car.supply.trade.core.version=${dsf.car.supply.trade.core.version}

dsf.distribution.tcdsfGroup.gsName=${dsf.distribution.tcdsfGroup.gsName}
dsf.distribution.tcdsfGroup.version=${dsf.distribution.tcdsfGroup.version}

# sms
config.sms-link.wxLink=${config.sms-link.wxLink}
config.company-link.wxLink=${config.company-link.wxLink}
config.msg.send.account=${config.msg.send.account}
config.msg.send.password=${config.msg.send.password}

# wx
config.generate-link.wxUrlLink=${config.generate-link.wxUrlLink}
config.generate-link.wxAccount=${config.generate-link.wxAccount}
config.generate-link.wxSign=${config.generate-link.wxSign}
config.generate-link.shortUrl=${config.generate-link.shortUrl}

# push
config.push.appPushUrl=${config.push.appPushUrl}
config.push.appPushToken=${config.push.appPushToken}
config.push.appJumpUrl=${config.push.appJumpUrl}
config.push.appPushJumpUrl=${config.push.appPushJumpUrl}

# machine fire
config.url.bumblebee=${config.url.bumblebee}
config.url.station=${config.url.station}

# risk
config.risk.url=${config.risk.url}

# websocket
config.car.robin.url=${config.car.robin.url}

travel.mobility.supply.crm.core.dsf.version=${travel.mobility.supply.crm.core.dsf.version}
travel.mobility.supply.crm.client.mq.group=${travel.mobility.supply.crm.client.mq.group}
travel.mobility.supply.crm.client.modules = ${travel.mobility.supply.crm.client.modules}

