sof.version=1.0.0
app.name=ImChatService
app.version=1.0-SNAPSHOT
app.type=web

# database
uniform.env=test
sof-env=dev

# dubbo
dubbo.application.name=car.shared.mobility.im.chat.service
dubbo.registry.address=tcdsf://testdsf.tcent.cn
dubbo.container=spring,log4j
dubbo.service.gsname=dsf.car.shared.mobility.im.chat.service
dubbo.service.port=11011
dubbo.service.registry.address=qa.dsf2.17usoft.com
dubbo.service.deploy.container=tomcat
dubbo.service.version=*******
dsf.distribution.tcdsfGroup.gsName=dsf.car.shared.mobility.distribution.order
dsf.distribution.tcdsfGroup.version=********

# mq
mq.nameSrvAddress=**************\:9876;*************\:9876
chat.mq.topic=car_im_chat_message_topic_v2
chat.mq.consumer.group=car_im_chat_consumer_group_v2
chat.mq.producer.group=im_chat_producer_group_v2
chat.dispatch.mq.topic=car_im_chat_message_dispatch_topic
chat.dispatch.mq.consumer.group=car_im_chat_message_dispatch_group
chat.distribute.mq.topic=car_im_chat_message_distribute_topic
chat.distribute.mq.consumer.group=car_im_chat_message_distribute_group

# http client
http_read_timeout=5000
connect_timeout=5000

# redis
redis.groupNames=car.im.chat.service.group

# dsf
dsf.car.integration.gsName=dsf.car.shared.mobility.supply.integration
dsf.car.integration.version=*******
dsf.car.order.service.gsName=dsf.car.order.service
dsf.car.order.service.version=*******
dsf.car.order.core.gsName=dsf.car.shared.mobility.supply.order.core
dsf.car.order.core.version=*******

# sms
config.sms-link.wxLink=/page/home/<USER>/webview?timestamp=*************&needwrap=1&src=https%3A%2F%2Fwx.test.17u.cn%2FimCarfe%2Fwechat%2Flist%3Frefid%3D1916459367
config.company-link.wxLink=/page/home/<USER>/webview?timestamp=*************&needwrap=1&src=https%3A%2F%2Fwx.test.17u.cn%2FimCarfe%2Fwechat%2Flist%3Frefid%3D1916460301
config.msg.send.account=tcwireless.indiana.pubapi
config.msg.send.password=tcwireless.indiana.pubapi

# wx
config.generate-link.wxUrlLink=http://tourwirelessapi.17usoft.com/wechatpubinnerapi/wxUrlLink/generate
config.generate-link.wxAccount=A1018
config.generate-link.wxSign=f87d5cd478014b15b7fc6dda28932d21
config.generate-link.shortUrl=http://sapi.17usoft.com/tcsa-api/services/wsc/create

# push
config.push.appPushUrl=http://tccommon1.t.17usoft.com/pushtemplateservice
config.push.appPushToken=73fcb2f71e5e4ce781aa8481a7ec105a
config.push.appJumpUrl=tctclient://web/login?url=https%3A%2F%2Fwx.test.17u.cn%2FimCarfe%2Fapp%2Flist%3Frefid%3D1916460207&wvc5=1&tcwvckbc=1
config.push.appPushJumpUrl=tctclient://web/login?url=https%3A%2F%2Fwx.test.17u.cn%2FimCarfe%2Fapp%2Flist%3Frefid%3D2000061478&wvc5=1&tcwvckbc=1

# machine fire
config.url.bumblebee=http://tcwireless.t.17usoft.com/bumblebee
config.url.station=http://tourwirelessapi.t.17usoft.com/traffic/api/map/poi/info/

# risk
config.risk.url=http://tcwireless.17usoft.com/car_risk_process

# websocket
config.car.robin.url=https://livechat.qa.ly.com/carrobin


# crm core dsf 服务版本号
travel.mobility.supply.crm.core.dsf.version=1.0.0.0
# crm client 监听mq的消费组
travel.mobility.supply.crm.client.mq.group=shared_mobility_order_core_group_dev
# 需要使用的数据模块(不配置默认加载全部模块)，多个模块用英文逗号隔开,模块名可在com.ly.travel.shared.mobility.supply.crm.core.model.enums.CrmClientDataModuleEnum 中查询
travel.mobility.supply.crm.client.modules = supplierBaseConfig,supplierInterfaceCapability
