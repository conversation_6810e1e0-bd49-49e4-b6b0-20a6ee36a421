* {
    margin: 0;
    padding: 0;
}

body {
    background-color: #f2f2f2;
    font-size: 14px;
    color: #333333;
}

li {
    list-style: none;
}

a {
    text-decoration: none;
}

html, body, #app, .el-container, #asideNav, ul.el-menu {
    height: 100%;
}

.el-tabs__content {
    padding: 0 !important;
    height: calc(100% - 40px);
}

.el-tabs--border-card {
    border: 0;
}

.el-tab-pane, .el-tab-pane > iframe {
    height: 100% !important;
}

.left-panel {
    -webkit-box-shadow: 2px 0 6px rgba(0, 21, 41, .35);
    box-shadow: 2px 0 6px rgba(0, 21, 41, .35);
    z-index: 999;
}

.el-aside {
    transition: width 0.25s;
    -webkit-transition: width 0.25s;
    -moz-transition: width 0.25s;
    -webkit-transition: width 0.25s;
    -o-transition: width 0.25s;
}

