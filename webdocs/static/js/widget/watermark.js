export default class Watermark {
    constructor(options, customStyle) {
        const defaultOptions = { // 默认设置
            id: 'watermark',
            target: document.body,
            txt: '',
            xPoint: 20, // 水印起始位置x轴坐标
            yPoint: 20, // 水印起始位置Y轴坐标
            rows: 20, // 水印行数
            cols: 20, // 水印列数
            xSpace: 100, // 水印x轴间隔
            ySpace: 50, // 水印y轴间隔
            width: 210, // 水印宽度
            height: 80, // 水印长度
            angle: 30, // 倾斜角度
        }
        const defaultStyle = { // 默认样式
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            opacity: '0.5',
            fontSize: '14px',
            textAlign: 'center',
            overflow: 'hidden',
            whiteSpace: 'pre', // 识别换行符
        }
        this._options = Object.assign(defaultOptions, options)
        this._styleOptions = Object.assign(defaultStyle, customStyle)
    }

    append = () => {
        let {
            id,
            target,
            txt,
            xPoint,
            yPoint,
            rows,
            cols,
            xSpace,
            ySpace,
            width,
            height,
            angle,
        } = this._options

// 防止重复添加水印
        if (document.getElementById(id)) return

// const oTemp = document.createDocumentFragment()
        const oTemp = document.createElement('div')
        oTemp.id = id
// 这种写法是水印固定 不跟随页面滚动
        oTemp.style.cssText = `
position: fixed;
top: 0;
left: 0;
width: 100%;
height: 100%;
overflow: hidden;
pointer-events: none;
`
// const pWidth = Math.max(document.body.scrollWidth, document.body.clientWidth) // 获取页面最大宽度
// const pageHeight = Math.max(document.body.scrollHeight, document.body.clientHeight) + 450 // 获取页面最大高度
        const pWidth = window.screen.availWidth || Math.max(document.body.scrollWidth, document.body.clientWidth)
        const cutWidth = pWidth * 0.0150
        const pageWidth = pWidth - cutWidth
        const pageHeight = window.screen.availHeight || Math.max(document.body.scrollHeight, document.body.clientHeight)
// 如果将水印列数设置为0，或水印列数设置过大，超过页面最大宽度，则重新计算水印列数和水印x轴间隔
        if (cols === 0 || (parseInt(xPoint + width * cols + xSpace * (cols - 1) + '') > pageWidth)) {
            cols = parseInt((pageWidth - xPoint + xSpace) / (width + xSpace) + '')
            xSpace = parseInt((pageWidth - xPoint - width * cols) / (cols - 1) + '')
        }
// 如果将水印行数设置为0，或水印行数设置过大，超过页面最大长度，则重新计算水印行数和水印y轴间隔
        if (rows === 0 || (parseInt(yPoint + height * rows + ySpace * (rows - 1) + '') > pageHeight)) {
            rows = parseInt((ySpace + pageHeight - yPoint) / (height + ySpace) + '')
            ySpace = parseInt(((pageHeight - yPoint) - height * rows) / (rows - 1) + '')
        }
        let x
        let y
        for (let i = 0; i < rows; i++) {
            y = yPoint + (ySpace + height) * i
            for (let j = 0; j < cols; j++) {
                x = xPoint + (width + xSpace) * j
                const maskDiv = document.createElement('div')
                maskDiv.id = 'mask_div' + i + j
                maskDiv.className = 'mask_div'
                maskDiv.appendChild(document.createTextNode(txt))
// 设置水印div倾斜显示

                const baseStyle = { // 不可以被覆盖的样式
                    display: 'block',
                    transform: 'rotate(-' + angle + 'deg)',
                    visibility: '',
                    position: 'absolute',
                    left: x + 'px',
                    top: y + 'px',
                    width: width + 'px',
                    height: height + 'px',
                    pointerEvents: 'none', // pointer-events:none 让水印不遮挡页面的点击事件
                }

                const styleOptions = {
                    ...this._styleOptions,
                    ...baseStyle,
                }

                for (const key in styleOptions) {
                    maskDiv.style[key] = styleOptions[key]
                }
                oTemp.appendChild(maskDiv)
            }
        }
        target.appendChild(oTemp)
    }

    remove = () => {
        const {id, target} = this._options
        if (!document.getElementById(id)) return
        target.removeChild(document.getElementById(id))
    }
}