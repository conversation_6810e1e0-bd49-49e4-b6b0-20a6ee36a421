/*
 * LY.com Inc.
 * Copyright (c) 2004-2016 All Rights Reserved.
 */

/**
 * Vuejs 封装的select和duallistbox控件.默认使用bootstrap风格.
 * 依赖jquery, select2, duallistbox.
 * select2的参数配置参见: https://select2.github.io/options.html
 * duallistbox的参数配置,参见: https://github.com/istvan-ujjmeszaros/bootstrap-duallistbox
 *
 * @param id {String} 控件的id.
 * @param name {String} 控件的name.
 * @param disabled {Boolean} 是否禁用.
 * @param class {String}
 *    样式字符串,默认使用bootstrap, select2自带的有default, classic样式.
 * @param model {Object}
 *    绑定值, 默认是单向绑定,如果需要同步更新其他绑定该model的控件,需要在component上设置绑定类型sync, 例如 :model.sync="model".
 * @param transfer {Boolean} 是否为transfer控件.
 * @param options {Array}
 *    可选参数, 可以通过select的自己的data参数进行设置.
 *    select选项的数组, 格式如下:"[{text: 'name1', value: 'val1'}, {text: 'name2', value: 'val2'}]".
 * @param configs
 *    select2的选项, 如果options参数没有设置,那必须设置configs的data参数, 默认会进行绑定数据,目前还没有测试url方式.
 * @param multiple 是否为多选.
 *
 * @param size 控件显示数量
 *
 * <AUTHOR> 2016-04-23
 */
Vue.component('vue-select', {
    template: '<el-row><div :style="selectStyle()"><el-select style="display:block;" v-model.sync="model" clearable filterable :placeholder="tip" :size="size" :multiple="multiple" :name="name" :id="id" :disabled="disabled"><el-option v-for="item in options" :key="item.value" :label="item.text" :value="item.value"></el-option></el-select></div><div style="width: 80px;display: inline-block" v-show="refresh"><el-button :size="size" style="padding: 9px 15px !important; margin-left: 5px !important;" :icon="refreshIcon" @click="refreshSelect" type="primary" plain>刷新</el-button></div></el-row>',
    props: {
        id: {
            type: String,
            required: false,
            default: "id"
        },
        name: {
            type: String,
            required: false,
            default: "name"
        },
        disabled: {
            type: Boolean,
            required: false,
            default: false
        },
        model: {
            required: true
        },
        options: {
            type: Array,
            required: false
        },
        multiple: {
            type: Boolean,
            required: false,
            default: false
        },
        tip: {
            type: String,
            required: false,
            default: "--请选择--"
        },
        size: {
            type: String,
            required: false,
            default: "small"
        },
        refresh: false,
    },
    data: function () {
        return {
            refreshIcon: "el-icon-refresh"
        }
    },
    watch: {
        "model": function (val) {
            this.$emit('update:model', val)
        },

    },
    methods: {
        selectStyle: function () {
            if (this.refresh) {
                return "width: calc(100% - 80px);display: inline-block";
            } else {
                return "width: 100%";
            }
        },
        refreshSelect: function () {
            let self = this;
            this.refreshIcon = "el-icon-loading";
            setTimeout(function () {
                self.$emit("on-refresh");
                self.refreshIcon = "el-icon-refresh";
            }, 500);
        }
    }
});