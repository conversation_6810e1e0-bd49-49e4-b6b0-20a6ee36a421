<template>
  <el-row style="text-align: center;padding: 100px 0 ; color:  #DCDFE6">
    <h3><i class="el-icon-warning-outline"></i>{{message}}</h3>
  </el-row>
</template>

<script>
module.exports = {
  name: "empty",
  props: [
    'msg'
  ],
  components: {},
  data: function () {
    return {
      message: this.msg
    }
  },
  mounted: function () {
    if (this.message === undefined) {
      this.message = "没有任何结果，请选择条件搜索"
    }
  },
  methods: {}
}
</script>
