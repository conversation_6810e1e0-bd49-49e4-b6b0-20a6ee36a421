Vue.component('vue-page', {
    props: ["paginator", "query"],
    template: '<el-row style="text-align: right;padding: 10px">\n' +
        '            <el-pagination\n' +
        '            @size-change="handleSizeChange"\n' +
        '            @current-change="handleCurrentChange"\n' +
        '            :current-page="currentPage"\n' +
        '            :page-sizes="[10, 20, 50]"\n' +
        '            :page-size="10"\n' +
        '            layout="total, sizes, prev, pager, next, jumper"\n' +
        '            background \n' +
        '            :total="totalCount">\n' +
        '    </el-pagination>' +
        '</el-row>',
    computed: {
        currentPage: function () {
            if (!this.paginator) {
                return 0;
            }
            return this.paginator.currentPage;
        },
        totalCount: function () {
            if (!this.paginator) {
                return 0;
            }
            return this.paginator.totalCount;
        }
    },
    methods: {
        //切换每页条数
        handleSizeChange(val) {
            this.paginator.pageSize = val;
            this.query();
        },

        //切换页数
        handleCurrentChange(val) {
            this.paginator.currentPage = val;
            this.query();
        }
    }
});