<template>
  <div :id="'editor'+id">
  </div>
</template>
<script>
module.exports = {
  props: {
    content: {
      type: String
    },
    id: {
      type: String
    }
  },
  data: function () {
    return {
      editor: null
    }
  },
  mounted: function () {
    this.init();
  },
  // watch: {
  //   content(val) {
  //     this.editor.txt.append(val);
  //   }
  // },
  methods: {
    init: function () {
      const E = window.wangEditor;
      this.editor = new E(document.getElementById(`editor${this.id}`));
      this.editor.config.height = 300;
      this.editor.config.placeholder = '输入你的内容';
      this.editor.config.zIndex = 1;
      this.editor.config.menus = [
        'head',  // 标题
        'bold',  // 粗体
        'fontSize',  // 字号
        'fontName',  // 字体
        'italic',  // 斜体
        'underline',  // 下划线
        'strikeThrough',  // 删除线
        'foreColor',  // 文字颜色
        'backColor',  // 背景颜色
        // 'link',  // 插入链接
        'list',  // 列表
        'justify',  // 对齐方式
        'quote',  // 引用
        'emoticon',  // 表情
        // 'image',  // 插入图片
        'table',  // 表格
        'code',  // 插入代码
        'undo',  // 撤销
        'redo'  // 重复
      ];
      this.editor.create();
      this.editor.txt.append(this.content);
    },
    save() {
      // 返回带有HTML样式的文件
      return this.editor.txt.html();
    }
  }
}
</script>

<style scoped>

</style>