new Vue({
    el: "#app",
    components: {
        "layout-aside": httpVueLoader(__ctx + '/js/layout/menu-aside.vue'),
        "layout-header": httpVueLoader(__ctx + '/js/layout/menu-header.vue'),
        "layout-tab": httpVueLoader(__ctx + '/js/layout/menu-tab.vue'),
    },
    data: function () {
        return {
            collapse: false,
            /**
             * tips菜单页面 当前使用/config/config-tab.json
             * 后续接入权限后可以从同一权限接口中载入
             */
            menuData: [],
            /**
             * 允许多开的页面
             * menuData>>item>>code
             */
            multTabs: ["home-2"]
        }
    },
    mounted() {},
    updated() {
        if (this.$refs.tab != null && this.menuData.length < 1) {
            this.initTabMenu();
        }
    },
    methods: {
        /**
         * 载入默认菜单
         */
        initTabMenu: function () {
            axios({
                method: 'get',
                url: __ctx + '/js/config/config-tab.json',
                responseType: 'json'
            }).then(response => {
                this.menuData = response.data;
                this.$refs.tab.addTab(this.menuData[0], "");
            });
        },
        /**
         * 新增一个tab
         * @param code
         */
        addTab(code) {
            let item = this.findItem(code, this.menuData);
            this.$refs.tab.addTab(item, "");
        },
        /**
         * 切换菜单左侧
         */
        collapseEvent() {
            this.collapse = !this.collapse;
        },
        /**
         * 查找tab元素
         * @param code
         * @param menus
         * @returns {*|null}
         */
        findItem(code, menus) {
            for (let i in menus) {
                if (menus[i].code == code) {
                    return menus[i];
                }
                if (menus[i].children != null) {
                    let item = this.findItem(code, menus[i].children);
                    if (item != null) {
                        return item;
                    }
                }
            }
            return null;
        }
    }
})
