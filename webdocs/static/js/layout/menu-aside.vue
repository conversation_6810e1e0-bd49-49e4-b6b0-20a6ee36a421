<template>
  <el-row id="asideNav">
    <el-row class="logo-name">
      <p v-if="collapse" class="logo-title">MNG</p>
      <p v-else class="logo-title">SOF管理后台</p>
    </el-row>
    <el-menu class="el-menu-vertical"
             :default-active="active"
             @select="select"
             background-color="#03152A"
             :unique-opened="true"
             text-color="#fff"
             size="mini"
             :collapse-transition="true"
             active-text-color="#ffd04b" :collapse="collapse">
      <template v-for="(item,index) in menuData" v-if="!item.hidden">
        <el-submenu v-if="item.children.length>0" :index="index+''" :key="index">
          <template slot="title">
            <i :class="item.icon"></i>
            <span slot="title">{{ item.title }}</span>
          </template>
          <menu-tree :menu-data="item.children"></menu-tree>
        </el-submenu>
        <el-menu-item v-else :index="item.code" :key="item.code">
          <i :class="item.icon"></i>
          <span slot="title">{{ item.title }}</span>
        </el-menu-item>
      </template>

    </el-menu>
  </el-row>
</template>

<script>
module.exports = {
  props: [
    "collapse",
    "active",
    "menuData"
  ],
  components: {
    "menu-tree": httpVueLoader(__ctx + '/js/layout/menu-tree.vue'),
  },
  model: {
    event: 'change'
  },
  data: function () {
    return {
      logoPath: __ctx + "/img/logo_1.png",
      collapseLogPath: __ctx + "/img/logo_1.png",
    }
  },
  methods: {
    select: function (item, event) {
      this.$emit('change', item);
    },
  }
}
</script>

<style>
#asideNav {
  width: auto !important;
  height: 100%;
  display: flex;
  flex-direction: column;
}

#asideNav .logo-name {
  width: 100%;
}

#asideNav .el-menu-vertical:not(.el-menu--collapse) {
  height: 100%;
}


#asideNav .logo-name {
  background-color: #03152A !important;
  font-weight: 300;
  z-index: 999;
}

#asideNav .logo-name .logo-img {
  text-align: center;
  padding: 10px;
}

#asideNav .logo-name .logo-title {
  height: 50px;
  line-height: 50px;
  text-align: center;
  font-size: 20px;
  color: #ffffff;
}

#asideNav .el-menu-vertical:not(.el-menu--collapse) {
  width: 250px;
  overflow-y: scroll;
  overflow-x: hidden;
}


#asideNav .el-menu {
  flex: 1;
  overflow: inherit;
  border-right: none;
}

#asideNav .el-menu::-webkit-scrollbar {
  display: none;
}

#asideNav .el-menu .fa {
  vertical-align: middle;
  margin-right: 5px;
  width: 24px;
  text-align: center;
  font-size: 18px;
}

#asideNav .el-menu .el-menu-item {
  border-bottom: 1px solid #020f1d;
}

#asideNav .el-menu .el-menu-item:hover {
  color: rgb(255, 208, 75);
  background-color: rgb(67, 74, 80) !important;
}

#asideNav .el-menu .el-menu-item.is-active {
  color: rgb(255, 208, 75);
  border-bottom-color: rgb(255, 208, 75);
}

#asideNav .el-menu .is-opened > .el-submenu__title > .el-icon-arrow-down {
  color: #ffffff;
  font-weight: 500;
  font-size: 18px;
}
</style>
