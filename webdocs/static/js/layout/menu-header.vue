<template>
  <div>
    <el-header id="header">
      <span class="hideAside" @click="collapse"><i class="el-icon-s-fold"></i></span>
      <ul class="personal">
        <li>
          <el-dropdown @command="handleCommand">
                  <span class="el-dropdown-link">
                   {{userName}}<i class="el-icon-arrow-down el-icon--right"></i>
                  </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="info">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </li>
        <li class="icon">
          <el-avatar :src="avatar"/>
        </li>
      </ul>
    </el-header>
  </div>
</template>

<script>
module.exports = {
  props: [],
  data() {
    return {
      avatar: __ctx + "/img/logo_1.png"
    };
  },
  components: {},
  methods: {
    handleCommand(command) {
      if (command === "logout") {
        window.location.href = __ctx + "/j_spring_cas_security_logout";
      }
    },
    collapse() {
      this.$emit("collapse", null);
    }
  }
}
</script>

<style>
#header .personal .el-dropdown-link, #header .personal .fullScreen, #header .hideAside {
  cursor: pointer;
}

.hideAside {
  font-size: 25px;
}

#header {
  width: 100%;
  max-height: 50px;
  line-height: 50px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  background-color: #ffffff;
}

#header .personal {
  display: flex;
  flex-direction: row;
}

#header .personal li {
  margin-left: 13px;
  margin-right: 13px;
  font-size: 12px;
}

#header .el-avatar {
  margin-top: 7px;
}
</style>