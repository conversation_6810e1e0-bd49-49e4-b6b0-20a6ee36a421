<template>
  <div id="tabMenu">
    <template>
      <el-row class="tab-header-t">
        <transition-group name="list" tag="ul">
          <li v-for="item in tabs"
              :class="'tab-header-button tabnav '+(active==item.index?'active':'')"
              v-bind:key="item.index"
          >
            <el-link  :icon="item.icon" @click="active = item.index">{{ item.label }}</el-link>
            <span style="width: 2px"></span>
            <i class="el-icon-refresh" @click="refreshTab(item)" v-if="item.index!=defaultPage"></i>
            <span style="width: 2px"></span>
            <i class="el-icon-close" @click="handleTabsEdit(item.index,'remove')" v-if="item.index!=defaultPage"></i>
          </li>
        </transition-group>
      </el-row>
    </template>
    <el-row class="tab-header-content">
      <div v-for="item in tabs" v-show="active==item.index" style="height: 100%">
        <iframe :src="item.url" :id="item.iframeId" style="width: 100%;height: calc(100% - 4px);border: 0">
        </iframe>
      </div>
    </el-row>
  </div>

</template>
<script>
module.exports = {
  props: ["multTabs"],
  components: {},
  data: function () {
    return {
      // 打开的tab
      tabs: [],
      // 默认页面code
      defaultPage: "default-page",
      active: "default-page",
      // 多开页 页码
      pageIndex: 0,
    }
  },
  mounted: function () {
  },
  methods: {
    /**
     * 刷新页面
     * @param item
     */
    refreshTab: function (item) {
      if (item.iframeId === undefined) {
        return;
      }
      let frame = document.getElementById(item.iframeId)
      if (frame === undefined) {
        return;
      }
      frame.contentWindow.location.reload();
    },

    /**
     * 新增一个tab
     */
    addTab(tab, queryParams) {
      let targetPage = {
        index: tab.code,
        url: __ctx + tab.path,
        icon: tab.icon,
        label: tab.title
      };
      if (queryParams != null && queryParams.length > 0) {
        targetPage.url += `?${queryParams}`;
      }
      // 当前的tab是否允许多开
      if (this.multTabs.filter(tab => tab == targetPage.index) < 1) {
        if (this.tabs.filter(tab => tab.index == targetPage.index).length > 0) {
          this.active = targetPage.index;
          return;
        }
      } else {
        // tab允许多开
        targetPage.index = `${targetPage.index}-${this.pageIndex}`;
      }
      targetPage.iframeId = this.guid();
      this.active = targetPage.index;
      this.tabs.push(targetPage);
      this.pageIndex = this.pageIndex + 1;
    },

    /** 动态生成一个guid作为frame的id,这样可以保证允许多开的页面刷新时不会相互影响 */
    guid: function () {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = Math.random() * 16 | 0,
            v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      });
    },
    /** 处理tabs编辑 */
    handleTabsEdit(targetName, action) {
      if (action === 'remove') {
        if (targetName == this.defaultPage) {
          return;
        }
        this.tabs = this.tabs.filter(tab => tab.index !== targetName);
        this.active = this.tabs[this.tabs.length - 1].index;
      }
    },
  }
}
</script>
<style>
.tabnav {
  display: inline-block;
  transition: all 0.5s;
}

.list-enter, .list-leave-to {
  opacity: 0;
  transform: translateY(30px);

}

.list-enter-active {
  transition: all 0.5s;
}

.list-leave-active {
  position: absolute;
  transition: all 1s;
}

.tab-header-t {
  width: 100%;
}

.tab-header-t ul li a {
  height: 100%;
}

#tabMenu {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tab-header-t {
  height: 42px;
  min-height: 42px;
  overflow: hidden;
  border-top: 1px solid #f6f6f6;
  border-bottom: 1px solid #d8dce5;
  -webkit-box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.04);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.04);
}

.tab-header-t ul {
  display: flex;
  justify-content: flex-start;
  padding-left: 20px;
  flex-wrap: nowrap;
  overflow-x: auto;
}

.tab-header-t ul li {
  padding: 0 5px;
  height: 30px;
  line-height: 31px;
  display: flex;
  align-items: center;
  margin-top: 6px;
  margin-right: 5px;
  overflow: hidden;
  text-align: center;

  font-size: 14px;
  font-weight: 500;
}

.tab-header-t ul li:not(:first-child) {
  min-width: 80px;
}

.tab-header-t ul li a {
  padding-left: 2px;
  padding-right: 2px;
  display: inline-block;
  color: #303133;
}

.tab-header-t ul li:nth-child(n+2) a {
}

.tab-header-t ul li i:hover {
  color: #409eff !important;
  border: 0 !important;
}

.tab-header-t ul li a:hover {
  color: #409eff !important;
  border: 0 !important;
}
.tab-header-t ul li a:hover:after {
  border: 0 !important;
}

.tab-header-t ul li.active {
  color: #409eff;
  border-bottom: 1px solid #409eff;
}

.tab-header-t ul li.active a {
  color: #409eff;
}

.tab-header-content {
  height: 100%;
}
</style>