<template>
  <div>
    <template v-for="(child) in menuData">
      <el-submenu v-if="child.children.length > 0" :index="child.code" :key="child.code">
        <template slot="title">
          <i :class="child.icon"></i>
          <span slot="title">{{ child.title }}</span>
        </template>
        <menu-tree :menu-data="child.children"/>
      </el-submenu>

      <el-menu-item v-else :index="child.code" :key="child.code">
        <i :class="child.icon"></i>
        <span slot="title">{{ child.title }}</span>
      </el-menu-item>
    </template>
  </div>
</template>

<script>
module.exports = {
  name: "menu-tree",
  props: ["menuData"]
}
</script>

<style scoped>

</style>
