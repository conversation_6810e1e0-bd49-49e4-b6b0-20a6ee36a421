/**
 * 判断数组为空
 * @param arr
 * @returns {boolean}
 */
var arrEmpty = function (arr) {
    if (arr === null) {
        return true;
    }
    if (arr === undefined) {
        return true;
    }
    if (arr.length < 1) {
        return true;
    }
    if (arr === '') {
        return true;
    }

    if (arr === 'undefined') {
        return true;
    }
    return false;
}

/**
 * 数组不为空
 * @param arr
 * @returns {boolean}
 */
var arrNotEmpty = function (arr) {
    return !arrEmpty(arr);
}

/**
 * 对象为空
 * @param obj
 * @returns {boolean}
 */
var objEmpty = function (obj) {
    if (obj === null) {
        return true;
    }
    if (obj === undefined) {
        return true;
    }
    if (obj === '') {
        return true;
    }
    if (obj === 'undefined') {
        return true;
    }
    return false;
}

/**
 * 对象不为空
 * @param obj
 * @returns {boolean}
 */
var objNotEmpty = function (obj) {
    return !objEmpty(obj);
}

/**
 * 获取隐藏域值 type = hidden 判断是否默认值、为空，不为空则调用callback回调。
 * @param id
 * @param callback
 */
function setHiddenValue(id, callback) {
    let val = $(id).val();
    if (val == null || val.length < 1 || val === `\$\{${id.substr(1)}\}`) {
        return;
    }
    callback(val);
}
