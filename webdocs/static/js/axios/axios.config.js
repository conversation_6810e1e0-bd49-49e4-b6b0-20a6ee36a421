let instance = axios.create({
    baseURL: __ctx
});

instance.defaults.headers.post['Content-Type'] = 'application/json';
instance.defaults.headers.put['Content-Type'] = 'application/json';
/**
 * 设置请求头
 */
instance.interceptors.request.use(config => {
        config.loading = Vue.prototype.$loading({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
        });

        // 设置公用请求头
        for (let key in csrfHeader) {
            if (key.startsWith("$")) {
                continue;
            }
            config.headers.common[key] = csrfHeader[key];
        }
        config.headers.common['X-Requested-With'] = 'XMLHttpRequest';
        return config;
    },
    error => Promise.reject(error));

/**
 * 判断响应403
 */
instance.interceptors.response.use(response => {
    if (response.config) {
        response.config.loading.close();
        delete response.config;
    }

    let data = response.data;
    if (data.success) {
        return data.data;
    }

    if (data.errorCode === "LY0500102003") {
        // 请刷新页面
        Vue.prototype.$message.error(data.errorMessage + "请刷新页面");
    } else if (data.errorCode === "LY0500102007") {
        window.location.href = cp + '/page/error/403';
    } else {
        Vue.prototype.$message.error(data.errorMessage);
    }

    return new Promise(resolve => {
    }, reject => {
    });
}, error => {
    if (error.config) {
        error.config.loading.close();
        delete error.config;
    }

    Vue.prototype.$message.error("系统异常, " + error.errorMessage);
    return new Promise(resolve => {
    }, reject => {
    });
});

Vue.prototype.$axios = instance;
