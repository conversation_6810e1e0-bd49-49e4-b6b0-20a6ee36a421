#set($layout="layout/iframe.vm")

<div id="home" style="height: 100%">
  <el-card class="box-card" style="padding:40px">
    <h1 style="font-size: 40px">欢迎使用SOF脚手架</h1>
    <p style="height: 20px"></p>
    <h2>一、使用须知</h2>
    <ul style="font-size: 18px;text-align: left">
      <li>1、删除或者更改当前默认页面</li>
      <li>2、在Layout.js中修改您的菜单页(/config/config-tab.json)</li>
      <li>3、在/view/widget/js-loader.vm修改水印(需要接入同一权限)</li>
      <li>4、在jsloader和cssloader自带组件，可以删除</li>
      <li>5、index.vm默认页面不可以删除</li>
      <li>6、资源热部署依赖tcbase-cache,接入redis，在integration>pom.xml删除（com.ly.flight.toolkit）依赖后即可使用</li>
    </ul>
    <p style="height: 20px"></p>
    <h2>二、已引入组件</h2>
    <ul style="font-size: 18px;text-align: left">
      <li>1、vue</li>
      <li>2、jquery</li>
      <li>3、element-ui(https://element.eleme.cn/#/zh-CN)</li>
      <li>4、http-vue-loader</li>
      <li>5、g2（已注释）</li>
      <li>6、g6（已注释）</li>
      <li>7、axios</li>
    </ul>

    <p style="height: 20px"></p>
    <h2>三、集成的新组件</h2>
    <ul style="font-size: 18px;text-align: left">
      <li>1、静态资源部署器(http://wiki.17usoft.com/pages/viewpage.action?pageId=52775859)</li>
      <li>2、Object-diff 比较对象的差异(http://wiki.17usoft.com/pages/viewpage.action?pageId=61033702)</li>
      <li>3、dalgen/db/form-gen.sh 生成单表表单(./form-gen.sh dal|table [表名]语法同gen.sh)</li>
    </ul>
  </el-card>
</div>

<script type="text/javascript" src="${rc.contextPath}/js/app/home/<USER>"></script>
