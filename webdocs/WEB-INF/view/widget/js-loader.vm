<script type="text/javascript">
    var __ctx = "${rc.contextPath}";
    var csrfHeader = {'${_csrf.headerName}': '${_csrf.token}'};
            var userName = 'Admin';
</script>
<!-- jquery -->
<script type="text/javascript" src="${rc.contextPath}/js/jquery/jquery-3.6.0.min.js"></script>

<!-- vue 生产环境 -->
<script type="text/javascript" src="${rc.contextPath}/js/vue/vue.min.js"></script>
<!-- vue 开发环境 -->
<!--<script type="text/javascript" src="${rc.contextPath}/js/vue/vue.js"></script>-->

<!-- element-ui -->
<script type="text/javascript" src="${rc.contextPath}/js/element-ui/index.js"></script>
<!-- htt vue loader -->
<script type="text/javascript" src="${rc.contextPath}/js/vue/http-vue-loader/httpVueLoader.js"></script>
<!-- antv g6流程图JS -->
<!--<script type="text/javascript" src="${rc.contextPath}/js/antv/g6.js"></script>-->
<!-- antv g2图表JS -->
<!--<script type="text/javascript" src="${rc.contextPath}/js/antv/g2.min.js"></script>-->

<!-- 内部封装全局应用组件 -->
<script type="text/javascript" src="${rc.contextPath}/js/widget/vue.page.js"></script>
<script type="text/javascript" src="${rc.contextPath}/js/widget/vue.select.js"></script>
<script type="text/javascript" src="${rc.contextPath}/js/widget/vue.filter.js"></script>
<script type="text/javascript" src="${rc.contextPath}/js/widget/vue.icheck.js"></script>

<!-- 扩展方法 -->
<script type="text/javascript" src="${rc.contextPath}/js/extend/date-utils.js"></script>
<script type="text/javascript" src="${rc.contextPath}/js/extend/form-utils.js"></script>
<script type="text/javascript" src="${rc.contextPath}/js/extend/jquery-extend.js"></script>

<!-- axios -->
<script type="text/javascript" src="${rc.contextPath}/js/axios/axios.min.js"></script>
<script type="text/javascript" src="${rc.contextPath}/js/axios/axios.config.js"></script>

<!-- 富文本 -->
<script type="text/javascript" src="${rc.contextPath}/js/vue/editor/wangEditor.min.js"></script>

<!-- 水印 -->
<script type="module">
    import Watermark from '${rc.contextPath}/js/widget/watermark.js'
    // 初始化
    let watermark = new Watermark({
        //txt: `${sec.currentUserName}  ${sec.currentUserNumber}`,
        txt: "水印待修改",
        xSpace: 0,
        width: 310,
        height: 150,
    }, {
        opacity: '0.05',
        color: '#000',
        lineHeight: '26px',
        fontSize: '20px',
        fontWeight: '500',
    })
    // 添加水印
    watermark.append()
</script>

<!-- 全局js -->
<script type="text/javascript">
    // 使用JQUERY 开启注释
    // jQuery.ajaxSetup({
    //   headers: csrfHeader,
    //   error: function (err) {
    //     console.error(err);
    //   }
    // });

    // SSO error
    //$(document).ajaxError(function (event, xhr, options) {
    //  try {
    //    var returnMsg = xhr.responseText;
    //    var returnObj = JSON.parse(returnMsg);
    //    if (returnObj.code == 'LY0500102003') {
    //      toastr.error(returnObj.message + '请刷新页面', '', {timeOut: 3000, positionClass: "toast-top-center"});
    //    }
    //    if (returnObj.code == 'LY0500102007') {
    //      window.location.href = __ctx + '/page/error/403';
    //    }
    //  } catch (e) {
    //  }
    //});
</script>